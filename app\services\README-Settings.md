# Settings Service Documentation

## Overview

The Settings Service provides a robust way to manage application settings with database persistence. Since there's no dedicated settings table in the current database schema, it uses the `AdminAuditLogs` table as a storage mechanism.

## Features

- ✅ **Database Persistence**: Settings are stored in the database and persist across sessions
- ✅ **Default Values**: Comprehensive default settings for new installations
- ✅ **Validation**: Built-in validation for all setting values
- ✅ **Audit Trail**: All setting changes are logged in AdminAuditLogs
- ✅ **Reset to Defaults**: Easy way to reset all settings to default values
- ✅ **Real-time Updates**: Settings are loaded and saved in real-time
- ✅ **Error Handling**: Graceful fallback to defaults when database is unavailable

## Settings Structure

```typescript
interface AppSettings {
  siteName: string;              // Name of the application/site
  siteDescription: string;       // Description of the site
  adminEmail: string;           // Primary admin email address
  itemsPerPage: number;         // Number of items to show per page (5-100)
  enableRegistration: boolean;   // Whether user registration is enabled
  maintenanceMode: boolean;     // Whether the site is in maintenance mode
  timezone: string;             // Site timezone (e.g., 'Europe/Dublin')
  dateFormat: string;           // Date format preference (e.g., 'DD/MM/YYYY')
  timeFormat: string;           // Time format preference ('12h' or '24h')
}
```

## Usage

### Loading Settings

```typescript
import { settingsService } from '@/app/services/settingsService';
import { useSupabase } from '@/app/context/AdminAuthContext';

const supabase = useSupabase();
const settings = await settingsService.getSettings(supabase);
```

### Saving Settings

```typescript
const newSettings = {
  siteName: 'My Convention',
  siteDescription: 'An amazing event',
  // ... other settings
};

await settingsService.updateSettings(supabase, newSettings, userId);
```

### Resetting to Defaults

```typescript
const defaultSettings = await settingsService.resetToDefaults(supabase, userId);
```

### Validation

```typescript
const validation = settingsService.validateSettings(partialSettings);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}
```

## Database Storage

Settings are stored in the `AdminAuditLogs` table with:
- `action`: 'APP_SETTINGS'
- `details`: The complete settings object (JSONB)
- `user_id`: ID of the admin who made the change
- `created_at`: Timestamp of the change

This approach provides:
1. **Audit Trail**: Complete history of all setting changes
2. **User Attribution**: Know who changed what and when
3. **Rollback Capability**: Can potentially rollback to previous settings
4. **No Schema Changes**: Works with existing database structure

## Default Settings

```typescript
{
  siteName: 'The Convention Before Christmas',
  siteDescription: 'Ireland\'s Premier Christmas Convention',
  adminEmail: '<EMAIL>',
  itemsPerPage: 25,
  enableRegistration: true,
  maintenanceMode: false,
  timezone: 'Europe/Dublin',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h'
}
```

## Error Handling

The service includes comprehensive error handling:
- **Database Unavailable**: Falls back to default settings
- **Invalid Data**: Validation prevents saving invalid settings
- **Network Errors**: Graceful error messages and retry options
- **Missing Settings**: Merges with defaults to ensure all fields exist

## Security

- Only authenticated admin users can access settings
- All changes are logged with user attribution
- Input validation prevents malicious data
- Settings are stored securely in the database

## Future Enhancements

1. **Dedicated Settings Table**: Create a proper settings table for better organization
2. **Setting Categories**: Group settings into logical categories
3. **Setting Types**: Support for more complex setting types (arrays, objects)
4. **Setting Descriptions**: Add help text and descriptions for each setting
5. **Setting Dependencies**: Some settings could depend on others
6. **Environment-specific Settings**: Different settings for dev/staging/production
