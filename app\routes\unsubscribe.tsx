/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from 'react';
import { useLocation, Link } from '@remix-run/react';
import { Suspense } from 'react';

// Create a component that uses useSearchParams
function UnsubscribeContent() {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const email = searchParams.get('email');
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<'initial' | 'success' | 'error'>('initial');
  const [message, setMessage] = useState('');

  const handleUnsubscribe = async () => {
    if (!email) {
      setStatus('error');
      setMessage('No email address provided.');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/newsletter/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message);
      } else {
        setStatus('error');
        setMessage(data.message);
      }
    } catch (error: any) {
      setStatus('error');
      setMessage(error.message || 'An error occurred while processing your request.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-md w-full bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-8 text-center">
      <h1 className="text-3xl font-bold text-[var(--darkGold)] mb-6">Email Preferences</h1>

      {!email ? (
        <div className="text-white mb-6">
          <p className="mb-4">No email address provided.</p>
          <Link to="/" className="inline-block px-6 py-2 bg-[var(--darkGold)] text-white rounded-md transition-colors">
            Return to Homepage
          </Link>
        </div>
      ) : status === 'initial' ? (
        <div className="text-white">
          <p className="mb-6">You are about to unsubscribe <span className="font-semibold">{email}</span> from our newsletter and promotional emails.</p>
          <button
            onClick={handleUnsubscribe}
            disabled={loading}
            className={`px-6 py-2 bg-[var(--darkGold)] text-white rounded-md transition-colors ${loading ? 'opacity-50 cursor-not-allowed' : ''} mb-4`}
          >
            {loading ? 'Processing...' : 'Unsubscribe'}
          </button>
          <div className="mt-4">
            <Link to="/" className="text-[var(--darkGold)] hover:underline">
              Return to Homepage
            </Link>
          </div>
        </div>
      ) : (
        <div className={`${status === 'success' ? 'text-green-500' : 'text-red-500'} mb-6`}>
          <p className="mb-4">{message}</p>
          <Link to="/" className="inline-block px-6 py-2 bg-[var(--darkGold)] text-white rounded-md transition-colors">
            Return to Homepage
          </Link>
        </div>
      )}
    </div>
  );
}

// Fallback component to show while loading
function UnsubscribeFallback() {
  return (
    <div className="max-w-md w-full bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-8 text-center">
      <h1 className="text-3xl font-bold text-[var(--darkGold)] mb-6">Email Preferences</h1>
      <div className="text-white">
        <p>Loading...</p>
      </div>
    </div>
  );
}

// Main page component with Suspense boundary
export default function UnsubscribePage() {
  return (
    <div className="min-h-screen bg-black flex flex-col items-center justify-center px-4">
      <Suspense fallback={<UnsubscribeFallback />}>
        <UnsubscribeContent />
      </Suspense>
    </div>
  );
}