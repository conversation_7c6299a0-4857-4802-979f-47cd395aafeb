/* eslint-disable @typescript-eslint/no-unused-vars */
"use client"
import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Vortex } from '@/app/components/ui/vortex';

const Welcome = () => {
    const [isVisible, setIsVisible] = useState(false);
    const sectionRef = useRef<HTMLElement>(null);

    useEffect(() => {
        const observer = new IntersectionObserver(
            ([entry]) => {
                if (entry.isIntersecting) {
                    setIsVisible(true);
                    observer.unobserve(entry.target);
                }
            },
            { threshold: 0.2 }
        );

        if (sectionRef.current) {
            observer.observe(sectionRef.current);
        }

        return () => {
            if (sectionRef.current) {
                observer.unobserve(sectionRef.current);
            }
        };
    }, []);

    /* const titleVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.05
            }
        }
    }; */

    const letterVariants = {
        hidden: { opacity: 0, y: 5 },
        visible: {
            opacity: 1,
            y: 0
        }
    };

    const descriptionVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                delay: 0.5,
                staggerChildren: 0.01
            }
        }
    };

    // const welcomeText = "Upcoming Event";
    //const descriptionText = "Coming to the National Show Centre Swords, 6th & 7th of December 2025";
    const descriptionText2 = "Where Christmas Meets Fandom, Craft & Collectibles, Live Shows, Fun Fair, Toy Fair, Comic Con, Christmas Market & Special Guests. <br /> December 6th and 7th 2025 @ The National Show Center, Swords Co. Dublin";

    return (
        <section ref={sectionRef} className='text-center -mt-20 lg:-mt-24 mb-20'>
            <div className='container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4 relative'>
                <div className='text-center mb-14'>
                    <Vortex
                        backgroundColor="transparent"
                        className="flex items-center flex-col justify-center px-2 md:px-10 py-4 w-full h-full"
                        baseHue={20}
                        rangeHue={2}
                        baseSpeed={0.1}
                        rangeSpeed={1.0}
                        particleCount={800}
                        baseRadius={1.2}
                        rangeRadius={1.5}
                        rangeY={300}
                        canvasHeight={700} // Set custom height here
                    >
                        {/* <motion.h3
                            className='text-white text-xl md:text-4xl font-bold leading-8 mb-8 text-shadow'
                            variants={descriptionVariants}
                            initial="hidden"
                            animate={isVisible ? "visible" : "hidden"}
                        >
                            {descriptionText.split('').map((char, index) => (
                                <motion.span key={index} variants={letterVariants}>
                                    {char}
                                </motion.span>
                            ))}
                        </motion.h3> */}

                        <motion.h3
                            className='text-white text-xl md:text-2xl font-bold leading-8'
                            variants={descriptionVariants}
                            initial="hidden"
                            animate={isVisible ? "visible" : "hidden"}
                        >
                            {descriptionText2.split('<br />').map((char, index) => (
                                <motion.div key={index} variants={letterVariants}>
                                    {char}
                                </motion.div>
                            ))}
                        </motion.h3>
                    </Vortex>
                </div>
            </div>
        </section>
    );
};
export default Welcome;