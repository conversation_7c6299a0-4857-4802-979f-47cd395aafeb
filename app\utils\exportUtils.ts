/**
 * Export utilities for admin data
 * Handles CSV, PDF, and Excel exports
 */

// CSV Export Function
export const exportToCSV = (data: any[], filename: string, headers?: string[]) => {
  try {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    // Get headers from first object if not provided
    const csvHeaders = headers || Object.keys(data[0]);

    // Create CSV content
    const csvContent = [
      // Header row
      csvHeaders.join(','),
      // Data rows
      ...data.map(row =>
        csvHeaders.map(header => {
          const value = row[header];
          // Handle values that might contain commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        }).join(',')
      )
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    downloadFile(blob, `${filename}.csv`);

    return true;
  } catch (error) {
    console.error('CSV export failed:', error);
    throw error;
  }
};

// Excel Export Function (using proper CSV format for Excel)
export const exportToExcel = (data: any[], filename: string, headers?: string[]) => {
  try {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    // Create proper CSV content that Excel can read
    const csvHeaders = headers || Object.keys(data[0]);

    // Add BOM for proper UTF-8 encoding in Excel
    const BOM = '\uFEFF';
    const csvContent = BOM + [
      csvHeaders.join(','),
      ...data.map(row =>
        csvHeaders.map(header => {
          const value = row[header];
          // Properly escape values for CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
            return `"${value.replace(/"/g, '""')}"`;
          }
          return value || '';
        }).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    downloadFile(blob, `${filename}.xlsx`);

    return true;
  } catch (error) {
    console.error('Excel export failed:', error);
    throw error;
  }
};

// PDF Export Function (HTML-based PDF that browsers can handle)
export const exportToPDF = (data: any[], filename: string, title: string = 'Report') => {
  try {
    if (!data || data.length === 0) {
      throw new Error('No data to export');
    }

    const headers = Object.keys(data[0]);
    const currentDate = new Date().toLocaleDateString();

    // Create HTML content that looks like a PDF
    let htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }
        .meta { color: #666; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .footer { margin-top: 30px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <h1>${title}</h1>
    <div class="meta">
        <p><strong>Generated on:</strong> ${currentDate}</p>
        <p><strong>Total Records:</strong> ${data.length}</p>
    </div>

    <table>
        <thead>
            <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
            </tr>
        </thead>
        <tbody>
            ${data.map(row => `
                <tr>
                    ${headers.map(header => `<td>${row[header] || ''}</td>`).join('')}
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="footer">
        <p>End of Report</p>
    </div>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    downloadFile(blob, `${filename}.html`); // Change to .html for better compatibility

    return true;
  } catch (error) {
    console.error('PDF export failed:', error);
    throw error;
  }
};

// Helper function to download file
const downloadFile = (blob: Blob, filename: string) => {
  try {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw new Error('Failed to download file');
  }
};

// Format data for export
export const formatDataForExport = (data: any[], type: 'transactions' | 'financial' | 'users') => {
  switch (type) {
    case 'transactions':
      return data.map(item => ({
        'Transaction ID': item.id,
        'Type': item.type,
        'Amount': `€${item.amount}`,
        'Status': item.status,
        'Customer Email': item.customer_email || item.email,
        'Date': new Date(item.created_at).toLocaleDateString(),
        'Time': new Date(item.created_at).toLocaleTimeString(),
        'Description': item.description || item.title || 'N/A'
      }));

    case 'financial':
      return data.map(item => ({
        'Date': new Date(item.created_at).toLocaleDateString(),
        'Revenue Type': item.type,
        'Amount': `€${item.amount}`,
        'Source': item.source || 'Direct',
        'Status': item.status,
        'Customer': item.customer_email || item.email || 'Anonymous'
      }));

    case 'users':
      return data.map(item => ({
        'Email': item.email,
        'Role': item.role,
        'Status': item.status || 'Active',
        'Created': new Date(item.created_at).toLocaleDateString(),
        'Last Login': item.last_login ? new Date(item.last_login).toLocaleDateString() : 'Never'
      }));

    default:
      return data;
  }
};

// Generate filename with timestamp
export const generateFilename = (prefix: string, dateRange?: string) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const range = dateRange && dateRange !== 'all' ? `-${dateRange}` : '';
  return `${prefix}${range}-${timestamp}`;
};