import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { useSupabase } from '@/app/context/AdminAuthContext';
import { settingsService, type AppSettings } from '@/app/services/settingsService';
import { showLoadingToast, updateToastToSuccess, updateToastToError } from '@/app/utils/toast';
import { Save, AlertCircle, CheckCircle, RefreshCw, RotateCcw } from 'lucide-react';

const settingsSchema = z.object({
  siteName: z.string().min(3, 'Site name must be at least 3 characters'),
  siteDescription: z.string().optional(),
  adminEmail: z.string().email('Please enter a valid email address'),
  itemsPerPage: z.number().min(5).max(100),
  enableRegistration: z.boolean(),
  maintenanceMode: z.boolean(),
  timezone: z.string(),
  dateFormat: z.string(),
  timeFormat: z.string(),
});

type SettingsFormValues = z.infer<typeof settingsSchema>;

export default function AdminSettings() {
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [saveStatus, setSaveStatus] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const supabase = useSupabase();

  const { register, handleSubmit, formState: { errors }, reset, watch } = useForm<SettingsFormValues>({
    resolver: zodResolver(settingsSchema),
    defaultValues: settingsService.getDefaults(),
  });

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    if (!supabase) {
      console.error('Supabase client not available');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Get the latest settings and timestamp
      const { data: settingsData } = await supabase
        .from('AdminAuditLogs')
        .select('*')
        .eq('action', 'APP_SETTINGS')
        .order('created_at', { ascending: false })
        .limit(1);

      const settings = await settingsService.getSettings(supabase);
      reset(settings);

      // Set last updated timestamp
      if (settingsData && settingsData.length > 0) {
        setLastUpdated(settingsData[0].created_at);
      } else {
        setLastUpdated(null);
      }

    } catch (error) {
      console.error('Error loading settings:', error);
      setSaveStatus({
        type: 'error',
        message: 'Failed to load settings. Using defaults.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: SettingsFormValues) => {
    if (!supabase) {
      setSaveStatus({
        type: 'error',
        message: 'Database connection not available. Please try again.',
      });
      return;
    }

    const toastId = showLoadingToast('Saving settings...');
    setIsSaving(true);
    setSaveStatus(null);

    try {
      // Validate settings before saving
      const validation = settingsService.validateSettings(data);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Save settings to database
      await settingsService.updateSettings(supabase, data as AppSettings);

      // Update last updated timestamp
      setLastUpdated(new Date().toISOString());

      updateToastToSuccess(toastId, 'Settings saved successfully!');
      setSaveStatus({
        type: 'success',
        message: 'Settings saved successfully!',
      });

      // Clear status after 5 seconds
      setTimeout(() => setSaveStatus(null), 5000);

    } catch (error) {
      console.error('Error saving settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save settings. Please try again.';

      updateToastToError(toastId, errorMessage);
      setSaveStatus({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetToDefaults = async () => {
    if (!supabase) {
      setSaveStatus({
        type: 'error',
        message: 'Database connection not available. Please try again.',
      });
      return;
    }

    if (!confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
      return;
    }

    const toastId = showLoadingToast('Resetting to defaults...');
    setIsSaving(true);

    try {
      const defaultSettings = await settingsService.resetToDefaults(supabase);
      reset(defaultSettings);

      // Update last updated timestamp
      setLastUpdated(new Date().toISOString());

      updateToastToSuccess(toastId, 'Settings reset to defaults successfully!');
      setSaveStatus({
        type: 'success',
        message: 'Settings reset to defaults successfully!',
      });

      // Clear status after 5 seconds
      setTimeout(() => setSaveStatus(null), 5000);

    } catch (error) {
      console.error('Error resetting settings:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset settings. Please try again.';

      updateToastToError(toastId, errorMessage);
      setSaveStatus({
        type: 'error',
        message: errorMessage,
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute requiredRole={['super_admin']}>
        <AdminLayout>
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-3">
              <RefreshCw className="h-6 w-6 text-amber-500 animate-spin" />
              <span className="text-gray-600 dark:text-gray-400">Loading settings...</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRole={['super_admin']}>
      <AdminLayout>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Configure application settings and preferences
              </p>
              {lastUpdated && (
                <p className="mt-1 text-xs text-gray-400 dark:text-gray-500">
                  Last updated: {new Date(lastUpdated).toLocaleString()}
                </p>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={loadSettings}
                disabled={isSaving}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload
              </button>
              <button
                type="button"
                onClick={handleResetToDefaults}
                disabled={isSaving}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset to Defaults
              </button>
            </div>
          </div>

          {saveStatus && (
            <div
              className={`p-4 rounded-md ${
                saveStatus.type === 'success'
                  ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                  : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200'
              }`}
            >
              <div className="flex">
                <div className="flex-shrink-0">
                  {saveStatus.type === 'success' ? (
                    <CheckCircle className="h-5 w-5" aria-hidden="true" />
                  ) : (
                    <AlertCircle className="h-5 w-5" aria-hidden="true" />
                  )}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{saveStatus.message}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 divide-y divide-gray-200 dark:divide-gray-700">
            <div className="space-y-8 divide-y divide-gray-200 dark:divide-gray-700">
              <div>
                <div className="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-4">
                    <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Site Name
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        id="siteName"
                        {...register('siteName')}
                        className="flex items-center w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-red-500 focus:ring-red-500 dark:bg-gray-700 dark:text-white sm:text-sm px-3 h-10"
                      />
                      {errors.siteName && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.siteName.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Site Description
                    </label>
                    <div className="mt-1">
                      <textarea
                        id="siteDescription"
                        rows={3}
                        {...register('siteDescription')}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-red-500 focus:ring-red-500 dark:bg-gray-700 dark:text-white sm:text-sm px-3 pt-2 resize-none"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-4">
                    <label htmlFor="adminEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Admin Email
                    </label>
                    <div className="mt-1">
                      <input
                        type="email"
                        id="adminEmail"
                        {...register('adminEmail')}
                        className="flex items-center w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-red-500 focus:ring-red-500 dark:bg-gray-700 dark:text-white sm:text-sm px-3 h-10"
                      />
                      {errors.adminEmail && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.adminEmail.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="sm:col-span-2">
                    <label htmlFor="itemsPerPage" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Items per page
                    </label>
                    <div className="mt-1">
                      <select
                        id="itemsPerPage"
                        {...register('itemsPerPage', { valueAsNumber: true })}
                        className="flex items-center w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-red-500 focus:ring-red-500 dark:bg-gray-700 dark:text-white sm:text-sm px-3 h-10"
                      >
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <fieldset>
                      <legend className="text-sm font-medium text-gray-700 dark:text-gray-300">Features</legend>
                      <div className="mt-4 space-y-4">
                        <div className="flex items-start">
                          <div className="flex h-5 items-center">
                            <input
                              id="enableRegistration"
                              type="checkbox"
                              {...register('enableRegistration')}
                              className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700"
                            />
                          </div>
                          <div className="ml-3 text-sm">
                            <label htmlFor="enableRegistration" className="font-medium text-gray-700 dark:text-gray-300">
                              Enable User Registration
                            </label>
                            <p className="text-gray-500 dark:text-gray-400">Allow new users to create accounts</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <div className="flex h-5 items-center">
                            <input
                              id="maintenanceMode"
                              type="checkbox"
                              {...register('maintenanceMode')}
                              className="h-4 w-4 rounded border-gray-300 text-red-600 focus:ring-red-500 dark:border-gray-600 dark:bg-gray-700"
                            />
                          </div>
                          <div className="ml-3 text-sm">
                            <label htmlFor="maintenanceMode" className="font-medium text-gray-700 dark:text-gray-300">
                              Maintenance Mode
                            </label>
                            <p className="text-gray-500 dark:text-gray-400">Only administrators can access the site</p>
                          </div>
                        </div>
                      </div>
                    </fieldset>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-5">
              <div className="flex justify-end">
                <button
                  type="button"
                  className="rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-4 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSaving}
                  className="ml-3 inline-flex justify-center rounded-md border border-transparent bg-red-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className={`-ml-1 mr-2 h-4 w-4 ${isSaving ? 'animate-spin' : ''}`} />
                  {isSaving ? 'Saving...' : 'Save'}
                </button>
              </div>
            </div>
          </form>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};