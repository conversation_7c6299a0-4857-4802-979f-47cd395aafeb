import { json, type ActionFunctionArgs } from "@remix-run/node";
import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import { render } from '@react-email/render';
import { env } from '@/app/utils/env.server';
import { UserOrderConfirmationEmail } from '@/app/components/Email/UserOrderConfirmationEmail';
import { AdminOrderNotificationEmail } from '@/app/components/Email/AdminOrderNotificationEmail';

const supabase = createClient(
  env.supabase.url,
  env.supabase.key
);

const resend = new Resend(env.resendApiKey);

export async function action({ request }: ActionFunctionArgs) {
  try {
    const { orderDetails } = await request.json();

    if (!orderDetails) {
      throw new Error('Missing order details');
    }

    // Use the orderId from payment intent response
    const orderId = orderDetails.orderId;
    const paymentIntentId = orderDetails.paymentIntentId;

    // Store order in database with initial status
    const { error: orderError } = await supabase
      .from('Orders')
      .insert({
        id: orderId,
        email: orderDetails.email,
        name: orderDetails.name,
        total: orderDetails.total,
        payment_intent_id: paymentIntentId,
        status: 'paid' // Set to paid since this is called after successful payment
      });

    if (orderError) throw orderError;

    // Add customer to newsletter subscribers if not already subscribed
    const { data: existingSubscriber } = await supabase
      .from('NewsletterSubscribers')
      .select('email')
      .eq('email', orderDetails.email.toLowerCase());

    if (existingSubscriber?.length === 0) {
      // Add new subscriber
      await supabase
        .from('NewsletterSubscribers')
        .insert([
          {
            email: orderDetails.email.toLowerCase(),
            subscribed_at: new Date().toISOString(),
            is_subscribed: true
          },
        ]);
    }

    // After storing order in database, check for and record charity donations
    const charityItems = orderDetails.tickets.filter((item: any) => item.ticketType.id === 'charity-donation');

    if (charityItems.length > 0) {
      const donationAmount = charityItems.reduce((sum: any, item: any) => sum + (item.quantity * item.ticketType.price), 0);

      // Create metadata object for the donation
      const metadata = {
        donor_name: orderDetails.name,
        donor_email: orderDetails.email,
        donation_date: new Date().toISOString(),
        multiplier_info: orderDetails.charityMultiplier
          ? {
            multiplier_used: orderDetails.charityMultiplier.multiplier,
            original_amount: orderDetails.charityMultiplier.originalAmount,
            multiplied_amount: orderDetails.charityMultiplier.appliedAmount,
            applied_at: new Date().toISOString()
          }
          : null
      };

      // Record the donation with multiplier information, metadata, and status
      const { data: donationData, error: donationError } = await supabase
        .from('CharityDonations')
        .insert({
          order_id: orderId,
          amount: donationAmount,
          multiplier: orderDetails.charityMultiplier?.multiplier || 1,
          original_amount: orderDetails.charityMultiplier?.originalAmount || donationAmount,
          metadata,
          status: 'completed', // Set to completed since this is called after successful payment
          payment_intent_id: paymentIntentId
        })
        .select();

      if (donationError) {
        console.error('Donation Error:', donationError);
        throw donationError;
      }

      console.log('Donation recorded:', donationData);
    }

    // Store order items and update ticket sales
    for (const item of orderDetails.tickets) {
      // Check if ticket exists
      const { data: existingTicket } = await supabase
        .from('Tickets')
        .select('id')
        .eq('id', item.ticketType.id);

      // If ticket doesn't exist, create it
      if (!existingTicket?.length) {
        const { error: ticketError } = await supabase
          .from('Tickets')
          .insert({
            id: item.ticketType.id,
            name: item.ticketType.name,
            description: item.ticketType.description,
            price: item.ticketType.price,
            max_quantity: item.ticketType.maxQuantity,
            type: item.ticketType.type,
            original_id: item.ticketType.originalId || null
          });

        if (ticketError) throw ticketError;
      }

      // Create order item record with selected day for 1-day tickets
      const { error: orderItemError } = await supabase
        .from('OrderItems')
        .insert({
          ticket_id: item.ticketType.id,
          quantity: item.quantity,
          price: item.ticketType.price,
          order_id: orderId,
          selected_day: item.selectedDay || item.ticketType.selectedDay || null,
          original_ticket_id: item.ticketType.originalId || null
        });

      if (orderItemError) throw orderItemError;

      // Record the ticket sale with day information for 1-day tickets
      const { error: saleError } = await supabase
        .from('TicketSales')
        .insert({
          ticket_id: item.ticketType.id,
          quantity: item.quantity,
          selected_day: item.selectedDay || item.ticketType.selectedDay || null
        });

      if (saleError) throw saleError;

      // If this is a Santa booking, handle the Santa slot
      if (item.santaSlot?.id) {
        // First check if the slot is still available
        const { data: slot, error: slotCheckError } = await supabase
          .from('SantaTimeSlots')
          .select('*')
          .eq('id', item.santaSlot.id);

        if (slotCheckError) throw slotCheckError;

        if (!slot.length || slot[0].booked >= slot[0].capacity) {
          throw new Error('Santa slot no longer available');
        }

        // Update the slot's booking count
        const { error: updateSlotError } = await supabase
          .from('SantaTimeSlots')
          .update({ booked: slot[0].booked + 1 })
          .eq('id', item.santaSlot.id);

        if (updateSlotError) throw updateSlotError;

        // Create the Santa booking record with all necessary information
        const { error: santaBookingError } = await supabase
          .from('SantaBookings')
          .insert({
            slot_id: item.santaSlot.id,
            order_id: orderId,
            customer_email: orderDetails.email,
            customer_name: orderDetails.name,
            num_children: item.santaSlot.numChildren || 1,
            day: item.santaSlot.day,
            time_range: item.santaSlot.timeRange,
            ticket_id: item.ticketType.id
          });

        if (santaBookingError) throw santaBookingError;
      }
    }

    const qrCodeInfoUrl = `https://dev.hiddendublintours.com/qrcode/api/scan.php?payment_intent_id=${paymentIntentId}&email=${orderDetails.email}`;

    // Generate QR code using QuickChart.io
    const qrCodeUrl = `https://quickchart.io/qr?text=${encodeURIComponent(qrCodeInfoUrl)}&size=200&margin=2&ecLevel=M`;
    const qrCodeResponse = await fetch(qrCodeUrl);
    if (!qrCodeResponse.ok) {
      throw new Error('Failed to generate QR code from QuickChart.io');
    }
    const qrCodeBuffer = Buffer.from(await qrCodeResponse.arrayBuffer());
    const qrCodeBase64 = qrCodeBuffer.toString('base64');

    // Render customer email with QR code
    const customerEmailHtml = await render(
      UserOrderConfirmationEmail({
        name: orderDetails.name,
        email: orderDetails.email,
        phoneNumber: orderDetails.phoneNumber,
        tickets: orderDetails.tickets,
        total: orderDetails.total,
        orderId: orderId,
        qrCodeDataUrl: `data:image/png;base64,${qrCodeBase64}`,
        charityMultiplier: orderDetails.charityMultiplier || undefined
      })
    ) as string;

    // Render admin email
    const adminEmailHtml = await render(
      AdminOrderNotificationEmail({
        name: orderDetails.name,
        email: orderDetails.email,
        phoneNumber: orderDetails.phoneNumber,
        tickets: orderDetails.tickets.map((item: any) => ({
          ...item,
          ticketType: {
            ...item.ticketType,
            originalId: !!item.ticketType.originalId
          }
        })),
        total: orderDetails.total,
        orderId: orderId,
        hasDonation: charityItems.length > 0,
        donationAmount: charityItems.reduce((sum: any, item: any) => sum + (item.quantity * item.ticketType.price), 0),
        charityMultiplier: orderDetails.charityMultiplier || undefined
      })
    ) as string;

    // Send customer confirmation email
    try {
      const { data: customerEmailData, error: customerEmailError } = await resend.emails.send({
        from: `"The Convention Before Christmas" <${env.emailFrom}>`,
        to: orderDetails.email,
        subject: "Ticket Booking Confirmation - The Convention Before Christmas",
        html: customerEmailHtml
      });

      if (customerEmailError) {
        throw customerEmailError;
      }

      console.log("Customer email sent successfully:", {
        to: orderDetails.email,
        messageId: customerEmailData?.id
      });
    } catch (emailError) {
      console.error("Error sending customer email:", {
        error: emailError,
        details: JSON.stringify(emailError, null, 2)
      });
      throw emailError;
    }

    // Send admin notification email
    try {
      const { data: adminEmailData, error: adminEmailError } = await resend.emails.send({
        from: `"The Convention Before Christmas" <${env.emailFrom}>`,
        to: env.admin.email,
        subject: `New Ticket Order: ${orderId}`,
        html: adminEmailHtml,
      });

      if (adminEmailError) {
        throw adminEmailError;
      }

      console.log("Admin email sent successfully:", {
        to: env.admin.email,
        messageId: adminEmailData?.id
      });
    } catch (adminEmailError) {
      console.error("Error sending admin email:", {
        error: adminEmailError,
        details: JSON.stringify(adminEmailError, null, 2)
      });
      // Don't throw here to ensure customer gets success response
    }

    return json({
      success: true,
      message: 'Confirmation email sent',
      orderId
    });
  } catch (error) {
    console.error('Error processing order:', error);
    return json(
      {
        error: 'Failed to process order',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
};