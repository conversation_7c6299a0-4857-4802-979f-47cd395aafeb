import type { MetaFunction, LinksFunction } from "@remix-run/node";
import { generateMetaDescriptors } from "../metadata";
import HomeClientContent from "@/app/components/home-client-content";
import { Suspense } from "react";

export const meta: MetaFunction = () => {
  return generateMetaDescriptors();
};

export const links: LinksFunction = () => {
    return [{ rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap' }];
};

export default function Index() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HomeClientContent />
    </Suspense>
  );
}