"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import styled from 'styled-components';

interface CountdownProps {
  targetDate: Date;
  className?: string;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const CountdownWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;

  @media (min-width: 600px) {
    grid-template-columns: repeat(4, 1fr);
  }
`;

const NumberTransition = ({ number, label }: { number: number; label: string }) => {
  const [prevNumber, setPrevNumber] = useState(number);

  useEffect(() => {
    if (prevNumber !== number) {
      setPrevNumber(number);
    }
  }, [number, prevNumber]);

  return (
    <div className="text-center px-4">
      <div className="number-container">
        <AnimatePresence mode="wait">
          <motion.div
            key={number}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="text-4xl md:text-6xl font-bold text-[var(--darkGold)] event-countdown-number"
            data-dis-type="simultaneous"
          >
            {number.toString().padStart(2, '0')}
          </motion.div>
        </AnimatePresence>
      </div>
      <div className="text-sm mt-2 text-[var(--darkGold)]">{label}</div>
    </div>
  );
};

const Countdown: React.FC<CountdownProps> = ({ targetDate, className }) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const calculateTimeLeft = () => {
      // Convert target date string to compatible format for Safari
      const targetDateTime = new Date(targetDate).getTime();
      const now = new Date().getTime();
      const difference = targetDateTime - now;

      let newTimeLeft = {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0
      };

      if (difference > 0) {
        newTimeLeft = {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
          minutes: Math.floor((difference / 1000 / 60) % 60),
          seconds: Math.floor((difference / 1000) % 60)
        };
      }

      return newTimeLeft;
    };

    // Initial calculation
    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    // Cleanup
    return () => clearInterval(timer);
  }, [targetDate]);

  return (
    <CountdownWrapper className={className || ''}>
      <NumberTransition number={timeLeft.days} label="Days" />
      <NumberTransition number={timeLeft.hours} label="Hours" />
      <NumberTransition number={timeLeft.minutes} label="Minutes" />
      <NumberTransition number={timeLeft.seconds} label="Seconds" />
    </CountdownWrapper>
  );
};

export default Countdown;
