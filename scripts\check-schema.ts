/**
 * Schema Check Script
 *
 * This script checks the actual database schema to understand table structures
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://trbcgeffweprfnovhggm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYmNnZWZmd2VwcmZub3ZoZ2dtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mjc1NjI0NSwiZXhwIjoyMDU4MzMyMjQ1fQ.ZkQQbOrwUN7Eqert9omwEPwLSj6itNr6vZB4dv1tXOI';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTableSchema(tableName: string) {
  console.log(`\n🔍 Checking ${tableName} table schema...`);

  try {
    // Try to get a sample record to understand the structure
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      console.error(`❌ Error querying ${tableName}:`, error.message);
      return;
    }

    if (data && data.length > 0) {
      console.log(`✅ Sample record from ${tableName}:`);
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      console.log(`📝 ${tableName} table exists but is empty`);

      // Try to insert a minimal record to see what fields are required
      console.log(`🧪 Testing minimal insert for ${tableName}...`);

      let testRecord: any = {};

      if (tableName === 'VendorStands') {
        testRecord = {
          business_name: 'Test Business',
          contact_name: 'Test Contact',
          email: '<EMAIL>',
          stand_number: 999
        };
      } else if (tableName === 'CharityDonations') {
        testRecord = {
          amount: 10.00
        };
      }

      const { data: insertData, error: insertError } = await supabase
        .from(tableName)
        .insert([testRecord])
        .select()
        .single();

      if (insertError) {
        console.log(`❌ Insert failed for ${tableName}:`, insertError.message);
        console.log(`💡 This tells us about required fields or constraints`);
      } else {
        console.log(`✅ Successfully inserted test record:`, insertData);

        // Clean up
        await supabase.from(tableName).delete().eq('id', insertData.id);
        console.log(`🧹 Cleaned up test record`);
      }
    }
  } catch (error) {
    console.error(`❌ Unexpected error with ${tableName}:`, error);
  }
}

async function main() {
  console.log('🚀 Starting Database Schema Check\n');

  const tables = ['Orders', 'VendorStands', 'CharityDonations', 'EventApplications', 'NewsletterSignups'];

  for (const table of tables) {
    await checkTableSchema(table);
  }

  console.log('\n✅ Schema check complete!');
}

main().catch(console.error);
