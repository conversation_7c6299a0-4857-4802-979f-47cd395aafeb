import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { ConnectionStatus, RealtimeNotification } from '@/app/utils/realtimeManager';

export interface SubscriptionStatus {
  table: string;
  status: 'subscribing' | 'subscribed' | 'error' | 'unsubscribed';
  lastUpdate: Date | null;
  errorMessage?: string;
}

export interface RealtimeError {
  id: string;
  message: string;
  timestamp: string; // ISO string instead of Date
  context?: string;
  resolved: boolean;
}

export interface RealtimeState {
  connectionStatus: ConnectionStatus;
  subscriptions: Record<string, SubscriptionStatus>;
  notifications: RealtimeNotification[];
  lastUpdate: Record<string, string>; // ISO strings instead of Date
  isEnabled: boolean;
  maxNotifications: number;
  errors: RealtimeError[];
  reconnectAttempts: number;
  lastConnectedAt: string | null; // ISO string instead of Date
  lastErrorAt: string | null; // ISO string instead of Date
}

export const initialState: RealtimeState = {
  connectionStatus: 'disconnected',
  subscriptions: {},
  notifications: [],
  lastUpdate: {},
  isEnabled: true,
  maxNotifications: 100,
  errors: [],
  reconnectAttempts: 0,
  lastConnectedAt: null,
  lastErrorAt: null,
};

export interface RealtimeInsertPayload {
  table: string;
  record: any;
}

export interface RealtimeUpdatePayload {
  table: string;
  newRecord: any;
  oldRecord: any;
}

export interface RealtimeDeletePayload {
  table: string;
  record: any;
}

const realtimeSlice = createSlice({
  name: 'realtime',
  initialState,
  reducers: {
    // Connection management
    updateConnectionStatus: (state, action: PayloadAction<ConnectionStatus>) => {
      state.connectionStatus = action.payload;
    },

    // Subscription management
    updateSubscriptionStatus: (
      state,
      action: PayloadAction<{ table: string; status: SubscriptionStatus['status']; errorMessage?: string }>
    ) => {
      const { table, status, errorMessage } = action.payload;
      state.subscriptions[table] = {
        table,
        status,
        lastUpdate: new Date(),
        errorMessage,
      };
    },

    // Real-time data handlers
    handleRealtimeInsert: (state, action: PayloadAction<RealtimeInsertPayload>) => {
      const { table } = action.payload;
      state.lastUpdate[table] = new Date().toISOString();

      // Update subscription status
      if (state.subscriptions[table]) {
        state.subscriptions[table].lastUpdate = new Date();
      }
    },

    handleRealtimeUpdate: (state, action: PayloadAction<RealtimeUpdatePayload>) => {
      const { table } = action.payload;
      state.lastUpdate[table] = new Date().toISOString();

      // Update subscription status
      if (state.subscriptions[table]) {
        state.subscriptions[table].lastUpdate = new Date();
      }
    },

    handleRealtimeDelete: (state, action: PayloadAction<RealtimeDeletePayload>) => {
      const { table } = action.payload;
      state.lastUpdate[table] = new Date().toISOString();

      // Update subscription status
      if (state.subscriptions[table]) {
        state.subscriptions[table].lastUpdate = new Date();
      }
    },

    // Notification management
    addNotification: (state, action: PayloadAction<RealtimeNotification>) => {
      state.notifications.unshift(action.payload);

      // Limit notifications to maxNotifications
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },

    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
    },

    clearNotifications: (state) => {
      state.notifications = [];
    },

    clearOldNotifications: (state, action: PayloadAction<number>) => {
      const maxAge = action.payload; // milliseconds
      const cutoffTime = new Date(Date.now() - maxAge);

      state.notifications = state.notifications.filter(
        notification => new Date(notification.timestamp) > cutoffTime
      );
    },

    // Settings
    setRealtimeEnabled: (state, action: PayloadAction<boolean>) => {
      state.isEnabled = action.payload;
    },

    setMaxNotifications: (state, action: PayloadAction<number>) => {
      state.maxNotifications = action.payload;

      // Trim existing notifications if needed
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },

    // Error handling
    addError: (state, action: PayloadAction<Omit<RealtimeError, 'id' | 'timestamp' | 'resolved'>>) => {
      const error: RealtimeError = {
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        resolved: false,
        ...action.payload
      };

      state.errors.unshift(error);
      state.lastErrorAt = new Date().toISOString();

      // Keep only last 50 errors
      if (state.errors.length > 50) {
        state.errors = state.errors.slice(0, 50);
      }
    },

    resolveError: (state, action: PayloadAction<string>) => {
      const error = state.errors.find(e => e.id === action.payload);
      if (error) {
        error.resolved = true;
      }
    },

    clearErrors: (state) => {
      state.errors = [];
    },

    clearResolvedErrors: (state) => {
      state.errors = state.errors.filter(error => !error.resolved);
    },

    incrementReconnectAttempts: (state) => {
      state.reconnectAttempts += 1;
    },

    resetReconnectAttempts: (state) => {
      state.reconnectAttempts = 0;
    },

    setLastConnectedAt: (state, action: PayloadAction<string>) => {
      state.lastConnectedAt = action.payload;
      state.reconnectAttempts = 0; // Reset on successful connection
    },

    // Reset state
    resetRealtimeState: () => initialState,
  },
});

export const {
  updateConnectionStatus,
  updateSubscriptionStatus,
  handleRealtimeInsert,
  handleRealtimeUpdate,
  handleRealtimeDelete,
  addNotification,
  removeNotification,
  clearNotifications,
  clearOldNotifications,
  setRealtimeEnabled,
  setMaxNotifications,
  addError,
  resolveError,
  clearErrors,
  clearResolvedErrors,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  setLastConnectedAt,
  resetRealtimeState,
} = realtimeSlice.actions;

export default realtimeSlice.reducer;

// Selectors
export const selectConnectionStatus = (state: { realtime: RealtimeState }) =>
  state.realtime.connectionStatus;

export const selectSubscriptions = (state: { realtime: RealtimeState }) =>
  state.realtime.subscriptions;

export const selectNotifications = (state: { realtime: RealtimeState }) =>
  state.realtime.notifications;

export const selectRecentNotifications = (state: { realtime: RealtimeState }, limit: number = 10) =>
  state.realtime.notifications.slice(0, limit);

export const selectNotificationsByTable = (state: { realtime: RealtimeState }, table: string) =>
  state.realtime.notifications.filter(notification => notification.table === table);

export const selectLastUpdate = (state: { realtime: RealtimeState }, table?: string) => {
  if (table) {
    return state.realtime.lastUpdate[table];
  }
  return state.realtime.lastUpdate;
};

export const selectIsRealtimeEnabled = (state: { realtime: RealtimeState }) =>
  state.realtime.isEnabled;

export const selectIsConnected = (state: { realtime: RealtimeState }) =>
  state.realtime.connectionStatus === 'connected';

export const selectConnectionStatusWithDetails = (state: { realtime: RealtimeState }) => ({
  status: state.realtime.connectionStatus,
  isConnected: state.realtime.connectionStatus === 'connected',
  isConnecting: state.realtime.connectionStatus === 'connecting',
  hasError: state.realtime.connectionStatus === 'error',
  subscriptionCount: Object.keys(state.realtime.subscriptions).length,
  activeSubscriptions: Object.values(state.realtime.subscriptions).filter(
    sub => sub.status === 'subscribed'
  ).length,
});

// Thunk actions for async operations
export const startRealtimeMonitoring = () => async (dispatch: any) => {
  try {
    const { realtimeManager } = await import('@/app/utils/realtimeManager');

    await realtimeManager.initialize();

    await realtimeManager.start();
  } catch (error) {
    console.error('Failed to start realtime monitoring:', error);
    dispatch(updateConnectionStatus('error'));
  }
};

export const stopRealtimeMonitoring = () => async () => {
  try {
    const { realtimeManager } = await import('@/app/utils/realtimeManager');
    await realtimeManager.stop();
  } catch (error) {
    console.error('Failed to stop realtime monitoring:', error);
  }
};

export const reconnectRealtime = () => async (dispatch: any) => {
  try {
    const { realtimeManager } = await import('@/app/utils/realtimeManager');
    await realtimeManager.reconnect();
  } catch (error) {
    console.error('Failed to reconnect realtime:', error);
    dispatch(updateConnectionStatus('error'));
  }
};

// Error selectors
export const selectErrors = (state: { realtime: RealtimeState }) =>
  state.realtime.errors;

export const selectUnresolvedErrors = (state: { realtime: RealtimeState }) =>
  state.realtime.errors.filter(error => !error.resolved);

export const selectRecentErrors = (state: { realtime: RealtimeState }, limit: number = 5) =>
  state.realtime.errors.slice(0, limit);

export const selectReconnectAttempts = (state: { realtime: RealtimeState }) =>
  state.realtime.reconnectAttempts;

export const selectLastConnectedAt = (state: { realtime: RealtimeState }) =>
  state.realtime.lastConnectedAt ? new Date(state.realtime.lastConnectedAt) : null;

export const selectLastErrorAt = (state: { realtime: RealtimeState }) =>
  state.realtime.lastErrorAt ? new Date(state.realtime.lastErrorAt) : null;

export const selectConnectionHealth = (state: { realtime: RealtimeState }) => {
  const { connectionStatus, reconnectAttempts, lastConnectedAt, lastErrorAt } = state.realtime;

  const lastConnectedDate = lastConnectedAt ? new Date(lastConnectedAt) : null;
  const lastErrorDate = lastErrorAt ? new Date(lastErrorAt) : null;

  return {
    status: connectionStatus,
    isHealthy: connectionStatus === 'connected' && reconnectAttempts === 0,
    reconnectAttempts,
    lastConnectedAt: lastConnectedDate,
    lastErrorAt: lastErrorDate,
    hasRecentErrors: lastErrorDate && Date.now() - lastErrorDate.getTime() < 300000, // 5 minutes
  };
};