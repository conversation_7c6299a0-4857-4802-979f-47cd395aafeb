'use client';

import { useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { useNavigate } from '@remix-run/react';

export function TicketConfirmationStep() {
  const navigate = useNavigate();
  const orderDetails = useSelector((state: RootState) => state.booking.orderDetails);
  const cart = useSelector((state: RootState) => state.booking.cart);

  const handleReturnHome = () => {
    navigate('/');
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-8 w-8 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Booking Confirmed!</h2>
        <p className="text-gray-300">
          Thank you for your purchase. A confirmation email has been sent to {orderDetails.email}
        </p>
      </div>

      <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Order Details</h3>
        <div className="space-y-4">
          <div>
            <p className="text-gray-400">Order ID</p>
            <p className="text-white">{orderDetails.orderId}</p>
          </div>
          <div>
            <p className="text-gray-400">Name</p>
            <p className="text-white">{orderDetails.name}</p>
          </div>
          <div>
            <p className="text-gray-400">Email</p>
            <p className="text-white">{orderDetails.email}</p>
          </div>
          <div>
            <p className="text-gray-400">Phone</p>
            <p className="text-white">{orderDetails.phoneNumber}</p>
          </div>
        </div>
      </div>

      <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6">
        <h3 className="text-xl font-semibold text-white mb-4">Tickets</h3>
        <div className="space-y-4">
          {cart.map((item) => (
            <div key={item.ticketType.id} className="flex justify-between items-center">
              <div>
                <p className="text-white">{item.ticketType.name}</p>
                {item.selectedDay && (
                  <p className="text-gray-400 text-sm">Day: {item.selectedDay}</p>
                )}
                {item.santaSlot && (
                  <p className="text-gray-400 text-sm">
                    Santa Slot: {item.santaSlot.day} at {item.santaSlot.timeRange}
                  </p>
                )}
              </div>
              <div className="text-right">
                <p className="text-white">x{item.quantity}</p>
                <p className="text-[var(--darkGold)]">
                  €{(item.ticketType.price * item.quantity).toFixed(2)}
                </p>
              </div>
            </div>
          ))}
          <div className="border-t border-gray-700 pt-4 mt-4">
            <div className="flex justify-between items-center">
              <p className="text-white font-semibold">Total</p>
              <p className="text-[var(--darkGold)] font-semibold">
                €{orderDetails.total.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      <button
        onClick={handleReturnHome}
        className="w-full py-2 px-4 rounded-lg bg-[var(--darkGold)] text-white font-semibold hover:bg-[#95784a] transition-colors"
      >
        Return to Home
      </button>
    </div>
  );
};