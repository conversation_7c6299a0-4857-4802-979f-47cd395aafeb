// This file contains type declarations for environment variables
// that are exposed to the client-side code through window.ENV

type WindowWithEnv = Window & {
  ENV: {
    SUPABASE_URL: string;
    SUPABASE_KEY: string;
    NODE_ENV: 'development' | 'production' | 'test';
    STRIPE_PUBLISHABLE_KEY: string;
    ADMIN_EMAIL: string;
    ADMIN_PASSWORD: string;
    ADMIN_NAME: string;
    ADMIN_ROLE: string;
    SESSION_SECRET: string;
  };
};

declare const window: WindowWithEnv;
