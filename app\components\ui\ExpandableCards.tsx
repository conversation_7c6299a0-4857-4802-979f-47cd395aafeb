import React, { useEffect, useId, useRef, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { useOutsideClick } from "@/hooks/useOutsideClick";
import { Divide } from "lucide-react";

export function ExpandableCards({ onActiveChange }: { onActiveChange?: (isActive: boolean) => void }) {
  const [active, setActive] = useState<(typeof cards)[number] | boolean | null>(
    null
  );
  const ref = useRef<HTMLDivElement>(null);
  const id = useId();

  useEffect(() => {
    function onKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setActive(false);
      }
    }

    if (active && typeof active === "object") {
      document.body.style.overflow = "hidden";
      // Notify parent component that modal is active
      onActiveChange?.(true);
    } else {
      document.body.style.overflow = "auto";
      // Notify parent component that modal is closed
      onActiveChange?.(false);
    }

    window.addEventListener("keydown", onKeyDown);
    return () => window.removeEventListener("keydown", onKeyDown);
  }, [active, onActiveChange]);

  const sectionRef = useRef<HTMLElement>(null);
  useOutsideClick(ref as React.RefObject<HTMLDivElement>, () => setActive(null));
  return (
    <section ref={sectionRef} className="py-11 backdrop-blur-md relative">
      <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4 uppercase">Special Guests</h2>
          <div className="text-gray-300">Attending The Convention Before Christmas</div>
        </div>

        <AnimatePresence mode="wait">
          {active && typeof active === "object" && (
            <motion.div
              //initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/80 h-full w-full z-50"
            />
          )}
        </AnimatePresence>

        {/* <AnimatePresence mode="wait">
          {active && typeof active === "object" ? (
            <div className="fixed inset-0 grid place-items-center z-[100] overflow-y-auto h-full py-8">
              <motion.button
                key={`button-${active.title}-${id}`}
                layout
                initial={{
                  opacity: 0,
                }}
                animate={{
                  opacity: 1,
                }}
                exit={{
                  opacity: 0,
                  transition: {
                    duration: 0.05,
                  },
                }}
                className="flex absolute top-2 right-2 lg:hidden items-center justify-center bg-white rounded-full h-6 w-6"
                onClick={() => setActive(null)}
              >
                <CloseIcon />
              </motion.button>

              <motion.div
                layoutId={`card-${active.title}-${id}`}
                ref={ref}
                className="w-full max-w-[500px] md:h-[80vh] flex flex-col bg-neutral-900 sm:rounded-3xl overflow-hidden"
              >
                <motion.div layoutId={`image-${active.title}-${id}`}>
                  <Image
                    priority
                    width={200}
                    height={200}
                    src={active.src}
                    alt={active.title}
                    className="w-full h-60 lg:h-60 sm:rounded-tr-lg sm:rounded-tl-lg object-cover object-top"
                  />
                </motion.div>

                <div className="flex-1 flex flex-col overflow-hidden">
                  <div className="flex justify-between items-start p-4">
                    <div className="">
                      <motion.h3
                        layoutId={`title-${active.title}-${id}`}
                        className="font-bold text-neutral-200"
                      >
                        {active.title}
                      </motion.h3>
                      <motion.p
                        layoutId={`description-${active.description}-${id}`}
                        className="text-neutral-400"
                      >
                        {active.description}
                      </motion.p>
                    </div>

                    <motion.a
                      layoutId={`button-${active.title}-${id}`}
                      href={active.ctaLink}
                      target="_blank"
                      className="px-4 py-3 text-sm rounded-full font-bold bg-green-500 text-white"
                    >
                      {active.ctaText}
                    </motion.a>
                  </div>
                  <div className="pt-4 relative px-4 pb-6 flex-1 overflow-y-auto">
                    <motion.div
                      layout
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-xs md:text-sm lg:text-base flex flex-col items-start gap-4 text-neutral-400"
                    >
                      {typeof active.content === "function"
                        ? active.content()
                        : active.content}
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            </div>
          ) : null}
        </AnimatePresence> */}
        <motion.ul
          className="mx-auto w-full gap-4 grid grid-cols-1"
          layout
        >
          {cards.map((card, index) => (
            <motion.div
              layoutId={`card-${card.key}-${id}`}
              key={`card-${card.key}-${id}-${index}`}
              className="p-4 flex flex-col md:flex-row justify-between items-center rounded-xl transition-colors border border-[var(--darkGold)] sm:text-center relative z-10"
              initial={{
                opacity: 1,
                x: 0
              }}
              animate={{
                opacity: 1
              }}
              transition={{
                type: "spring",
                bounce: 0.3,
                duration: 0.8,
                delay: index * 0.2
              }}
            >
              <div className="flex gap-4 flex-col md:flex-row w-full justify-center lg:justify-between items-center">
                <motion.div
                  layoutId={`image-${card.key}-${id}`}
                  className="text-center relative z-10 w-[250px] h-[250px]"
                >
                  <img
                    width={200}
                    height={200}
                    src={card.src}
                    alt={card.title}
                    className="w-full h-full rounded-lg object-contain inline-block"
                  />
                </motion.div>
                <div className="relative z-10 max-w-[95%] lg:max-w-[80%] w-full">
                  <motion.h3
                    layoutId={`title-${card.key}-${id}`}
                    className="font-medium text-neutral-200 text-center md:text-left sm:text-center mb-4 text-2xl"
                  >
                    {card.title}
                  </motion.h3>
                  <motion.div
                    layoutId={`description-${card.description}-${id}`}
                    className="text-neutral-200 text-center md:text-left"
                  >
                    {typeof card.description === 'function' ? card.description() : card.description}
                  </motion.div>
                </div>
              </div>
              {/* Hide the More Info button */}
              {/* <motion.button
                layoutId={`button-${card.title}-${id}`}
                className="px-4 py-2 text-sm rounded-full font-bold bg-gray-100 hover:bg-green-500 hover:text-white text-black mt-4 md:mt-0"
              >
                {card.ctaText}
              </motion.button> */}
            </motion.div>
          ))}
        </motion.ul>
      </div>
    </section>
  );
}

export const CloseIcon = () => {
  return (
    <motion.svg
      /* initial={{
        opacity: 0,
      }} */
      animate={{
        opacity: 1,
      }}
      exit={{
        opacity: 0,
        transition: {
          duration: 0.05,
        },
      }}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-4 w-4 text-black"
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M18 6l-12 12" />
      <path d="M6 6l12 12" />
    </motion.svg>
  );
};

const cards = [
  {
    key: 1,
    description: () => {
      return (
        <div className="overflow-auto max-h-64 space-y-2">
          <div>Karol Cristina da Silva is an actress known for her role as Rabé in "Star Wars: Episode I – The Phantom Menace."</div>

          <div>We're thrilled to announce that Karol Cristina da Silva will be joining us at The Convention Before Christmas 6th and 7th of December!</div>

          <div>She will be one of 3 amazing cosplay judges and will be available for photo opportunities.</div>
        </div>
      )
    },
    title: "Karol Cristina da Silva",
    src: "/images/celebs/rabe-1.webp",
    ctaText: "More Info",
    ctaLink: "",
  },
  {
    key: 2,
    description: () => {
      return (
        <div className="overflow-auto max-h-64 space-y-2">
          <div>We welcome illustrator and animator David Butler to the Convention Before Christmas!</div>
          <div>Why he's a standout guest:</div>
          <div>* Illustrator of Tom Crean: Irish Antarctic Hero, a powerful new graphic novel on Ireland's legendary Antarctic explorer, created with author Michael Smith and released earlier this year.</div>
          <div>* His portfolio also includes graphic biographies like Michael Collins: Ireland's Rebel Son (2022)</div>
          <div>* A graduate of Dún Laoghaire Film Institute whose animation roots run deep, his style brings characters and history vividly to life.</div>
        </div>
      )
    },
    title: "Dave Butler",
    src: "/images/celebs/dave-butler.jpg",
    ctaText: "More Info",
    ctaLink: "",
  },
  {
    key: 3,
    description: "TBA",
    title: "Special guest to be announced",
    src: "/images/celebs/default-celeb.jpg",
    ctaText: "More Info",
    ctaLink: "",
  },
  {
    key: 4,
    description: "TBA",
    title: "Special guest to be announced",
    src: "/images/celebs/default-celeb.jpg",
    ctaText: "More Info",
    ctaLink: "",
  },
];
