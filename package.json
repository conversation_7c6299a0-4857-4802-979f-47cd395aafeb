{"name": "con-before-christmas", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@chakra-ui/react": "^3.14.2", "@devnomic/marquee": "^1.0.3", "@emailjs/browser": "^4.4.1", "@emotion/react": "^11.14.0", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@iconify/react": "^6.0.0", "@nivo/axes": "^0.99.0", "@nivo/bar": "^0.99.0", "@nivo/bump": "^0.99.0", "@nivo/core": "^0.99.0", "@nivo/geo": "^0.99.0", "@nivo/legends": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/pie": "^0.99.0", "@nivo/tooltip": "^0.99.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.12", "@react-email/components": "^0.0.34", "@reduxjs/toolkit": "^2.8.2", "@remix-run/node": "^2.16.8", "@remix-run/react": "^2.16.8", "@remix-run/serve": "^2.16.8", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^6.1.0", "@supabase/supabase-js": "^2.49.3", "@tippyjs/react": "^4.2.6", "@types/bcryptjs": "^2.4.6", "animate.css": "^4.1.1", "aos": "^2.3.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "d3-geo": "^3.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.6.0", "geoip-lite": "^1.4.10", "isbot": "^4.1.0", "lucide-react": "^0.484.0", "mapbox-gl": "^3.10.0", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "react-scan": "^0.3.4", "react-slick": "^0.30.3", "react-tabs": "^6.1.0", "react-toastify": "^11.0.5", "remix-utils": "^8.7.0", "resend": "^4.5.1", "simplex-noise": "^4.0.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "stripe": "^18.2.0", "styled-components": "^6.1.18", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.46"}, "devDependencies": {"@remix-run/dev": "^2.16.8", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}