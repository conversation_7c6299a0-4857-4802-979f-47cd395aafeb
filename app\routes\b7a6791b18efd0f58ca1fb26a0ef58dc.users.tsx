import { useEffect, useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import {
  fetchUserBehavior,
  fetchUserDemographics,
  setUserBehaviorTimeRange,
  setUserDemographicsTimeRange
} from "@/app/store/slices/adminSlice";
import {
  Users,
  BarChart2,
  Clock,
  TrendingUp,
  RefreshCw,
  FileText,
  Activity,
} from "lucide-react";
import { AdminLayout } from "@/app/components/admin/layout/AdminLayout";
import { ProtectedRoute } from "@/app/components/admin/ProtectedRoute";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/app/components/ui/card";
import { StatCard } from "@/app/components/admin/charts/StatCard";
import { PageViewsChart } from "@/app/components/admin/charts/PageViewsChart";
import { DeviceDistributionChart } from "@/app/components/admin/charts/DeviceDistributionChart";
import { UserActivityChart } from "@/app/components/admin/charts/UserActivityChart";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";

// Helper function to format time duration
const formatDuration = (seconds: number): string => {
  if (!seconds) return '0s';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
};

export default function UserAnalyticsOverview() {
  const dispatch = useDispatch<AppDispatch>();
  const {
    data: behaviorData,
    loading: behaviorLoading,
    error: behaviorError,
    timeRange: behaviorTimeRange
  } = useSelector((state: RootState) => state?.admin?.userBehavior);

  const {
    data: demographicsData,
    loading: demographicsLoading,
    error: demographicsError
  } = useSelector((state: RootState) => state.admin.userDemographics);

  const [dateRange, setDateRange] = useState<'7days' | '30days' | '90days' | 'year'>('30days');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch data on component mount and when date range changes
  useEffect(() => {
    const fetchData = async () => {
      await Promise.all([
        dispatch(fetchUserBehavior({ timeRange: dateRange })),
        dispatch(fetchUserDemographics({ timeRange: dateRange }))
      ]);
      setIsRefreshing(false);
    };

    fetchData();

    // Update time range in Redux when it changes
    dispatch(setUserBehaviorTimeRange(dateRange));
    dispatch(setUserDemographicsTimeRange(dateRange));
  }, [dispatch, dateRange]);

  const handleRefresh = useCallback(async () => {
    try {
      setIsRefreshing(true);
      await Promise.all([
        dispatch(fetchUserBehavior({ timeRange: dateRange })),
        dispatch(fetchUserDemographics({ timeRange: dateRange }))
      ]);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [dispatch, dateRange]);

  const handleDateRangeChange = (range: '7days' | '30days' | '90days' | 'year') => {
    setDateRange(range);
  };

  // Color utility functions for different analytics categories
  const getDeviceColor = (device: string) => {
    const deviceMap: Record<string, string> = {
      'desktop': '#3b82f6',  // blue
      'mobile': '#10b981',   // emerald
      'tablet': '#f59e0b',   // amber
      'smartphone': '#10b981', // emerald
      'other': '#8b5cf6',    // violet
    };
    return deviceMap[device?.toLowerCase()] || '#8b5cf6';
  };

  const getBrowserColor = (browser: string) => {
    const browserMap: Record<string, string> = {
      'chrome': '#4285f4',
      'firefox': '#ff9500',
      'safari': '#1e88e5',
      'edge': '#0078d7',
      'opera': '#ff1b2d',
      'samsung internet': '#1da1f2',
      'ie': '#00a4ef',
      'android browser': '#3ddc84',
      'uc browser': '#ff6a00',
    };
    return browserMap[browser?.toLowerCase()] || '#8b5cf6';
  };

  const getOSColor = (os: string) => {
    const osMap: Record<string, string> = {
      'windows': '#0078d7',
      'macos': '#000000',
      'ios': '#000000',
      'android': '#3ddc84',
      'linux': '#fcc624',
      'chromeos': '#4285f4',
      'ubuntu': '#e95420',
      'fedora': '#0b57a4',
    };
    return osMap[os?.toLowerCase()] || '#8b5cf6';
  };

  const getScreenColor = (size: string) => {
    // Extract the width from the size string (e.g., "1920x1080" -> 1920)
    const width = parseInt(size?.split('x')[0], 10) || 0;

    if (width >= 1920) return '#3b82f6';  // desktop
    if (width >= 1024) return '#10b981';  // laptop/tablet landscape
    if (width >= 768) return '#f59e0b';   // tablet
    if (width >= 320) return '#ef4444';   // mobile

    return '#8b5cf6';  // default
  };

  return (
    <ProtectedRoute requiredRole={["super_admin", "support_admin"]}>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-white">User Analytics Overview</h1>
              <p className="mt-1 text-sm text-amber-100/70">
                Key metrics and insights about your users
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={behaviorLoading || demographicsLoading || isRefreshing}
                className="border-amber-600/30 text-amber-100 bg-zinc-800 hover:bg-amber-900/30"
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isRefreshing ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>
              <div className="relative">
                <select
                  value={dateRange}
                  onChange={(e) => handleDateRangeChange(e.target.value as '7days' | '30days' | '90days' | 'year')}
                  className="appearance-none bg-zinc-800 border border-amber-600/30 rounded-md pl-3 pr-8 py-2 text-sm text-amber-100 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                >
                  <option value="7days">Last 7 days</option>
                  <option value="30days">Last 30 days</option>
                  <option value="90days">Last 90 days</option>
                  <option value="year">This year</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-100">
                  <svg
                    className="h-4 w-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <StatCard
              title="Total Visitors"
              value={behaviorData?.pageViews?.reduce((sum, day) => sum + day.users, 0).toLocaleString() || '0'}
              change=""
              icon={<Users className="h-5 w-5 text-amber-400" />}
              color="amber"
              loading={behaviorLoading || isRefreshing}
              tooltip="Total unique visitors in the selected time period"
            />
            <StatCard
              title="Page Views"
              value={behaviorData?.pageViews?.reduce((sum, day) => sum + day.views, 0).toLocaleString() || '0'}
              change=""
              icon={<BarChart2 className="h-5 w-5 text-blue-400" />}
              color="blue"
              loading={behaviorLoading || isRefreshing}
              tooltip="Total number of pages viewed in the selected time period"
            />
            <StatCard
              title="Sessions"
              value={behaviorData?.pageViews?.reduce((sum, day) => sum + day.sessions, 0).toLocaleString() || '0'}
              change=""
              icon={<Activity className="h-5 w-5 text-green-400" />}
              color="green"
              loading={behaviorLoading || isRefreshing}
              tooltip="Total number of sessions in the selected time period"
            />
            <StatCard
              title="Avg. Session"
              value={behaviorData?.sessionMetrics ? formatDuration(behaviorData.sessionMetrics.avgSessionDuration) : '0s'}
              change=""
              icon={<Clock className="h-5 w-5 text-purple-400" />}
              color="purple"
              loading={behaviorLoading || isRefreshing}
              tooltip="Average duration of user sessions"
            />
            <StatCard
              title="Bounce Rate"
              value={behaviorData?.sessionMetrics ? `${behaviorData.sessionMetrics.bounceRate}%` : '0%'}
              change=""
              icon={<TrendingUp className="h-5 w-5 text-red-400" />}
              color="red"
              loading={behaviorLoading || isRefreshing}
              tooltip="Percentage of single-page sessions (bounced)"
            />
            <StatCard
              title="Pages/Session"
              value={behaviorData?.sessionMetrics?.avgPagesPerSession?.toFixed(1) || '0.0'}
              change=""
              icon={<FileText className="h-5 w-5 text-indigo-400" />}
              color="indigo"
              loading={behaviorLoading || isRefreshing}
              tooltip="Average number of pages viewed per session"
            />
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Page Views Chart */}
            <Card className="border-amber-600/30 bg-zinc-800/50">
              <CardHeader>
                <CardTitle className="text-white">Page Views</CardTitle>
                <CardDescription className="text-amber-100/50">
                  {dateRange === '7days' ? 'Last 7 days' :
                    dateRange === '30days' ? 'Last 30 days' :
                      dateRange === '90days' ? 'Last 90 days' : 'Last 12 months'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-full">
                  {behaviorLoading || isRefreshing ? (
                    <div className="flex items-center justify-center h-full">
                      <RefreshCw className="h-6 w-6 animate-spin text-amber-400" />
                    </div>
                  ) : behaviorError ? (
                    <div className="text-red-400 text-sm p-4">Error loading chart: {behaviorError}</div>
                  ) : behaviorData?.pageViews?.length ? (
                    <PageViewsChart
                      data={behaviorData.pageViews.map(pv => ({
                        date: pv.date,
                        views: pv.views,
                        uniqueVisitors: pv.uniqueVisitors,
                      }))}
                      height={356}
                    />
                  ) : (
                    <div className="text-amber-100/50 flex items-center justify-center h-full">
                      No page view data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Device Analytics Card */}
            <Card className="border-amber-600/30 bg-zinc-800/50">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-white">Device Analytics</CardTitle>

                </div>
                <CardDescription className="text-amber-100/50">
                  {dateRange === '7days' ? 'Last 7 days' :
                    dateRange === '30days' ? 'Last 30 days' :
                      dateRange === '90days' ? 'Last 90 days' : 'Last 12 months'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-full">
                  {behaviorLoading || isRefreshing ? (
                    <div className="flex items-center justify-center h-full">
                      <RefreshCw className="h-6 w-6 animate-spin text-amber-400" />
                    </div>
                  ) : behaviorError ? (
                    <div className="text-red-400 text-sm p-4">Error loading device data: {behaviorError}</div>
                  ) : behaviorData?.deviceAnalytics ? (
                    <Tabs defaultValue="type" className="h-full">
                      <TabsList className="grid w-full grid-cols-4 bg-zinc-800/50 border border-amber-600/30 mb-4">
                        <TabsTrigger value="type" className="text-xs">Type</TabsTrigger>
                        <TabsTrigger value="browser" className="text-xs">Browser</TabsTrigger>
                        <TabsTrigger value="os" className="text-xs">OS</TabsTrigger>
                        <TabsTrigger value="screen" className="text-xs">Screen</TabsTrigger>
                      </TabsList>
                      {/* Device Type Tab */}
                      <TabsContent value="type" className="h-full m-0">
                        {behaviorData.deviceTypes?.length > 0 ? (
                          <DeviceDistributionChart
                            data={behaviorData.deviceTypes.map(dt => ({
                              id: dt.id,
                              label: dt.label,
                              value: dt.value,
                              color: getDeviceColor(dt.label)
                            }))}
                            height={350}
                          />
                        ) : (
                          <div className="text-amber-100/50 flex items-center justify-center h-full">
                            No device type data available
                          </div>
                        )}
                      </TabsContent>

                      {/* Browser Tab */}
                      <TabsContent value="browser" className="h-full m-0">
                        {behaviorData.deviceAnalytics.browsers?.length > 0 ? (
                          <DeviceDistributionChart
                            data={[...behaviorData.deviceAnalytics.browsers]
                              .sort((a, b) => (b?.count || 0) - (a?.count || 0))
                              .map(item => ({
                                id: item.id,
                                label: item.label,
                                value: item.value,
                                color: getBrowserColor(item.label)
                              }))}
                            height={350}
                          />
                        ) : (
                          <div className="text-amber-100/50 flex items-center justify-center h-full">
                            No browser data available
                          </div>
                        )}
                      </TabsContent>

                      {/* OS Tab */}
                      <TabsContent value="os" className="h-full m-0">
                        {behaviorData.deviceAnalytics.operatingSystems?.length > 0 ? (
                          <DeviceDistributionChart
                            data={[...behaviorData.deviceAnalytics.operatingSystems]
                              .sort((a, b) => b.count - a.count)
                              .map(os => ({
                                id: os.id || os.label.toLowerCase(),
                                label: os.label,
                                value: os.value ?? os.count,
                                color: getOSColor(os.label)
                              }))}
                            height={350}
                          />
                        ) : (
                          <div className="text-amber-100/50 flex items-center justify-center h-full">
                            No OS data available
                          </div>
                        )}
                      </TabsContent>

                      {/* Screen Size Tab */}
                      <TabsContent value="screen" className="h-full m-0">
                        {behaviorData.deviceAnalytics.screenSizes?.length > 0 ? (
                          <DeviceDistributionChart
                            data={[...behaviorData.deviceAnalytics.screenSizes]
                              .sort((a, b) => b.count - a.count)
                              .map(screen => ({
                                id: screen.id || screen.label,
                                label: screen.label,
                                value: screen.value ?? screen.count,
                                color: getScreenColor(screen.label)
                              }))}
                            height={350}
                          />
                        ) : (
                          <div className="text-amber-100/50 flex items-center justify-center h-full">
                            No screen size data available
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  ) : (
                    <div className="text-amber-100/50 flex items-center justify-center h-full">
                      No device analytics data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* User Activity Chart */}
            <Card className="border-amber-600/30 bg-zinc-800/50">
              <CardHeader>
                <CardTitle className="text-white">User Activity</CardTitle>
                <CardDescription className="text-amber-100/50">
                  {dateRange === '7days' ? 'Last 7 days' :
                    dateRange === '30days' ? 'Last 30 days' :
                      dateRange === '90days' ? 'Last 90 days' : 'Last 12 months'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-full">
                  {behaviorLoading || isRefreshing ? (
                    <div className="flex items-center justify-center h-full">
                      <RefreshCw className="h-6 w-6 animate-spin text-amber-400" />
                    </div>
                  ) : behaviorError ? (
                    <div className="text-red-400 text-sm p-4">Error loading data: {behaviorError}</div>
                  ) : behaviorData?.userActivity ? (
                    <UserActivityChart data={{
                      labels: behaviorData.userActivity.labels || [],
                      activeUsers: behaviorData.userActivity.activeUsers || [],
                      newUsers: behaviorData.userActivity.newUsers || [],
                      sessions: behaviorData.userActivity.sessions || [],
                      returningUsers: behaviorData.userActivity.returningUsers || []
                    }} />
                  ) : (
                    <div className="text-amber-100/50 text-sm p-4">No activity data available</div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* New vs Returning Visitors */}
            <Card className="border-amber-600/30 bg-zinc-800/50">
              <CardHeader>
                <CardTitle className="text-white">Visitor Demographics</CardTitle>
                <CardDescription className="text-amber-100/50">
                  {dateRange === '7days' ? 'Last 7 days' :
                    dateRange === '30days' ? 'Last 30 days' :
                      dateRange === '90days' ? 'Last 90 days' : 'Last 12 months'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="new-returning" className="h-full">
                  <TabsList className="grid w-full grid-cols-2 bg-zinc-800/50 border border-amber-600/30">
                    <TabsTrigger value="new-returning" className="text-xs">New vs Returning</TabsTrigger>
                    <TabsTrigger value="location" className="text-xs">Location</TabsTrigger>
                  </TabsList>
                  <TabsContent value="new-returning" className="h-56">
                    {demographicsLoading || isRefreshing ? (
                      <div className="flex items-center justify-center h-full">
                        <RefreshCw className="h-6 w-6 animate-spin text-amber-400" />
                      </div>
                    ) : demographicsError ? (
                      <div className="text-red-400 text-sm p-4">Error loading data: {demographicsError}</div>
                    ) : demographicsData?.newVsReturning ? (
                      <div className="h-full flex flex-col items-center justify-center">
                        <div className="relative w-48 h-48 mb-4">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                              <p className="text-3xl font-bold text-amber-400">
                                {demographicsData.newVsReturning.newUsers + demographicsData.newVsReturning.returningUsers}
                              </p>
                              <p className="text-xs text-amber-100/70">Total Visitors</p>
                            </div>
                          </div>
                          <div className="relative w-full h-full">
                            <div
                              className="absolute inset-0 rounded-full border-8 border-amber-400/30"
                              style={{
                                clipPath: `polygon(0 0, 100% 0, 100% 100%, 0% 100%)`,
                              }}
                            ></div>
                            <div
                              className="absolute inset-0 rounded-full border-8 border-amber-400"
                              style={{
                                clipPath: `polygon(0 0, 100% 0, 100% 100%, 0% 100%)`,
                                transform: 'rotate(0.5turn)',
                                transformOrigin: 'center',
                                '--rotation': `${(demographicsData.newVsReturning.newUsers / (demographicsData.newVsReturning.newUsers + demographicsData.newVsReturning.returningUsers)) * 360}deg`
                              } as React.CSSProperties}
                            ></div>
                          </div>
                        </div>
                        <div className="flex gap-6 text-center">
                          <div>
                            <div className="h-3 w-3 rounded-full bg-amber-400 inline-block mr-1"></div>
                            <span className="text-xs text-amber-100/80">New: {demographicsData.newVsReturning.newUsers}</span>
                          </div>
                          <div>
                            <div className="h-3 w-3 rounded-full bg-amber-400/30 inline-block mr-1"></div>
                            <span className="text-xs text-amber-100/80">Returning: {demographicsData.newVsReturning.returningUsers}</span>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-amber-100/50 text-sm p-4">No visitor data available</div>
                    )}
                  </TabsContent>
                  <TabsContent value="location" className="h-56">
                    {demographicsLoading || isRefreshing ? (
                      <div className="flex items-center justify-center h-full">
                        <RefreshCw className="h-6 w-6 animate-spin text-amber-400" />
                      </div>
                    ) : demographicsError ? (
                      <div className="text-red-400 text-sm p-4">Error loading data: {demographicsError}</div>
                    ) : behaviorData?.locationAnalytics?.length ? (
                      <div className="h-full flex flex-col">
                        <div className="flex-1 overflow-y-auto pr-2">
                          {behaviorData.locationAnalytics.map((location: any, index: any) => (
                            <div key={index} className="mb-2">
                              <div className="flex justify-between text-xs mb-1">
                                <span className="text-amber-100/80">{location.label || 'Unknown'}</span>
                                <span className="text-amber-400">{location.count} users</span>
                              </div>
                              <div className="w-full bg-zinc-700/50 rounded-full h-2">
                                <div
                                  className="bg-amber-400 h-2 rounded-full"
                                  style={{
                                    width: `${(location.count / Math.max(...behaviorData.locationAnalytics.map((l: any) => l.count))) * 100}%`
                                  }}
                                ></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-amber-100/50 text-sm p-4">No location data available</div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};