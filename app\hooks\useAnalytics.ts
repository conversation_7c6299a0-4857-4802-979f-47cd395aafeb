import { useEffect, useCallback } from 'react';
import { useLocation } from '@remix-run/react';
import {
  trackPageView,
  trackEvent,
  trackUserDemographics,
  trackTicketJourney,
  trackVendorJourney,
  trackDonationJourney,
  trackFormJourney,
  trackNavigation,
  AnalyticsEventType
} from '@/app/utils/analytics';

// Type definitions for analytics events
type EventProperties = Record<string, any>;

interface TrackEventParams {
  eventName: AnalyticsEventType;
  eventData?: EventProperties;
  userId?: string | null;
}

interface TrackPageViewParams {
  path?: string;
  metadata?: {
    type?: string;
    [key: string]: any;
  };
}

interface TrackUserDemographicsParams {
  userId: string;
  age?: number;
  gender?: string;
  country?: string;
  city?: string;
}

// Track page views and user interactions
export function useAnalytics() {
  const location = useLocation();

  // Track page views
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const path = location.pathname + location.search;
    trackPageView(path, {
      type: 'page_view',
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
    });
  }, [location]);

  // Track custom events with type safety
  const track = useCallback((params: TrackEventParams | string, eventData?: EventProperties) => {
    if (typeof window === 'undefined') return;

    let eventName: AnalyticsEventType;
    let finalEventData: EventProperties = {
      ...eventData,
      page_url: window.location.href,
      timestamp: new Date().toISOString(),
    };

    if (typeof params === 'string') {
      eventName = params as AnalyticsEventType;
    } else {
      eventName = params.eventName;
      if (params.eventData) {
        finalEventData = { ...finalEventData, ...params.eventData };
      }
    }

    trackEvent(eventName, finalEventData);
  }, []);

  // Track user demographics (to be called when user registers or updates profile)
  const trackUserData = useCallback((params: TrackUserDemographicsParams) => {
    if (typeof window === 'undefined') return;

    const { userId, ...demographics } = params;
    trackUserDemographics(userId, demographics);
  }, [track]);

  // Track form interactions
  const trackForm = useCallback((formName: string, action: string, data: EventProperties = {}) => {
    track({
      eventName: 'form_interaction',
      eventData: {
        form_name: formName,
        action,
        ...data,
      },
    });
  }, [track]);

  // Track button clicks and other UI interactions
  const trackInteraction = useCallback((elementName: string, action: string, data: EventProperties = {}) => {
    track({
      eventName: 'ui_interaction',
      eventData: {
        element: elementName,
        action,
        ...data,
      },
    });
  }, [track]);

  // Track errors
  const trackError = useCallback((error: Error, context: Record<string, any> = {}) => {
    track({
      eventName: 'error_occurred',
      eventData: {
        error_message: error.message,
        error_stack: error.stack,
        error_name: error.name,
        ...context,
      },
    });
  }, [track]);

  return {
    track,
    trackUserData,
    trackForm,
    trackInteraction,
    trackError,
    // Journey-specific tracking
    trackTicketJourney,
    trackVendorJourney,
    trackDonationJourney,
    trackFormJourney,
    trackNavigation,
  };
};

// Track form interactions with additional form-specific methods
export function useFormAnalytics(formName: string) {
  const { track, trackForm } = useAnalytics();

  // Track form submission
  const trackSubmit = useCallback((data: Record<string, any> = {}) => {
    trackForm(formName, 'submit', data);
  }, [formName, trackForm]);

  // Track form field interactions
  const trackFieldInteraction = useCallback((fieldName: string, action: string, value?: any) => {
    trackForm(formName, `field_${action}`, {
      field: fieldName,
      value: value !== undefined ? value : null
    });
  }, [formName, trackForm]);

  // Track form validation errors
  const trackValidationError = useCallback((fieldName: string, error: string) => {
    trackForm(formName, 'validation_error', {
      field: fieldName,
      error
    });
  }, [formName, trackForm]);

  return {
    trackSubmit,
    trackFieldInteraction,
    trackValidationError,
  };
};

// Track button clicks
export function useButtonAnalytics(buttonName: string) {
  const { track } = useAnalytics();

  const trackClick = (additionalData: Record<string, any> = {}) => {
    track('button_click', {
      button_name: buttonName,
      ...additionalData,
    });
  };

  return { trackClick };
};