import { Navigate } from '@remix-run/react';
import { useAdminAuth, type AdminRole } from '@/app/context/AdminAuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: AdminRole | AdminRole[];
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { isAuthenticated, hasPermission, loading } = useAdminAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-zinc-900">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-t-red-600 border-b-red-600 border-l-gray-200 border-r-gray-200 rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  // Check authentication
  if (!isAuthenticated) {
    return <Navigate to="/b7a6791b18efd0f58ca1fb26a0ef58dc/login" replace />;
  }

  // Check role permissions if required
  if (requiredRole && !hasPermission(requiredRole)) {
    return <Navigate to="/b7a6791b18efd0f58ca1fb26a0ef58dc/unauthorized" replace />;
  }

  return <>{children}</>;
};