import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { getSupabaseClient } from '@/app/utils/supabaseClient';
import type { AdminRole } from '@/app/context/AdminAuthContext';
import { v4 as uuidv4 } from 'uuid';

declare global {
  interface Window {
    ENV: {
      SUPABASE_URL: string;
      SUPABASE_KEY: string;
      [key: string]: string | undefined;
    };
  }
}

// Helper to safely use Supabase in async functions
const withSupabase = async <T>(
  callback: (supabase: ReturnType<typeof getSupabaseClient>) => Promise<T>
): Promise<T> => {
  if (typeof window === 'undefined') {
    // On the server, return a rejected promise with a meaningful error
    return Promise.reject(new Error('Supabase client can only be used on the client side'));
  }

  try {
    const client = getSupabaseClient();
    if (!client) {
      throw new Error('Failed to initialize Supabase client');
    }
    return await callback(client);
  } catch (error) {
    console.error('Supabase error:', error);
    throw error;
  }
};

// Define types
interface Donation {
  payment_method: string;
  status: string;
  id: string;
  order_id: string;
  amount: number;
  created_at: string;
  metadata: Record<string, any>;
  multiplier: number | null;
  original_amount: number | null;
}

// Vendor Stand types
interface VendorStandAddon {
  id: string;
  name: string;
  quantity: number;
  price: number;
}

interface VendorStandSale {
  id: string;
  stand_number: number;
  business_name: string;
  contact_name: string;
  email: string;
  phone: string;
  price: number;
  addons: VendorStandAddon[];
  discount_applied: boolean;
  discount_percentage: number | null;
  original_price: number | null;
  created_at: string;
  status?: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_intent_id?: string;
  metadata?: Record<string, any>;
}

interface VendorStallMetrics {
  totalStands: number;
  totalRevenue: number;
  standsByType: Array<{
    type: string;
    count: number;
    revenue: number;
    averageRevenue: number;
  }>;
  recentSales: VendorStandSale[];
  topBusinesses: Array<{
    businessName: string;
    contactName: string;
    email: string;
    totalSpent: number;
    standsBooked: number;
  }>;
  revenueByDay: Array<{
    date: string;
    revenue: number;
    count: number;
  }>;
  addonRevenue: number;
  discountSavings: number;
  dateRange: {
    start: string;
    end: string;
  };
}

interface DonationMetrics {
  totalDonations: number;
  monthlyDonations: number;
  recurringDonations: number;
  topDonors: Array<{ name: string; amount: number; email: string }>;
  dailyDonations: Array<{ date: string; amount: number }>;
  campaignTotals: Record<string, number>;
  recentDonations: Donation[];
}

interface TicketType {
  id: string;
  name: string;
  description: string;
  price: number;
  // Add other ticket properties as needed
}

interface OrderItem {
  id: number;
  ticket_id: string;
  quantity: number;
  price: number;
  order_id: string;
  original_ticket_id: string;
  selected_day: string;
  ticket_name?: string;
  ticket_description?: string;
  ticket?: TicketType;
}

interface AdminUser {
  id: string;
  userId: string;
  email: string;
  name?: string;
  role: AdminRole;
  status: 'active' | 'inactive' | 'suspended';
  lastActive?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface TransactionItem {
  id: number | string;
  ticket_id?: string;
  quantity: number;
  price: number;
  order_id?: string;
  original_ticket_id?: string;
  selected_day?: string;
  ticket_name?: string;
  ticket_description?: string;
  // For vendor stands
  stand_number?: number;
  business_name?: string;
  addons?: VendorStandAddon[];
  // For donations
  donation_type?: string;
  campaign?: string;
  multiplier?: number;
  original_amount?: number;
}

export interface Transaction {
  id: string;
  email: string;
  name: string;
  total: number;
  payment_intent_id: string;
  created_at: string;
  redeemed?: boolean;
  redeemed_timestamp?: string | null;
  status: 'success' | 'failed' | 'pending' | 'refunded' | 'cancelled';
  items: TransactionItem[];
  transaction_type: 'ticket' | 'vendor_stand' | 'donation';
  // Additional fields for vendor stands
  stand_number?: number;
  business_name?: string;
  contact_name?: string;
  phone?: string;
  discount_applied?: boolean;
  discount_percentage?: number | null;
  original_price?: number | null;
  // Additional fields for donations
  donation_campaign?: string;
  multiplier?: number;
  original_amount?: number;
  is_recurring?: boolean;
}

interface DashboardMetrics {
  totalRevenue: number;
  ticketsSold: number;
  activeUsers: number;
  conversionRate: number;
  averageOrderValue: number;
  uniqueCustomers: number;
  recentTransactions: any;
  totalCustomers: number;
  dateRange: any;
  revenueByDay: Array<{
    date: string;
    revenue: number;
  }>;
  ticketsByType: Array<{
    type: string;
    count: number;
  }>;
  revenueByProductType: Array<{
    name: string;
    revenue: number;
  }>;
}

// User Analytics Types
export interface UserDemographics {
  totalUsers: number;
  locations: any;
  topCountries: Array<{
    id: string;
    name: string;
    users: number;
    percent: number;
  }>;
  ageGroups: Array<{
    id: string;
    label: string;
    value: number;
    count: number;
  }>;
  devices: Array<{
    id: string;
    label: string;
    value: number;
    count: number;
  }>;
  genderDistribution: Array<{
    gender: string;
    count: number;
    percent: number;
  }>;
  newVsReturning: {
    newUsers: number;
    returningUsers: number;
  };
}

export interface UserBehaviorMetrics {
  pageViews: Array<{
    date: string;
    views: number;
    users: number;
    sessions: number;
    uniqueVisitors: number;
    bounceRate: number;
    activityData: any;
    behaviorData: any;
  }>;
  topPages: Array<{
    path: string;
    title: string;
    views: number;
    users: number;
    avgTimeOnPage: number;
    bounceRate: number;
  }>;
  userActivity: {
    returningUsers: number[];
    labels: string[];
    activeUsers: number[];
    newUsers: number[];
    sessions: number[];
  };
  sessionMetrics: {
    avgSessionDuration: number;
    avgPagesPerSession: number;
    bounceRate: number;
  };
  deviceTypes: Array<{
    id: any;
    label: any;
    value: any;
    type: string;
    count: number;
    percent: number;
  }>;
  deviceAnalytics: {
    browsers: Array<{
      id: any;
      label: any;
      value: any;
      browser: string;
      count: number;
      percent: number;
    }>;
    operatingSystems: Array<{
      id: any;
      label: any;
      value: number;
      os: string;
      count: number;
      percent: number;
    }>;
    screenSizes: Array<{
      id: any;
      value: number;
      label: any;
      size: string;
      count: number;
      percent: number;
    }>;
  };
  locationAnalytics: Array<{
    id: string;
    label: string;
    value: number;
    count: number;
    percent: number;
  }>;
  eventsByType: Array<{ date: string } & { [eventType: string]: number }>;
}

export interface AdminState {
  metrics: {
    data: DashboardMetrics | null;
    loading: boolean;
    error: string | null;
  };
  transactions: {
    data: Transaction[];
    loading: boolean;
    error: string | null;
    page: number;
    totalPages: number;
    count: number | null;
  };
  users: {
    data: AdminUser[];
    loading: boolean;
    error: string | null;
    page: number;
    pageSize: number;
    total: number;
    searchQuery: string;
    filters: {
      role: string;
      status: string;
    };
    sort: {
      field: string;
      direction: 'asc' | 'desc';
    };
  };
  userDemographics: {
    data: UserDemographics | null;
    loading: boolean;
    error: string | null;
    timeRange: '7days' | '30days' | '90days' | 'year';
  };
  userBehavior: {
    data: UserBehaviorMetrics | null;
    loading: boolean;
    error: string | null;
    timeRange: '7days' | '30days' | '90days' | 'year';
  };
  donations: {
    data: DonationMetrics | null;
    loading: boolean;
    error: string | null;
    notifications: {
      data: Array<{
        id: string;
        message: string;
        type: 'info' | 'warning' | 'error' | 'success';
        read: boolean;
        createdAt: string;
      }>;
      unreadCount: number;
    };
  };
  vendorStalls: {
    data: VendorStallMetrics | null;
    loading: boolean;
    error: string | null;
  };
}

// Define and export initial state
export const initialState: AdminState = {
  metrics: {
    data: null,
    loading: false,
    error: null,
  },
  transactions: {
    data: [],
    loading: false,
    error: null,
    page: 1,
    totalPages: 1,
    count: null,
  },
  users: {
    data: [],
    loading: false,
    error: null,
    page: 1,
    pageSize: 10,
    total: 0,
    searchQuery: '',
    filters: {
      role: '',
      status: '',
    },
    sort: {
      field: 'createdAt',
      direction: 'desc',
    },
  },
  userDemographics: {
    data: null,
    loading: false,
    error: null,
    timeRange: '30days',
  },
  userBehavior: {
    data: null,
    loading: false,
    error: null,
    timeRange: '30days',
  },
  donations: {
    data: null,
    loading: false,
    error: null,
    notifications: {
      data: [],
      unreadCount: 0,
    },
  },
  vendorStalls: {
    data: null,
    loading: false,
    error: null,
  },
};

// Define async thunks
export const fetchTransactions = createAsyncThunk(
  'admin/fetchTransactions',
  async (
    { page = 1, pageSize = 10, status = 'all', transactionType = 'all', searchQuery = '' }: { page?: number; pageSize?: number; status?: string; transactionType?: string; searchQuery?: string } = {},
    { rejectWithValue }
  ) => {
    try {
      return await withSupabase(async (supabase) => {
        const allTransactions: Transaction[] = [];
        let ordersCount = 0;
        let vendorStandsCount = 0;
        let donationsCount = 0;

        // Only fetch the transaction types that match the filter
        const shouldFetchTickets = transactionType === 'all' || transactionType === 'ticket';
        const shouldFetchVendorStands = transactionType === 'all' || transactionType === 'vendor_stand';
        const shouldFetchDonations = transactionType === 'all' || transactionType === 'donation';

        // 1. Fetch Ticket Sales (Orders) - only if needed
        if (shouldFetchTickets) {
          let ordersQuery = supabase!
            .from('Orders')
            .select('*', { count: 'exact' })
            .order('created_at', { ascending: false });

          if (searchQuery) {
            // Search only on text/varchar fields to avoid UUID casting issues
            ordersQuery = ordersQuery.or(
              `name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`
            );
          }

          if (status && status !== 'all') {
            if (status === 'complete' || status === 'paid' || status == 'success') {
              ordersQuery = ordersQuery.in('status', ['paid', 'complete', 'success']);
            } else {
              ordersQuery = ordersQuery.eq('status', status);
            }
          }

          const { data: orders, error: ordersError, count: fetchedOrdersCount } = await ordersQuery;
          ordersCount = fetchedOrdersCount || 0;
          if (ordersError) throw ordersError;

          if (orders?.length) {
            // Get order items for ticket sales
            const orderIds = orders.map(order => order.id);
            const { data: orderItems, error: itemsError } = await supabase!
              .from('OrderItems')
              .select('*')
              .in('order_id', orderIds);

            if (itemsError) throw itemsError;

            // Get ticket details
            const ticketIds = [...new Set(orderItems.map(item => item.ticket_id))];
            const { data: tickets, error: ticketsError } = await supabase!
              .from('Tickets')
              .select('id, name, description')
              .in('id', ticketIds);

            if (ticketsError) throw ticketsError;
            const ticketMap = new Map(tickets.map(ticket => [ticket.id, ticket]));

            // Convert orders to transactions
            orders.forEach(order => {
              const orderTransactionItems = orderItems
                .filter(item => item.order_id === order.id)
                // Exclude charity donation items as they're handled separately in the donations section
                .filter(item => item.ticket_id !== 'charity-donation')
                .map(item => ({
                  id: item.id,
                  ticket_id: item.ticket_id,
                  quantity: item.quantity,
                  price: item.price,
                  order_id: item.order_id,
                  original_ticket_id: item.original_ticket_id,
                  selected_day: item.selected_day,
                  ticket_name: ticketMap.get(item.ticket_id)?.name || 'Unknown Ticket',
                  ticket_description: ticketMap.get(item.ticket_id)?.description || ''
                }));

              // Only create a ticket transaction if there are non-charity items
              if (orderTransactionItems.length > 0) {
                allTransactions.push({
                  id: order.id,
                  email: order.email,
                  name: order.name,
                  total: order.total,
                  payment_intent_id: order.payment_intent_id,
                  created_at: order.created_at,
                  redeemed: order.redeemed,
                  redeemed_timestamp: order.redeemed_timestamp,
                  status: order.status,
                  items: orderTransactionItems,
                  transaction_type: 'ticket'
                });
              }
            });
          }
        }

        // 2. Fetch Vendor Stand Purchases - only if needed
        if (shouldFetchVendorStands) {
          let vendorStandsQuery = supabase!
            .from('VendorStands')
            .select('*', { count: 'exact' })
            .order('created_at', { ascending: false });

          if (status && status !== 'all') {
            if (status === 'complete' || status === 'paid' || status == 'success') {
              vendorStandsQuery = vendorStandsQuery.in('payment_status', ['paid', 'complete', 'success']);
            } else {
              vendorStandsQuery = vendorStandsQuery.eq('payment_status', status);
            }
          }

          if (status && status !== 'all') {
            if (status === 'complete' || status === 'paid' || status == 'success') {
              vendorStandsQuery = vendorStandsQuery.in('payment_status', ['paid', 'complete', 'success']);
            } else {
              vendorStandsQuery = vendorStandsQuery.eq('payment_status', status);
            }
          }

          const { data: vendorStands, error: vendorStandsError, count: fetchedVendorStandsCount } = await vendorStandsQuery;
          vendorStandsCount = fetchedVendorStandsCount || 0;
          if (vendorStandsError) throw vendorStandsError;

          if (vendorStands?.length) {
            vendorStands.forEach(stand => {
              const standItems = [{
                id: stand.id,
                quantity: 1,
                price: stand.price,
                stand_number: stand.stand_number,
                business_name: stand.business_name,
                addons: stand.addons || []
              }];

              allTransactions.push({
                id: stand.id,
                email: stand.email,
                name: stand.contact_name,
                total: stand.price,
                payment_intent_id: stand.payment_intent_id || '',
                created_at: stand.created_at,
                status: stand.payment_status || 'pending',
                items: standItems,
                transaction_type: 'vendor_stand',
                stand_number: stand.stand_number,
                business_name: stand.business_name,
                contact_name: stand.contact_name,
                phone: stand.phone,
                discount_applied: stand.discount_applied,
                discount_percentage: stand.discount_percentage,
                original_price: stand.original_price
              });
            });
          }
        }

        // 3. Fetch Donations - only if needed
        if (shouldFetchDonations) {
          // For donations, we only show them when status is 'all' or 'success'
          // since donations are inherently successful transactions
          if (status === 'all' || status === 'success' || status === 'complete' || status == 'paid') {
            let donationsQuery = supabase!
              .from('CharityDonations')
              .select('*', { count: 'exact' })
              .order('created_at', { ascending: false });

            if (searchQuery) {
              // Only search on text fields, avoid UUID fields
              donationsQuery = donationsQuery.or(
                `metadata->>donor_name.ilike.%${searchQuery}%,metadata->>donor_email.ilike.%${searchQuery}%,metadata->>campaign.ilike.%${searchQuery}%`
              );
            }

            const { data: donations, error: donationsError, count: fetchedDonationsCount } = await donationsQuery;
            donationsCount = fetchedDonationsCount || 0;
            if (donationsError) throw donationsError;

            if (donations?.length) {
              donations.forEach(donation => {
                const donationItems = [{
                  id: donation.id,
                  quantity: 1,
                  price: donation.amount,
                  donation_type: 'charity',
                  campaign: donation.metadata?.campaign || 'General Donation',
                  multiplier: donation.multiplier,
                  original_amount: donation.original_amount
                }];

                allTransactions.push({
                  id: donation.id,
                  email: donation.metadata?.donor_email || 'Anonymous',
                  name: donation.metadata?.donor_name || 'Anonymous Donor',
                  total: donation.amount,
                  payment_intent_id: donation.order_id,
                  created_at: donation.created_at,
                  status: 'success', // Donations are typically successful
                  items: donationItems,
                  transaction_type: 'donation',
                  donation_campaign: donation.metadata?.campaign || 'General Donation',
                  multiplier: donation.multiplier,
                  original_amount: donation.original_amount,
                  is_recurring: donation.metadata?.is_recurring || false
                });
              });
            }
          }
          // If status is not 'all' or 'success', we don't include any donations
        }

        // Sort all transactions by created_at (newest first)
        allTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        // Apply pagination to the combined results
        // Now totalCount reflects only the filtered transaction types
        const totalCount = ordersCount + vendorStandsCount + donationsCount;
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        const paginatedTransactions = allTransactions.slice(from, to + 1);
        const totalPages = Math.ceil(totalCount / pageSize);

        return {
          data: paginatedTransactions,
          count: totalCount,
          page,
          totalPages
        };
      });
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch transactions');
    }
  }
);

interface FetchDashboardMetricsParams {
  startDate?: string;
  endDate?: string;
}

export const fetchDashboardMetrics = createAsyncThunk(
  'admin/fetchDashboardMetrics',
  async (params: FetchDashboardMetricsParams = {}, { rejectWithValue }) => {
    const supabase = getSupabaseClient();
    if (!supabase) {
      return rejectWithValue('Supabase client not available (SSR or missing ENV)');
    }

    try {
      // Build the base query with date filtering if provided
      let orderItemsQuery = supabase
        .from('OrderItems')
        .select(`
            id,
            quantity,
            price,
            ticket_id,
            original_ticket_id,
            selected_day,
            order_id,
            orders:order_id (
              id,
              email,
              name,
              total,
              created_at,
              status,
              redeemed
            )
        `)
        .order('created_at', { ascending: false });

      // Apply date filtering if dates are provided
      if (params.startDate && params.endDate) {
        orderItemsQuery = orderItemsQuery
          .gte('created_at', params.startDate)
          .lte('created_at', params.endDate);
      }

      const { data: orderItems, error: orderItemsError } = await orderItemsQuery;

      if (orderItemsError) throw orderItemsError;

      // Filter out any orders that don't have the required data
      const validOrderItems = orderItems.filter(
        (item: any) => item.orders && item.orders.created_at
      );

      // Calculate total revenue from order items
      const totalRevenue = validOrderItems.reduce((sum: number, item: any) => {
        return sum + (Number(item.price) * item.quantity);
      }, 0);

      // Calculate total tickets sold
      const ticketsSold = validOrderItems.reduce((sum: number, item: any) => {
        return sum + item.quantity;
      }, 0);

      // Calculate revenue by day
      const revenueByDay = validOrderItems.reduce((acc: Record<string, number>, item: any) => {
        if (item.orders?.created_at) {
          const date = new Date(item.orders.created_at).toISOString().split('T')[0];
          const itemTotal = Number(item.price) * item.quantity;
          acc[date] = (acc[date] || 0) + itemTotal;
        }
        return acc;
      }, {} as Record<string, number>);

      // Calculate tickets by type and revenue by product type
      const ticketsByType: Record<string, number> = {};
      const revenueByProductType: Record<string, number> = {};

      validOrderItems.forEach((item: any) => {
        const ticketId = item.ticket_id;
        if (ticketId) {
          // Count tickets by type
          ticketsByType[ticketId] = (ticketsByType[ticketId] || 0) + item.quantity;

          // Calculate revenue by product type
          const itemRevenue = Number(item.price) * item.quantity;
          revenueByProductType[ticketId] = (revenueByProductType[ticketId] || 0) + itemRevenue;
        }
      });

      // Get unique customers (based on unique email addresses from orders)
      const uniqueEmails = new Set<string>();
      validOrderItems.forEach((item: any) => {
        if (item.orders?.email) {
          uniqueEmails.add(item.orders.email);
        }
      });

      const totalCustomers = uniqueEmails.size;

      // Get active users from analytics (users with events in last 30 minutes)
      const { data: activeUsersData, error: activeUsersError } = await supabase
        .rpc('GetActiveUserCount');

      const activeUsers = activeUsersError ? 0 : (activeUsersData || 0);

      // Get conversion rate from analytics
      const { data: conversionRateData, error: conversionRateError } = await supabase
        .rpc('GetConversionRate');

      const conversionRate = conversionRateError ? 0 : (conversionRateData || 0);

      // First get the most recent orders
      const { data: recentOrders, error: ordersError } = await supabase
        .from('Orders')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      if (ordersError) {
        console.error('Error fetching recent orders:', ordersError);
        return rejectWithValue('Failed to fetch recent orders');
      }

      const orderIds = recentOrders.map((order: any) => order.id);

      // Get all related data for these orders
      const [
        { data: orderItemsData, error: itemsError },
        { data: donationsData, error: donationsError },
        { data: santaBookingsData, error: santaBookingsError }
      ] = await Promise.all([
        // Get ticket order items
        supabase
          .from('OrderItems')
          .select(`
            *,
            ticket:Tickets!ticket_id(
              id,
              name,
              description,
              price
            )
          `)
          .in('order_id', orderIds),

        // Get donations
        supabase
          .from('CharityDonations')
          .select('*')
          .in('order_id', orderIds),

        // Get Santa bookings (which are a type of order)
        supabase
          .from('SantaBookings')
          .select('*')
          .in('order_id', orderIds)
      ]);

      if (itemsError || donationsError || santaBookingsError) {
        console.error('Error fetching transaction data:', { itemsError, donationsError, santaBookingsError });
        return rejectWithValue('Failed to fetch transaction data');
      }

      // Create lookup maps for each transaction type
      const itemsByOrderId = (orderItemsData || []).reduce((acc: Record<string, any[]>, item: any) => {
        if (!acc[item.order_id]) acc[item.order_id] = [];
        acc[item.order_id].push(item);
        return acc;
      }, {} as Record<string, any[]>);

      const donationsByOrderId = (donationsData || []).reduce((acc: Record<string, any>, donation: any) => {
        acc[donation.order_id] = donation;
        return acc;
      }, {} as Record<string, any>);

      const santaBookingsByOrderId = (santaBookingsData || []).reduce((acc: Record<string, any>, booking: any) => {
        acc[booking.order_id] = booking;
        return acc;
      }, {} as Record<string, any>);

      // Format transactions for the dashboard with proper typing
      const formattedTransactions = (recentOrders || []).map((order: any) => {
        // Determine transaction type based on related data
        let transactionType: 'ticket' | 'donation' | 'santa' | 'unknown' = 'unknown';
        let items: any[] = [];
        let orderTotal = order.total;

        if (itemsByOrderId[order.id]?.length) {
          transactionType = 'ticket';
          items = itemsByOrderId[order.id].map((item: any) => ({
            id: item.id,
            type: 'ticket',
            name: item.ticket?.name || `Ticket ${item.ticket_id?.substring(0, 6) || ''}`,
            description: item.ticket?.description || '',
            quantity: item.quantity,
            price: item.price,
            selectedDay: item.selected_day
          }));
        } else if (donationsByOrderId[order.id]) {
          const donation = donationsByOrderId[order.id] as any;
          transactionType = 'donation';
          items = [{
            id: donation.id,
            type: 'donation',
            name: 'Charity Donation',
            description: donation.metadata?.campaign || 'General Donation',
            quantity: 1,
            price: donation.amount,
            isRecurring: donation.metadata?.is_recurring || false
          }];
        } else if (santaBookingsByOrderId[order.id]) {
          const booking = santaBookingsByOrderId[order.id] as any;
          transactionType = 'santa';
          items = [{
            id: booking.id,
            type: 'santa_booking',
            name: `Santa Visit - ${booking.day}`,
            description: `Time: ${booking.time_range} | ${booking.num_children} children`,
            quantity: 1,
            price: order.total, // Santa bookings store price in the order total
            contact: booking.customer_name,
            email: booking.customer_email
          }];
        }

        return {
          id: order.id,
          email: items[0]?.email || order.email || '', // Use booking email if available
          name: items[0]?.contact || order.name || 'Guest',
          amount: order.total,
          status: order.status || (order.redeemed ? 'completed' : 'pending'),
          timestamp: order.created_at,
          transactionType,
          items,
          // Add type-specific metadata
          metadata: transactionType === 'santa' ? {
            day: santaBookingsByOrderId[order.id]?.day,
            time: santaBookingsByOrderId[order.id]?.time_range,
            numChildren: santaBookingsByOrderId[order.id]?.num_children
          } : undefined
        };
      });

      // Format the response data
      const result: DashboardMetrics = {
        totalRevenue: Math.round(totalRevenue * 100) / 100, // Round to 2 decimal places
        ticketsSold,
        activeUsers: Number(activeUsers),
        conversionRate: Math.round(Number(conversionRate) * 10) / 10, // Round to 1 decimal place
        revenueByDay: Object.entries(revenueByDay || {}).map(([date, revenue]) => ({
          date,
          revenue: Math.round(Number(revenue) * 100) / 100 // Round to 2 decimal places
        })),
        ticketsByType: Object.entries(ticketsByType || {}).map(([type, count]) => ({
          type,
          count: Number(count),
          revenue: Number(revenueByProductType[type])
        })),
        revenueByProductType: Object.entries(revenueByProductType || {}).map(([name, revenue]) => ({
          name,
          revenue: Math.round(revenue * 100) / 100 // Round to 2 decimal places
        })),
        averageOrderValue: ticketsSold > 0 ? Math.round((totalRevenue / ticketsSold) * 100) / 100 : 0,
        uniqueCustomers: Number(activeUsers),
        recentTransactions: formattedTransactions,
        totalCustomers: Number(totalCustomers),
        dateRange: {
          start: validOrderItems.length > 0
            ? new Date(Math.min(...validOrderItems.map((item: any) =>
              new Date(item.orders?.created_at || new Date()).getTime()
            )))
            : new Date(),
          end: new Date()
        }
      };

      return result;

    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard metrics');
    }
  }
);

export const fetchAdminUsers = createAsyncThunk(
  'admin/fetchAdminUsers',
  async (_, { rejectWithValue }) => {
    try {
      return await withSupabase(async (supabase) => {
        if (!supabase) {
          return rejectWithValue('Supabase client not available (SSR or missing ENV)');
        }

        // First get admin users
        const { data: adminUsers, error: adminError } = await supabase
          .from('AdminUsers')
          .select('*')
          .order('created_at', { ascending: false });

        if (adminError) throw adminError;

        // Then get their user details from auth
        const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

        if (authError) throw authError;

        // Combine the data
        return adminUsers.map(admin => {
          const authUser = authUsers.users.find(u => u.id === admin.user_id);
          return {
            id: admin.id,
            userId: admin.user_id,
            email: authUser?.email || 'Unknown',
            role: admin.role,
            createdAt: admin.created_at
          };
        });
      });
    } catch (error) {
      console.error('Error fetching admin users:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch admin users');
    }
  }
);

// User Management Thunks
export const fetchUsers = createAsyncThunk(
  'admin/fetchUsers',
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { admin: AdminState };
      const { page, pageSize, searchQuery, filters, sort } = state.admin.users;

      return await withSupabase(async (supabase) => {
        // Start with base query
        let query = supabase
          .from('AdminUsers')
          .select('*', { count: 'exact' });

        // Apply search
        if (searchQuery) {
          query = query.or(`email.ilike.%${searchQuery}%,name.ilike.%${searchQuery}%`);
        }

        // Apply filters
        if (filters.role !== 'all') {
          query = query.eq('role', filters.role);
        }

        if (filters.status !== 'all') {
          query = query.eq('status', filters.status);
        }

        // Apply sorting
        if (sort.field) {
          query = query.order(sort.field, { ascending: sort.direction === 'asc' });
        }

        // Apply pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        query = query.range(from, to);

        const { data, count, error } = await query;

        if (error) throw error;

        return {
          users: data || [],
          total: count || 0,
        };
      });
    } catch (error) {
      console.error('Error fetching users:', error);
      return rejectWithValue('Failed to fetch users');
    }
  }
);

export const createUser = createAsyncThunk(
  'admin/createUser',
  async (userData: {
    email: string;
    password: string;
    name?: string;
    role: AdminRole;
  }, { rejectWithValue }) => {
    try {
      return await withSupabase(async (supabase) => {
        if (!supabase) {
          return rejectWithValue('Supabase client not available (SSR or missing ENV)');
        }

        // First create auth user
        const { data: authData, error: authError } = await supabase.auth.signUp({
          email: userData.email,
          password: userData.password,
        });

        if (authError) throw authError;
        if (!authData.user) throw new Error('Failed to create user');

        // Then create admin user record
        const newUser = {
          id: uuidv4(),
          userId: authData.user.id,
          email: userData.email,
          name: userData.name,
          role: userData.role,
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const { error: dbError } = await supabase
          .from('AdminUsers')
          .insert([newUser]);

        if (dbError) throw dbError;

        return newUser;
      });
    } catch (error) {
      console.error('Error creating user:', error);
      return rejectWithValue('Failed to create user');
    }
  }
);

export const updateUserRole = createAsyncThunk(
  'admin/updateUserRole',
  async ({ userId, role }: { userId: string; role: AdminRole }, { rejectWithValue }) => {
    try {
      return await withSupabase(async (supabase) => {
        if (!supabase) {
          return rejectWithValue('Supabase client not available (SSR or missing ENV)');
        }

        const { data, error } = await supabase
          .from('AdminUsers')
          .update({
            role,
            updatedAt: new Date().toISOString()
          })
          .eq('id', userId)
          .select()
          .single();

        if (error) throw error;
        return data;
      });
    } catch (error) {
      console.error('Error updating user role:', error);
      return rejectWithValue('Failed to update user role');
    }
  }
);

export const deleteUser = createAsyncThunk(
  'admin/deleteUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      return await withSupabase(async (supabase) => {
        if (!supabase) {
          return rejectWithValue('Supabase client not available (SSR or missing ENV)');
        }

        // First delete the auth user
        const { data: userData, error: userError } = await supabase
          .from('AdminUsers')
          .select('userId')
          .eq('id', userId)
          .single();

        if (userError) throw userError;

        if (userData && userData?.userId) {
          const { error: authError } = await supabase.auth.admin.deleteUser(userData?.userId);
          if (authError) throw authError;
        }

        // Then delete the admin user record
        const { error: deleteError } = await supabase
          .from('AdminUsers')
          .delete()
          .eq('id', userId);

        if (deleteError) throw deleteError;

        return userId;
      });
    } catch (error) {
      console.error('Error deleting user:', error);
      return rejectWithValue('Failed to delete user');
    }
  }
);

// Create slice
const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    // User Management Reducers
    setUsersPage: (state, action: PayloadAction<number>) => {
      state.users.page = action.payload;
    },
    setUsersPageSize: (state, action: PayloadAction<number>) => {
      state.users.pageSize = action.payload;
      state.users.page = 1; // Reset to first page when page size changes
    },
    setUsersSearchQuery: (state, action: PayloadAction<string>) => {
      state.users.searchQuery = action.payload;
      state.users.page = 1; // Reset to first page when search query changes
    },
    setUsersFilter: (state, action: PayloadAction<{ key: string; value: string }>) => {
      const { key, value } = action.payload;
      if (key in state.users.filters) {
        (state.users.filters as any)[key] = value;
        state.users.page = 1; // Reset to first page when filters change
      }
    },
    setUsersSort: (state, action: PayloadAction<{ field: string; direction: 'asc' | 'desc' }>) => {
      state.users.sort = action.payload;
    },

    // Notification Reducers
    addNotification: (state: any, action: PayloadAction<{
      message: string;
      type: 'info' | 'warning' | 'error' | 'success';
    }>) => {
      const { message, type } = action.payload;
      state.notifications.data.unshift({
        id: Date.now().toString(),
        message,
        type,
        read: false,
        createdAt: new Date().toISOString()
      });
      state.notifications.unreadCount += 1;
    },
    markNotificationAsRead: (state: any, action: PayloadAction<string>) => {
      const notification = state.notifications.data.find((n: any) => n.id === action.payload);
      if (notification && !notification.read) {
        notification.read = true;
        state.notifications.unreadCount -= 1;
      }
    },
    markAllNotificationsAsRead: (state: any) => {
      console.log({ state })
      state.notifications.data.forEach((notification: any) => {
        notification.read = true;
      });
      state.notifications.unreadCount = 0;
    },
    clearNotifications: (state: any) => {
      state.notifications.data = [];
      state.notifications.unreadCount = 0;
    },
    setUserDemographicsTimeRange: (state, action: PayloadAction<'7days' | '30days' | '90days' | 'year'>) => {
      state.userDemographics.timeRange = action.payload;
    },
    setUserBehaviorTimeRange: (state, action: PayloadAction<'7days' | '30days' | '90days' | 'year'>) => {
      state.userBehavior.timeRange = action.payload;
    },

    // Real-time update handlers
    handleRealtimeTransactionInsert: (state, action: PayloadAction<{ table: string; record: any }>) => {
      const { table, record } = action.payload;

      if (table === 'Orders') {
        // Add new order to transactions list
        const newTransaction: Transaction = {
          id: record.id,
          email: record.email,
          name: record.name,
          total: record.total,
          payment_intent_id: record.payment_intent_id,
          created_at: record.created_at,
          status: record.status || 'pending',
          items: [], // Will be populated by separate query if needed
          transaction_type: 'ticket'
        };

        state.transactions.data.unshift(newTransaction);
        state.transactions.totalCount += 1;

        // Update metrics
        if (record.status === 'paid') {
          state.transactions.totalRevenue += record.total;
        }
      } else if (table === 'VendorStands') {
        // Add new vendor booking to transactions list
        const newTransaction: Transaction = {
          id: record.id,
          email: record.email,
          name: record.contact_name,
          total: record.price,
          payment_intent_id: record.payment_intent_id || '',
          created_at: record.created_at,
          status: record.payment_status || 'pending',
          items: [],
          transaction_type: 'vendor_stand',
          stand_number: record.stand_number,
          business_name: record.business_name,
          contact_name: record.contact_name,
          phone: record.phone
        };

        state.transactions.data.unshift(newTransaction);
        state.transactions.totalCount += 1;

        // Update metrics
        if (record.payment_status === 'paid') {
          state.transactions.totalRevenue += record.price;
        }
      } else if (table === 'CharityDonations') {
        // Add new donation to transactions list
        const newTransaction: Transaction = {
          id: record.id,
          email: record.order_id ? 'N/A' : 'Direct Donation', // Donations might not have direct email
          name: 'Charity Donation',
          total: record.amount,
          payment_intent_id: record.payment_intent_id || '',
          created_at: record.created_at,
          status: record.status || 'pending',
          items: [],
          transaction_type: 'donation',
          donation_campaign: 'General',
          multiplier: record.multiplier,
          original_amount: record.original_amount
        };

        state.transactions.data.unshift(newTransaction);
        state.transactions.totalCount += 1;

        // Update metrics
        if (record.status === 'completed') {
          state.transactions.totalRevenue += record.amount;
        }
      }
    },

    handleRealtimeTransactionUpdate: (state, action: PayloadAction<{ table: string; newRecord: any; oldRecord: any }>) => {
      const { table, newRecord, oldRecord } = action.payload;

      // Find and update existing transaction
      const transactionIndex = state.transactions.data.findIndex(t => t.id === newRecord.id);

      if (transactionIndex !== -1) {
        const existingTransaction = state.transactions.data[transactionIndex];

        if (table === 'Orders') {
          existingTransaction.status = newRecord.status || existingTransaction.status;
          existingTransaction.total = newRecord.total || existingTransaction.total;

          // Update revenue if status changed to paid
          if (oldRecord.status !== 'paid' && newRecord.status === 'paid') {
            state.transactions.totalRevenue += newRecord.total;
          }
        } else if (table === 'VendorStands') {
          existingTransaction.status = newRecord.payment_status || existingTransaction.status;
          existingTransaction.total = newRecord.price || existingTransaction.total;

          // Update revenue if payment status changed to paid
          if (oldRecord.payment_status !== 'paid' && newRecord.payment_status === 'paid') {
            state.transactions.totalRevenue += newRecord.price;
          }
        } else if (table === 'CharityDonations') {
          existingTransaction.status = newRecord.status || existingTransaction.status;
          existingTransaction.total = newRecord.amount || existingTransaction.total;

          // Update revenue if status changed to completed
          if (oldRecord.status !== 'completed' && newRecord.status === 'completed') {
            state.transactions.totalRevenue += newRecord.amount;
          }
        }

        // Mark as updated for UI highlighting
        existingTransaction.updated_at = new Date().toISOString();
      }
    },

    handleRealtimeTransactionDelete: (state, action: PayloadAction<{ table: string; record: any }>) => {
      const { record } = action.payload;

      // Remove transaction from list
      const transactionIndex = state.transactions.data.findIndex(t => t.id === record.id);

      if (transactionIndex !== -1) {
        const removedTransaction = state.transactions.data[transactionIndex];
        state.transactions.data.splice(transactionIndex, 1);
        state.transactions.totalCount -= 1;

        // Update revenue if it was a paid transaction
        if (removedTransaction.status === 'success' || removedTransaction.status === 'paid') {
          state.transactions.totalRevenue -= removedTransaction.total;
        }
      }
    },

    // Real-time dashboard metrics updates
    updateDashboardMetricsRealtime: (state, action: PayloadAction<{ table: string; record: any; type: 'insert' | 'update' | 'delete'; oldRecord?: any }>) => {
      const { table, record, type, oldRecord } = action.payload;

      if (!state.dashboard.metrics) return;

      if (table === 'Orders') {
        if (type === 'insert') {
          // New order
          if (record.status === 'success' || record.status === 'paid') {
            state.dashboard.metrics.totalRevenue += record.total;
            state.dashboard.metrics.ticketsSold += 1;
          }
        } else if (type === 'update' && oldRecord) {
          // Order status changed
          const wasSuccessful = oldRecord.status === 'success' || oldRecord.status === 'paid';
          const isSuccessful = record.status === 'success' || record.status === 'paid';

          if (!wasSuccessful && isSuccessful) {
            // Order became successful
            state.dashboard.metrics.totalRevenue += record.total;
            state.dashboard.metrics.ticketsSold += 1;
          } else if (wasSuccessful && !isSuccessful) {
            // Order became unsuccessful
            state.dashboard.metrics.totalRevenue -= oldRecord.total;
            state.dashboard.metrics.ticketsSold -= 1;
          } else if (wasSuccessful && isSuccessful && record.total !== oldRecord.total) {
            // Order amount changed
            state.dashboard.metrics.totalRevenue += (record.total - oldRecord.total);
          }
        } else if (type === 'delete') {
          // Order deleted
          if (record.status === 'success' || record.status === 'paid') {
            state.dashboard.metrics.totalRevenue -= record.total;
            state.dashboard.metrics.ticketsSold -= 1;
          }
        }
      } else if (table === 'VendorStands') {
        if (type === 'insert') {
          // New vendor booking
          if (record.payment_status === 'paid') {
            state.dashboard.metrics.totalRevenue += record.price;
          }
        } else if (type === 'update' && oldRecord) {
          // Vendor booking status changed
          const wasSuccessful = oldRecord.payment_status === 'paid';
          const isSuccessful = record.payment_status === 'paid';

          if (!wasSuccessful && isSuccessful) {
            // Booking became successful
            state.dashboard.metrics.totalRevenue += record.price;
          } else if (wasSuccessful && !isSuccessful) {
            // Booking became unsuccessful
            state.dashboard.metrics.totalRevenue -= oldRecord.price;
          } else if (wasSuccessful && isSuccessful && record.price !== oldRecord.price) {
            // Booking amount changed
            state.dashboard.metrics.totalRevenue += (record.price - oldRecord.price);
          }
        } else if (type === 'delete') {
          // Vendor booking deleted
          if (record.payment_status === 'paid') {
            state.dashboard.metrics.totalRevenue -= record.price;
          }
        }
      } else if (table === 'CharityDonations') {
        if (type === 'insert') {
          // New donation
          if (record.status === 'completed') {
            state.dashboard.metrics.totalRevenue += record.amount;
          }
        } else if (type === 'update' && oldRecord) {
          // Donation status changed
          const wasSuccessful = oldRecord.status === 'completed';
          const isSuccessful = record.status === 'completed';

          if (!wasSuccessful && isSuccessful) {
            // Donation became successful
            state.dashboard.metrics.totalRevenue += record.amount;
          } else if (wasSuccessful && !isSuccessful) {
            // Donation became unsuccessful
            state.dashboard.metrics.totalRevenue -= oldRecord.amount;
          } else if (wasSuccessful && isSuccessful && record.amount !== oldRecord.amount) {
            // Donation amount changed
            state.dashboard.metrics.totalRevenue += (record.amount - oldRecord.amount);
          }
        } else if (type === 'delete') {
          // Donation deleted
          if (record.status === 'completed') {
            state.dashboard.metrics.totalRevenue -= record.amount;
          }
        }
      }

      // Update average order value
      if (state.dashboard.metrics.ticketsSold > 0) {
        state.dashboard.metrics.averageOrderValue = state.dashboard.metrics.totalRevenue / state.dashboard.metrics.ticketsSold;
      }

      // Mark dashboard as updated
      state.dashboard.lastUpdated = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    // Handle fetchDonationMetrics
    builder
      .addCase(fetchDonationMetrics.pending, (state) => {
        state.donations.loading = true;
        state.donations.error = null;
      })
      .addCase(fetchDonationMetrics.fulfilled, (state, action) => {
        state.donations.loading = false;
        state.donations.data = action.payload as DonationMetrics;
      })
      .addCase(fetchDonationMetrics.rejected, (state, action) => {
        state.donations.loading = false;
        state.donations.error = action.payload as string;
      })

      // Handle fetchDashboardMetrics
      .addCase(fetchDashboardMetrics.pending, (state) => {
        state.metrics.loading = true;
        state.metrics.error = null;
      })
      .addCase(fetchDashboardMetrics.fulfilled, (state, action) => {
        state.metrics.loading = false;
        state.metrics.data = action.payload;
      })
      .addCase(fetchDashboardMetrics.rejected, (state, action) => {
        state.metrics.loading = false;
        state.metrics.error = action.payload as string;
      })

      // User Demographics
      .addCase(fetchUserDemographics.pending, (state) => {
        state.userDemographics.loading = true;
        state.userDemographics.error = null;
      })
      .addCase(fetchUserDemographics.fulfilled, (state, action) => {
        state.userDemographics.loading = false;
        state.userDemographics.data = action.payload;
      })
      .addCase(fetchUserDemographics.rejected, (state, action) => {
        state.userDemographics.loading = false;
        state.userDemographics.error = action.payload as string;
      })

      // User Behavior
      .addCase(fetchUserBehavior.pending, (state) => {
        state.userBehavior.loading = true;
        state.userBehavior.error = null;
      })
      .addCase(fetchUserBehavior.fulfilled, (state, action) => {
        state.userBehavior.loading = false;
        state.userBehavior.data = action.payload;
      })
      .addCase(fetchUserBehavior.rejected, (state, action) => {
        state.userBehavior.loading = false;
        state.userBehavior.error = action.payload as string;
      });

    // Transactions
    builder.addCase(fetchTransactions.pending, (state) => {
      state.transactions.loading = true;
      state.transactions.error = null;
    });
    builder.addCase(fetchTransactions.fulfilled, (state, action) => {
      state.transactions.loading = false;
      state.transactions.data = action.payload.data;
      state.transactions.page = action.payload.page;
      state.transactions.totalPages = action.payload.totalPages;
      state.transactions.count = action.payload.count;
    });
    builder.addCase(fetchTransactions.rejected, (state, action) => {
      state.transactions.loading = false;
      state.transactions.error = action.payload as string;
    });

    // Users Management
    builder.addCase(fetchUsers.pending, (state) => {
      state.users.loading = true;
      state.users.error = null;
    });
    builder.addCase(fetchUsers.fulfilled, (state: any, action) => {
      state.users.loading = false;
      state.users.data = action.payload.users;
      state.users.total = action.payload.total;
    });
    builder.addCase(fetchUsers.rejected, (state, action) => {
      state.users.loading = false;
      state.users.error = action.payload as string;
    });

    // Create User
    builder.addCase(createUser.pending, (state) => {
      state.users.loading = true;
      state.users.error = null;
    });
    builder.addCase(createUser.fulfilled, (state: any, action) => {
      state.users.loading = false;
      state.users.data = [...state.users.data, action.payload];
      state.users.total += 1;
    });
    builder.addCase(createUser.rejected, (state, action) => {
      state.users.loading = false;
      state.users.error = action.payload as string;
    });

    // Update User Role
    builder.addCase(updateUserRole.pending, (state) => {
      state.users.loading = true;
      state.users.error = null;
    });
    builder.addCase(updateUserRole.fulfilled, (state, action) => {
      state.users.loading = false;
      state.users.data = state.users.data.map(user =>
        user.id === action.payload.id ? { ...user, ...action.payload } : user
      );
    });
    builder.addCase(updateUserRole.rejected, (state, action) => {
      state.users.loading = false;
      state.users.error = action.payload as string;
    });

    // Vendor Stalls
    builder.addCase(fetchVendorStallMetrics.pending, (state) => {
      state.vendorStalls.loading = true;
      state.vendorStalls.error = null;
    });
    builder.addCase(fetchVendorStallMetrics.fulfilled, (state, action) => {
      state.vendorStalls.loading = false;
      state.vendorStalls.data = action.payload;
    });
    builder.addCase(fetchVendorStallMetrics.rejected, (state, action) => {
      state.vendorStalls.loading = false;
      state.vendorStalls.error = action.payload as string;

      // Add error notification
      state.donations.notifications.data.unshift({
        id: uuidv4(),
        message: `Failed to load vendor stall metrics: ${action.payload}`,
        type: 'error',
        read: false,
        createdAt: new Date().toISOString(),
      });
      state.donations.notifications.unreadCount += 1;
    });

    // Delete User
    builder.addCase(deleteUser.pending, (state) => {
      state.users.loading = true;
      state.users.error = null;
    });
    builder.addCase(deleteUser.fulfilled, (state, action) => {
      state.users.loading = false;
      state.users.data = state.users.data.filter(user => user.id !== action.payload);
      state.users.total = Math.max(0, state.users.total - 1);
    });
    builder.addCase(deleteUser.rejected, (state, action) => {
      state.users.loading = false;
      state.users.error = action.payload as string;
    });
  },
});

// Interface for the donation data from the database
interface DonationFromDB {
  id: string;
  error: boolean;
  order_id: string;
  amount: number;
  created_at: string;
  metadata: {
    donor_name?: string;
    donor_email?: string;
    donation_date?: string;
    multiplier_info?: any;
    campaign?: string;
    is_recurring?: boolean;
  };
  multiplier?: number;
  original_amount?: number;
}

// Process events for page views with proper typing
interface PageViewData {
  date: string;
  views: number;
  users: Set<string>;
  sessions: Set<string>;
}

// Get top pages with proper typing
interface PageStats {
  path: string;
  title: string;
  views: number;
  users: Set<string>;
  sessions: Set<string>;
  totalTime: number;
  bounces: number;
}

interface SessionData {
  start: Date;
  last: Date;
  pageCount: number;
}

interface DeviceAnalytics {
  byType: Record<string, { type: string; count: number }>;
  byBrowser: Record<string, { browser: string; count: number }>;
  byOS: Record<string, { os: string; count: number }>;
  byScreenSize: Record<string, { size: string; count: number }>;
  byDeviceType: Record<string, number>;
  total: number;
}

// User Analytics Thunks
export const fetchUserDemographics = createAsyncThunk<
  UserDemographics,
  { timeRange?: '7days' | '30days' | '90days' | 'year' },
  { rejectValue: string }
>('admin/fetchUserDemographics', async ({ timeRange = '30days' }, { rejectWithValue }) => {
  const supabase = getSupabaseClient();
  if (!supabase) {
    return rejectWithValue('Supabase client not available (SSR or missing ENV)');
  }

  try {
    // Calculate date range
    const now = new Date();
    const fromDate = new Date();

    switch (timeRange) {
      case '7days':
        fromDate.setDate(now.getDate() - 7);
        break;
      case '90days':
        fromDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        fromDate.setFullYear(now.getFullYear() - 1);
        break;
      case '30days':
      default:
        fromDate.setDate(now.getDate() - 30);
        break;
    }

    // 1. Get unique users count
    const { count: totalUsers, error: countError } = await supabase
      .from('AnalyticsEvents')
      .select('user_id', { count: 'exact', head: true })
      .not('user_id', 'is', null);

    if (countError) throw countError;

    // 2. Get device distribution from user agents
    const { data: userAgents, error: userAgentError } = await supabase
      .from('AnalyticsEvents')
      .select('user_agent')
      .not('user_agent', 'is', null);

    if (userAgentError) throw userAgentError;

    // Process user agents to get device types
    const deviceCounts = userAgents.reduce((acc, { user_agent }: any) => {
      const ua = user_agent?.toLowerCase() || '';
      if (/(tablet|ipad|playbook|silk)|(android(?!.*mobile))/i.test(ua)) {
        acc.tablet++;
      } else if (/mobile|android|iphone|ipod|phone|blackberry|iemobile|kindle|silk-accelerated|(android.*mobile)/i.test(ua)) {
        acc.mobile++;
      } else {
        acc.desktop++;
      }
      return acc;
    }, { mobile: 0, tablet: 0, desktop: 0 });

    const totalDevices = Object.values(deviceCounts).reduce((a, b) => a + b, 0);

    // 3. Get country data (simplified - in production, use proper IP geolocation)
    // This is a placeholder - you'd typically use an IP geolocation service
    const countryData = [
      { id: 'US', name: 'United States', users: Math.floor((totalUsers || 0) * 0.4), percent: 40 },
      { id: 'GB', name: 'United Kingdom', users: Math.floor((totalUsers || 0) * 0.3), percent: 30 },
      { id: 'CA', name: 'Canada', users: Math.floor((totalUsers || 0) * 0.1), percent: 10 },
      { id: 'AU', name: 'Australia', users: Math.floor((totalUsers || 0) * 0.05), percent: 5 },
      {
        id: 'other',
        name: 'Other',
        users: (totalUsers || 0) - Math.floor((totalUsers || 0) * 0.85),
        percent: 15
      },
    ];

    // 4. Prepare response data
    const result: UserDemographics = {
      totalUsers: totalUsers || 0,
      topCountries: countryData.filter(c => c.users > 0),
      ageGroups: [
        { id: '13-17', label: '13-17', value: 15, count: Math.floor((totalUsers || 0) * 0.15) },
        { id: '18-24', label: '18-24', value: 25, count: Math.floor((totalUsers || 0) * 0.25) },
        { id: '25-34', label: '25-34', value: 30, count: Math.floor((totalUsers || 0) * 0.3) },
        { id: '35-44', label: '35-44', value: 15, count: Math.floor((totalUsers || 0) * 0.15) },
        { id: '45-54', label: '45-54', value: 8, count: Math.floor((totalUsers || 0) * 0.08) },
        { id: '55-64', label: '55-64', value: 5, count: Math.floor((totalUsers || 0) * 0.05) },
        { id: '65+', label: '65+', value: 2, count: Math.floor((totalUsers || 0) * 0.02) },
      ],
      devices: [
        {
          id: 'mobile',
          label: 'Mobile',
          value: totalDevices ? Math.round((deviceCounts.mobile / totalDevices) * 100) : 60,
          count: deviceCounts.mobile || 0
        },
        {
          id: 'desktop',
          label: 'Desktop',
          value: totalDevices ? Math.round((deviceCounts.desktop / totalDevices) * 100) : 30,
          count: deviceCounts.desktop || 0
        },
        {
          id: 'tablet',
          label: 'Tablet',
          value: totalDevices ? Math.round((deviceCounts.tablet / totalDevices) * 100) : 10,
          count: deviceCounts.tablet || 0
        },
      ],
      // Note: Gender distribution requires user profile data
      // This is a placeholder - in a real app, you'd get this from user profiles
      genderDistribution: [
        { gender: 'Male', count: Math.floor((totalUsers || 0) * 0.45), percent: 45 },
        { gender: 'Female', count: Math.floor((totalUsers || 0) * 0.5), percent: 50 },
        { gender: 'Other', count: Math.max(1, (totalUsers || 0) - Math.floor((totalUsers || 0) * 0.95)), percent: 5 },
      ],
      // Get new vs returning users from analytics events
      newVsReturning: await (async () => {
        try {
          // Get first-time visitors (events with 'first_visit' type)
          const { count: newUsers = 0 } = await supabase
            .from('AnalyticsEvents')
            .select('*', { count: 'exact', head: true })
            .eq('event_type', 'first_visit')
            .gte('created_at', fromDate.toISOString());

          const returningUsers = Math.max(0, (totalUsers || 0) - (newUsers || 0));

          return {
            newUsers: newUsers || 0,
            returningUsers
          };
        } catch (error) {
          console.error('Error calculating new vs returning users:', error);
          // Fallback to estimated values if there's an error
          return {
            newUsers: Math.floor((totalUsers || 0) * 0.4),
            returningUsers: Math.floor((totalUsers || 0) * 0.6)
          };
        }
      })(),
    };

    return result;
  } catch (error) {
    console.error('Error fetching user demographics:', error);
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user demographics');
  }
});

export const fetchUserBehavior = createAsyncThunk<
  UserBehaviorMetrics,
  { timeRange: '7days' | '30days' | '90days' | 'year' },
  { rejectValue: string }
>('admin/fetchUserBehavior', async ({ timeRange = '30days' }, { rejectWithValue }) => {
  const supabase = getSupabaseClient();
  if (!supabase) {
    return rejectWithValue('Supabase client not available (SSR or missing ENV)');
  }

  try {
    const now = new Date();
    const fromDate = new Date();

    switch (timeRange) {
      case '7days':
        fromDate.setDate(now.getDate() - 7);
        break;
      case '90days':
        fromDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        fromDate.setFullYear(now.getFullYear() - 1);
        break;
      case '30days':
      default:
        fromDate.setDate(now.getDate() - 30);
        break;
    }

    const pageSize = 1000;
    let allEvents: any[] = [];
    let start = 0;
    let hasMore = true;

    while (hasMore) {
      const { data: events, error: eventsError } = await supabase
        .from('AnalyticsEvents')
        .select('*')
        .gte('created_at', fromDate.toISOString())
        //.not('device_type', 'is', null)
        .range(start, start + pageSize - 1);

      if (eventsError) throw eventsError;

      if (events && events.length > 0) {
        allEvents = [...allEvents, ...events];
        start += pageSize;
        hasMore = events.length === pageSize;
      } else {
        hasMore = false;
      }
    }

    const events = allEvents;
    // subset with device data for device analytics
    const eventsWithDevice = events.filter((e: any) => e.device_type);

    const pageViews = events
      .filter((e: any) => e.event_type === 'page_view')
      .reduce((acc: any, event: any) => {
        const eventDate = new Date(event.created_at);
        if (isNaN(eventDate.getTime())) return acc;

        const date = eventDate.toISOString().split('T')[0];
        if (!acc[date]) {
          acc[date] = {
            date,
            views: 0,
            users: new Set(),
            sessions: new Set()
          };
        }
        const dayData = acc[date];
        dayData.views++;
        if (event.user_id) dayData.users.add(event.user_id);
        if (event.session_id) dayData.sessions.add(event.session_id);
        return acc;
      }, {});

    const pageViewsArray: any[] = Object.values(pageViews).map((pv: any) => ({
      date: pv.date,
      views: pv.views,
      users: pv.users.size,
      sessions: pv.sessions.size,
      uniqueVisitors: pv.users.size,
      bounceRate: 0,
      activityData: [],
      behaviorData: {},
    }));

    const pageStats = events
      .filter((e: any) => e.event_type === 'page_view')
      .reduce((acc: any, event: any) => {
        const path = event.page_path || '/';
        if (!acc[path]) {
          acc[path] = {
            path,
            title: path.split('/').pop() || 'Home',
            views: 0,
            users: new Set(),
            sessions: new Set(),
            totalTime: 0,
            bounces: 0,
          };
        }
        const pageData = acc[path];
        pageData.views++;
        if (event.user_id) pageData.users.add(event.user_id);
        if (event.session_id) pageData.sessions.add(event.session_id);
        pageData.totalTime += 30;
        return acc;
      }, {});

    const topPages = Object.values(pageStats)
      .map((p: any) => ({
        path: p.path,
        title: p.title,
        views: p.views,
        users: p.users.size,
        avgTimeOnPage: p.views > 0 ? Math.round(p.totalTime / p.views) : 0,
        bounceRate: p.sessions.size > 0 ? Math.round((p.bounces / p.sessions.size) * 100) : 0,
      }))
      .sort((a, b) => b.views - a.views)
      .slice(0, 10);

    const daysInRange = Math.floor((now.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const dateLabels = Array.from({ length: daysInRange }, (_, i) => {
      const d = new Date(fromDate);
      d.setDate(d.getDate() + i);
      return d.toISOString().split('T')[0];
    });

    const userActivity = {
      labels: dateLabels,
      activeUsers: dateLabels.map((date: any) => {
        const dayEvents = events.filter((e: any) => e.created_at.startsWith(date));
        return new Set(dayEvents.map(e => e.user_id || e.session_id)).size;
      }),
      newUsers: dateLabels.map((date: any) => {
        const dayEvents = events.filter((e: any) => e.created_at.startsWith(date));
        return Math.floor(dayEvents.length * 0.3);
      }),
      sessions: dateLabels.map(date => {
        const dayEvents = events.filter((e) => e.created_at.startsWith(date));
        return new Set(dayEvents.map(e => e.session_id)).size;
      }),
      returningUsers: dateLabels.map((date: any) => {
        const dayEvents = events.filter((e: any) => e.created_at.startsWith(date));
        const activeUsers = new Set(dayEvents.map(e => e.user_id || e.session_id)).size;
        const newUsers = Math.floor(dayEvents.length * 0.3);
        return Math.max(0, activeUsers - newUsers);
      }),
    };

    const sessions = new Map();
    events.forEach((event) => {
      if (!event.session_id) return;

      const eventTime = new Date(event.created_at);
      if (isNaN(eventTime.getTime())) return;

      const sessionId = String(event.session_id);
      const existingSession = sessions.get(sessionId);

      if (existingSession) {
        existingSession.last = eventTime;
        existingSession.pageCount++;
      } else {
        sessions.set(sessionId, {
          start: eventTime,
          last: eventTime,
          pageCount: 1
        });
      }
    });

    const sessionDurations = Array.from(sessions.values())
      .map((s: any) => (s.last.getTime() - s.start.getTime()) / 1000);
    const totalSessions = sessionDurations.length;
    const avgSessionDuration = totalSessions > 0
      ? Math.round(sessionDurations.reduce((a, b) => a + b, 0) / totalSessions)
      : 0;

    const avgPagesPerSession = totalSessions > 0
      ? Math.round(events.filter((e) => e.event_type === 'page_view').length / totalSessions * 10) / 10
      : 0;

    const bounceSessions = Array.from(sessions.values())
      .filter(s => s.pageCount === 1).length;
    const bounceRate = totalSessions > 0
      ? Math.round((bounceSessions / totalSessions) * 100)
      : 0;

    const deviceAnalytics: DeviceAnalytics = eventsWithDevice.reduce<DeviceAnalytics>((acc, event) => {
      const deviceType = event.device_type?.toLowerCase() || 'unknown';
      if (!acc.byType[deviceType]) {
        acc.byType[deviceType] = { type: deviceType, count: 0 };
      }
      acc.byType[deviceType].count++;

      if (event.browser) {
        const browser = event.browser;
        if (!acc.byBrowser[browser]) {
          acc.byBrowser[browser] = { browser, count: 0 };
        }
        acc.byBrowser[browser].count++;
      }

      if (event.os) {
        const os = event.os;
        if (!acc.byOS[os]) {
          acc.byOS[os] = { os, count: 0 };
        }
        acc.byOS[os].count++;
      }

      if (event.viewport_size) {
        const [width] = event.viewport_size.split('x').map(Number);
        let sizeCategory = 'desktop';
        if (width < 768) sizeCategory = 'mobile';
        else if (width < 1024) sizeCategory = 'tablet';

        if (!acc.byScreenSize[sizeCategory]) {
          acc.byScreenSize[sizeCategory] = { size: sizeCategory, count: 0 };
        }
        acc.byScreenSize[sizeCategory].count++;
      }

      acc.total++;
      return acc;
    }, {
      byType: {},
      byBrowser: {},
      byOS: {},
      byScreenSize: {},
      total: 0
    });

    const deviceTypesArray = Object.entries(deviceAnalytics.byType)
      .map(([type, { count }]: any) => ({
        id: type.toLowerCase(),
        label: type,
        value: count,
        count,
        percent: deviceAnalytics.total ? Math.round((count / deviceAnalytics.total) * 100) : 0,
      }));

    const browserData = Object.entries(deviceAnalytics.byBrowser)
      .map(([browser, { count }]: any) => ({
        id: browser.toLowerCase().replace(/\s+/g, '-'),
        label: browser,
        value: count,
        count,
        percent: deviceAnalytics.total > 0 ? Math.round((count / deviceAnalytics.total) * 100) : 0,
      }))
      .sort((a, b) => b.count - a.count);

    const osData = Object.entries(deviceAnalytics.byOS)
      .map(([os, { count }]: any) => ({
        id: os.toLowerCase().replace(/\s+/g, '-'),
        label: os,
        value: count,
        count,
        percent: deviceAnalytics.total > 0 ? Math.round((count / deviceAnalytics.total) * 100) : 0,
      }))
      .sort((a, b) => b.count - a.count);

    const screenSizeData = Object.entries(deviceAnalytics.byScreenSize)
      .map(([size, { count }]: any) => ({
        id: size.toLowerCase().replace(/\s+/g, '-'),
        label: size,
        value: count,
        count,
        percent: deviceAnalytics.total > 0 ? Math.round((count / deviceAnalytics.total) * 100) : 0,
      }));

    // ---------------- Location analytics (country-level via ipapi.co) ----------------
    const countryCounts: Record<string, number> = {};
    const uniqueIps = [...new Set(events.map((e: any) => e.ip_address).filter(Boolean))];
    await Promise.all(uniqueIps.map(async ip => {
      try {
        const res = await fetch(`https://ipapi.co/${ip}/country/`);
        if (!res.ok) return;
        const country = (await res.text()).trim() || 'Unknown';
        countryCounts[country] = (countryCounts[country] ?? 0) + 1;
      } catch (_) {
        // ignore lookup failures
      }
    }));

    const locationData = Object.entries(countryCounts)
      .map(([country, count]) => ({
        id: country.toLowerCase(),
        label: country,
        value: count,
        count,
        percent: deviceAnalytics.total ? Math.round((count / deviceAnalytics.total) * 100) : 0,
      }))
      .sort((a, b) => (b.count as number) - (a.count as number));

    // --- Aggregate all events by type and date ---
    const eventTypes = [
      'page_view', 'first_visit', 'device_detected', 'user_demographics',
      'login_error', 'login_attempt', 'login_failed',
      'form_interaction', 'ui_interaction', 'error_occurred',
      'session_start', 'session_end'
    ];
    const eventsByTypeMap: Record<string, { [eventType: string]: number }> = {};
    events.forEach((event: any) => {
      const eventDate = new Date(event.created_at).toISOString().split('T')[0];
      if (!eventsByTypeMap[eventDate]) {
        eventsByTypeMap[eventDate] = {};
        eventTypes.forEach(type => { eventsByTypeMap[eventDate][type] = 0; });
      }
      if (eventTypes.includes(event.event_type)) {
        eventsByTypeMap[eventDate][event.event_type]++;
      }
    });
    const eventsByType = Object.entries(eventsByTypeMap)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([date, counts]) => ({ date, ...counts }));

    const result: UserBehaviorMetrics = {
      pageViews: pageViewsArray,
      topPages,
      userActivity: {
        labels: userActivity.labels.map(d => new Date(d).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
        activeUsers: userActivity.activeUsers,
        newUsers: userActivity.newUsers,
        sessions: userActivity.sessions,
        returningUsers: userActivity.returningUsers,
      },
      sessionMetrics: {
        avgSessionDuration,
        avgPagesPerSession,
        bounceRate,
      },
      deviceTypes: deviceTypesArray,
      deviceAnalytics: {
        browsers: browserData,
        operatingSystems: osData,
        screenSizes: screenSizeData,
      },
      locationAnalytics: locationData,
      eventsByType, // <-- add this line
    };

    return result;
  } catch (error) {
    console.error('Error fetching user behavior:', error);
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user behavior');
  }
}
);

export const fetchVendorStallMetrics = createAsyncThunk<
  VendorStallMetrics,
  { timeRange?: 'week' | 'month' | 'year' },
  { rejectValue: string }
>('admin/fetchVendorStallMetrics', async ({ timeRange = 'month' }, { rejectWithValue }) => {
  const supabase = getSupabaseClient();
  if (!supabase) {
    return rejectWithValue('Supabase client not available (SSR or missing ENV)');
  }

  try {
    // Calculate date range
    const now = new Date();
    const startDate = new Date(now);

    switch (timeRange) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    const startDateStr = startDate.toISOString();

    // Fetch vendor stands
    const { data: stands, error: standsError } = await supabase
      .from('VendorStands')
      .select('*')
      .gte('created_at', startDateStr)
      .order('created_at', { ascending: false });

    if (standsError) throw standsError;

    // Calculate metrics
    const totalStands = stands.length;
    const totalRevenue = stands.reduce((sum, stand) => sum + (stand.price || 0), 0);

    // Calculate addon revenue and discount savings
    let addonRevenue = 0;
    let discountSavings = 0;

    stands.forEach(stand => {
      // Calculate addon revenue
      if (stand.addons && Array.isArray(stand.addons)) {
        stand.addons.forEach((addon: VendorStandAddon) => {
          addonRevenue += (addon.price * (addon.quantity || 1));
        });
      }

      // Calculate discount savings
      if (stand.discount_applied && stand.original_price) {
        discountSavings += (stand.original_price - stand.price);
      }
    });

    // Group by business for top businesses
    const businesses = stands.reduce((acc: Record<string, {
      businessName: string;
      contactName: string;
      email: string;
      totalSpent: number;
      standsBooked: number;
    }>, stand) => {
      const businessKey = stand.email;
      if (!acc[businessKey]) {
        acc[businessKey] = {
          businessName: stand.business_name,
          contactName: stand.contact_name,
          email: stand.email,
          totalSpent: 0,
          standsBooked: 0,
        };
      }
      acc[businessKey].standsBooked += 1;
      acc[businessKey].totalSpent += stand.price || 0;
      return acc;
    }, {} as Record<string, {
      businessName: string;
      contactName: string;
      email: string;
      totalSpent: number;
      standsBooked: number;
    }>);

    const topBusinesses = Object.values(businesses)
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 5);

    // Group by day for revenue chart
    const revenueByDay = stands.reduce((acc: Record<string, { date: string; revenue: number; count: number }>, stand) => {
      const date = new Date(stand.created_at).toISOString().split('T')[0];
      if (!acc[date]) {
        acc[date] = { date, revenue: 0, count: 0 };
      }
      acc[date].revenue += stand.price || 0;
      acc[date].count += 1;
      return acc;
    }, {} as Record<string, { date: string; revenue: number; count: number }>);

    const formattedRevenueByDay = Object.values(revenueByDay).sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Get recent sales (last 10)
    const recentSales = stands.slice(0, 10).map(stand => ({
      ...stand,
      // Add any additional formatting if needed
    })) as unknown as VendorStandSale[];

    return {
      totalStands,
      totalRevenue,
      standsByType: [], // Not grouping by type for now
      recentSales,
      topBusinesses,
      revenueByDay: formattedRevenueByDay,
      addonRevenue,
      discountSavings,
      dateRange: {
        start: startDateStr,
        end: now.toISOString(),
      },
    };
  } catch (error) {
    console.error('Error fetching vendor stall metrics:', error);
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch vendor stall metrics');
  }
});

export const fetchDonationMetrics = createAsyncThunk<
  DonationMetrics, // Return type
  { timeRange?: 'week' | 'month' | 'year' }, // Args type
  { rejectValue: string }
>('admin/fetchDonationMetrics', async ({ timeRange = 'month' }, { rejectWithValue }) => {
  const supabase = getSupabaseClient();
  if (!supabase) {
    return rejectWithValue('Supabase client not available (SSR or missing ENV)');
  }

  try {
    // Calculate date range based on timeRange
    const now = new Date();
    const startDate = new Date(now);

    switch (timeRange) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }

    // Format date for Supabase query
    const startDateStr = startDate.toISOString();

    // Get donations within the date range
    const { data: donations, error } = await supabase
      .from<DonationFromDB>('CharityDonations')
      .select('*')
      .gte('created_at', startDateStr)
      .order('created_at', { ascending: false });

    if (error) throw error;

    const { data: totalDonations, error: totalDonationsError } = await supabase
      .from<DonationFromDB>('totaldonations')
      .select('total_amount')
      .single();

    if (totalDonationsError) throw totalDonationsError;

    // Process donations
    const oneMonthAgo = new Date(now);
    oneMonthAgo.setMonth(now.getMonth() - 1);

    // Type guard to filter out null/undefined
    const validDonations = (donations || []).filter((d): d is DonationFromDB => !!d);

    // Calculate monthly donations
    const monthlyDonations = validDonations
      .filter(d => new Date(d.created_at) > oneMonthAgo)
      .reduce((sum, d) => sum + (d.amount || 0), 0);

    // Calculate recurring donations
    const recurringDonations = validDonations
      .filter(d => d.metadata?.is_recurring)
      .reduce((sum, d) => sum + (d.amount || 0), 0);

    // Group by campaign
    const campaignTotals: Record<string, number> = {};
    validDonations.forEach(d => {
      const campaign = d.metadata?.campaign || 'General Fund';
      campaignTotals[campaign] = (campaignTotals[campaign] || 0) + (d.amount || 0);
    });

    // Get top donors
    const donorMap: Record<string, { name: string; amount: number; email: string }> = {};
    validDonations.forEach(d => {
      const email = d.metadata?.donor_email || '<EMAIL>';
      const name = d.metadata?.donor_name || 'Anonymous';

      if (!donorMap[email]) {
        donorMap[email] = { name, amount: 0, email };
      }
      donorMap[email].amount += d.amount || 0;
    });

    const topDonors = Object.values(donorMap)
      .sort((a, b) => b.amount - a.amount)
      .slice(0, 5);

    // Calculate daily donations for the last 30 days
    const dailyDonations: Record<string, number> = {};
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Initialize last 30 days with 0
    for (let i = 0; i <= 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dailyDonations[dateStr] = 0;
    }

    // Sum donations by day
    validDonations
      .filter((d: any) => new Date(d.created_at) >= thirtyDaysAgo)
      .forEach((d: any) => {
        const dateStr = new Date(d.created_at).toISOString().split('T')[0];
        dailyDonations[dateStr] = (dailyDonations[dateStr] || 0) + (d.amount || 0);
      });

    // Convert to array for chart
    const dailyDonationsArray = Object.entries(dailyDonations)
      .map(([date, amount]) => ({ date, amount }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Map to the expected DonationMetrics type
    return {
      totalDonations: totalDonations.total_amount,
      monthlyDonations,
      recurringDonations,
      topDonors,
      dailyDonations: dailyDonationsArray,
      campaignTotals,
      recentDonations: validDonations.slice(0, 10).map(d => ({
        id: d.id,
        order_id: d.order_id,
        amount: d.amount,
        created_at: d.created_at,
        metadata: d.metadata,
        multiplier: d.multiplier || 1,
        original_amount: d.original_amount || d.amount
      }))
    };

  } catch (error) {
    console.error('Error fetching donation metrics:', error);
    const errorMessage = (error as any)?.message || 'Failed to fetch donation metrics';
    return rejectWithValue(errorMessage);
  }
});

// Export actions and reducer
export const {
  // User Management Actions
  setUsersPage,
  setUsersPageSize,
  setUsersSearchQuery,
  setUsersFilter,
  setUsersSort,

  // Notification Actions
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
  setUserDemographicsTimeRange,
  setUserBehaviorTimeRange,

  // Real-time actions
  handleRealtimeTransactionInsert,
  handleRealtimeTransactionUpdate,
  handleRealtimeTransactionDelete,
  updateDashboardMetricsRealtime,
} = adminSlice.actions;

export default adminSlice.reducer;