import { useSelector, useDispatch } from 'react-redux';
import {
  openFamilyInfo,
  closeFamilyInfo,
  openSantaInfo,
  closeSantaInfo,
  openCharityInfo,
  closeCharityInfo,
} from '@/store/slices/info-panels-slice';
import { RootState } from '@/store/store';

export const useInfoPanels = () => {
  const dispatch = useDispatch();
  const {
    isFamilyInfoOpen,
    isSantaInfoOpen,
    isCharityInfoOpen,
  } = useSelector((state: RootState) => state.infoPanels);

  return {
    isFamilyInfoOpen,
    isSantaInfoOpen,
    isCharityInfoOpen,
    openFamilyInfo: () => dispatch(openFamilyInfo()),
    closeFamilyInfo: () => dispatch(closeFamilyInfo()),
    openSantaInfo: () => dispatch(openSantaInfo()),
    closeSantaInfo: () => dispatch(closeSantaInfo()),
    openCharityInfo: () => dispatch(openCharityInfo()),
    closeCharityInfo: () => dispatch(closeCharityInfo()),
  };
};