import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { createClient } from '@supabase/supabase-js';
import * as bcrypt from 'bcryptjs';

// Types
export type AdminRole = 'super_admin' | 'finance_admin' | 'support_admin';

export type AuthResult = { success: boolean; error?: string };

export interface AdminUser {
  id: string;
  email: string;
  role: AdminRole;
  name: string;
  password_hash: string;
  created_at: string;
  updated_at: string; // Made required since we always set it
}

interface AdminAuthContextType {
  user: AdminUser | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<AuthResult>;
  logout: () => Promise<AuthResult>;
  isAuthenticated: boolean;
  hasPermission: (requiredRole: AdminRole | AdminRole[]) => boolean;
  updateProfile: (name: string) => Promise<AuthResult>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<AuthResult>;
}

// Create a single Supabase client for client-side use
let supabaseClient: ReturnType<typeof createClient> | null = null;

// Create a context for the Supabase client
const SupabaseContext = createContext<ReturnType<typeof createClient> | null>(null);

// Helper to safely access window.ENV
const getEnv = (key: string): string => {
  if (typeof window === 'undefined') return '';
  return (window as any).ENV?.[key] || '';
};

// Create a provider for the Supabase client
export function SupabaseProvider({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  // Initialize Supabase client only once
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    try {
      if (!supabaseClient) {
        const supabaseUrl = getEnv('SUPABASE_URL');
        const supabaseKey = getEnv('SUPABASE_KEY');

        if (!supabaseUrl || !supabaseKey) {
          throw new Error('Missing Supabase environment variables');
        }

        supabaseClient = createClient(supabaseUrl, supabaseKey, {
          auth: {
            persistSession: true,
            autoRefreshToken: true,
            detectSessionInUrl: true,
            storageKey: 'sb-auth-token',
            storage: window.localStorage
          }
        });
      }

      setMounted(true);
    } catch (error) {
      console.error('Failed to initialize Supabase client:', error);
      setMounted(true); // Still set mounted to true to avoid blocking the UI
    }
  }, []);

  // Only render children when mounted to prevent hydration issues
  if (typeof window === 'undefined' || !mounted) {
    return null;
  }

  return (
    <SupabaseContext.Provider value={supabaseClient}>
      {children}
    </SupabaseContext.Provider>
  );
};

// Custom hook to use the Supabase client
export function useSupabase() {
  const supabase = useContext(SupabaseContext);
  if (supabase === null) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return supabase;
};

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(undefined);

export function AdminAuthProvider({ children }: { children: ReactNode }) {
  const supabase = useSupabase();
  const [user, setUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  // Set mounted state on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Password utility functions
  const hashPassword = useCallback(async (password: string): Promise<string> => {
    const saltRounds = 10;
    return await bcrypt.hash(password, saltRounds);
  }, []);

  const verifyPassword = useCallback(async (password: string, hash: unknown): Promise<boolean> => {
    try {
      // Ensure hash is a non-empty string
      if (typeof hash !== 'string' || hash.trim() === '') {
        console.error('Invalid hash provided');
        return false;
      }
      return await bcrypt.compare(password, hash);
    } catch (error) {
      console.error('Password verification error:', error);
      return false;
    }
  }, []);

  // Check for existing session on mount
  useEffect(() => {
    if (!mounted || !supabase) return;

    const checkSession = async () => {
      try {
        setLoading(true);

        // Check for existing session in localStorage
        const storedUser = localStorage.getItem('adminUser');
        if (!storedUser) {
          setUser(null);
          return;
        }

        const userData = JSON.parse(storedUser) as AdminUser;

        // Verify the user still exists in the database
        const { data: adminUser, error } = await supabase
          .from('AdminUsers')
          .select('*')
          .eq('email', userData.email)
          .single();

        if (error || !adminUser) {
          throw new Error('User not found in database');
        }

        // Ensure all required fields are present
        const now = new Date().toISOString();
        const validUserData: AdminUser = {
          id: String(adminUser.id || ''),
          email: String(adminUser.email || ''),
          name: String(adminUser.name || 'Admin User'),
          role: (adminUser.role as AdminRole) || 'support_admin',
          password_hash: String(adminUser.password_hash || ''),
          created_at: String(adminUser.created_at || now),
          updated_at: String(adminUser.updated_at || now)
        };

        setUser(validUserData);
      } catch (error) {
        console.error('Session check error:', error);
        setUser(null);
        localStorage.removeItem('adminUser');
      } finally {
        setLoading(false);
      }
    };

    checkSession();
  }, [mounted, supabase]);


  const login = async (email: string, password: string): Promise<AuthResult> => {
    if (!supabase) {
      return { success: false, error: 'Supabase client not initialized' };
    }

    try {
      setLoading(true);

      // Basic validation
      if (!email || !password) {
        return { success: false, error: 'Email and password are required' };
      }

      // Check user in AdminUsers table
      const { data: adminUser, error: userError } = await supabase
        .from('AdminUsers')
        .select('*')
        .eq('email', email.trim())
        .single();

      if (userError || !adminUser) {
        console.error('User not found or error:', userError);
        return { success: false, error: 'Invalid email or password' };
      }

      // Verify user has required fields
      if (!adminUser.id || !adminUser.email || !adminUser.password) {
        console.error('Incomplete user data for:', adminUser.id);
        return { success: false, error: 'Authentication error. Please contact support.' };
      }

      // Verify password using bcrypt
      const passwordHash = adminUser.password;
      if (typeof passwordHash !== 'string' || passwordHash.trim() === '') {
        console.error('Invalid password hash format for user:', adminUser.id);
        return { success: false, error: 'Authentication error. Please contact support.' };
      }

      const isPasswordValid = await verifyPassword(password, passwordHash);

      if (!isPasswordValid) {
        console.error('Invalid password for user:', adminUser.id);
        return { success: false, error: 'Invalid email or password' };
      }

      // Create AdminUser object with all required fields
      const now = new Date().toISOString();
      const userData: AdminUser = {
        id: String(adminUser?.id ?? ''),
        email: String(adminUser?.email ?? ''),
        role: (adminUser?.role as AdminRole) || 'support_admin', // Default role if not specified
        name: String(adminUser?.name ?? 'Admin User'),
        password_hash: String(passwordHash ?? ''),
        created_at: String(adminUser?.created_at ?? now),
        updated_at: String(adminUser?.updated_at ?? now)
      };

      try {
        setUser(userData);
        localStorage.setItem('adminUser', JSON.stringify(userData));
        return { success: true };
      } catch (storageError) {
        console.error('Failed to save user data:', storageError);
        return {
          success: false,
          error: 'Failed to save session. Please try again.'
        };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Login error:', errorMessage);
      return {
        success: false,
        error: 'An error occurred during login. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<AuthResult> => {
    try {
      setLoading(true);

      // Clear user data
      setUser(null);
      localStorage.removeItem('adminUser');

      return { success: true };
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (requiredRole: AdminRole | AdminRole[]): boolean => {
    if (!user) return false;

    // Super admin has access to everything
    if (user.role === 'super_admin') return true;

    // Check if user's role is in the required roles array
    if (Array.isArray(requiredRole)) {
      return requiredRole.includes(user.role);
    }

    // Check if user's role matches the required role
    return user.role === requiredRole;
  };

  // Check if user is authenticated
  const isAuthenticated = !!user;

  const updateProfile = async (name: string): Promise<AuthResult> => {
    if (!supabase || !user) {
      return { success: false, error: 'Not authenticated or Supabase client not initialized' };
    }

    try {
      setLoading(true);

      // Validate input
      if (!name || typeof name !== 'string' || name.trim() === '') {
        return { success: false, error: 'Name is required' };
      }

      const updatedAt = new Date().toISOString();
      const trimmedName = name.trim();

      // Update in AdminUsers table
      const { error: updateError } = await supabase
        .from('AdminUsers')
        .update({
          name: trimmedName,
          updated_at: updatedAt
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Failed to update profile:', updateError);
        return {
          success: false,
          error: 'Failed to update profile. Please try again.'
        };
      }

      // Update local state with all required fields
      const updatedUser: AdminUser = {
        id: user.id,
        email: user.email,
        role: user.role,
        name: trimmedName,
        password_hash: user.password_hash,
        created_at: user.created_at,
        updated_at: updatedAt
      };

      setUser(updatedUser);

      // Also update localStorage
      try {
        localStorage.setItem('adminUser', JSON.stringify(updatedUser));
      } catch (storageError) {
        console.error('Failed to update user in localStorage:', storageError);
        // Not critical, so we'll continue even if this fails
      }

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Error updating profile:', errorMessage);
      return {
        success: false,
        error: 'An error occurred while updating your profile. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string): Promise<AuthResult> => {
    if (!supabase || !user) {
      return { success: false, error: 'Not authenticated or Supabase client not initialized' };
    }

    try {
      setLoading(true);

      // Validate inputs
      if (!currentPassword || !newPassword) {
        return { success: false, error: 'Current password and new password are required' };
      }

      // First, verify current password
      const { data: adminUser, error: userError } = await supabase
        .from('AdminUsers')
        .select('password_hash')
        .eq('email', user.email)
        .single();

      if (userError || !adminUser) {
        return { success: false, error: 'User not found' };
      }

      // Verify current password
      const isCurrentPasswordValid = await verifyPassword(currentPassword, adminUser.password_hash);
      if (!isCurrentPasswordValid) {
        return { success: false, error: 'Current password is incorrect' };
      }

      // Hash the new password
      const hashedPassword = await hashPassword(newPassword);

      // Update the password in AdminUsers table
      const { error: updateError } = await supabase
        .from('AdminUsers')
        .update({
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('email', user.email);

      if (updateError) {
        console.error('Failed to update password:', updateError);
        return {
          success: false,
          error: 'Failed to update password. Please try again.'
        };
      }

      // Update local user state with new password hash
      setUser(prev => prev ? {
        ...prev,
        password_hash: hashedPassword,
        updated_at: new Date().toISOString()
      } : null);

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Error updating password:', errorMessage);
      return {
        success: false,
        error: 'An error occurred while updating your password. Please try again.'
      };
    } finally {
      setLoading(false);
    }
  };

  // Don't render children until mounted to prevent hydration issues
  if (!mounted) {
    // Optionally show a loading spinner or return null
    return null;
  }

  return (
    <AdminAuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        isAuthenticated,
        hasPermission,
        updateProfile,
        updatePassword
      }}
    >
      {children}
    </AdminAuthContext.Provider>
  );
};

export function useAdminAuth() {
  const context = useContext(AdminAuthContext);
  if (context === undefined) {
    throw new Error('useAdminAuth must be used within an AdminAuthProvider');
  }
  return context;
};