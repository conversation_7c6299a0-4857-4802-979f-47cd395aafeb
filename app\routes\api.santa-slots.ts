import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs, type ActionFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

// Get all available Santa slots
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { data: slots, error } = await supabase
      .from('SantaTimeSlots')
      .select('*')
      .order('day')
      .order('start_time');

    if (error) throw error;

    // Format the slots for easier consumption by the frontend
    const formattedSlots = slots.map(slot => ({
      id: slot.id,
      day: slot.day,
      startTime: slot.start_time,
      endTime: slot.end_time,
      available: slot.capacity - slot.booked,
      isAvailable: (slot.capacity - slot.booked) > 0
    }));

    return json({ slots: formattedSlots });
  } catch (error) {
    console.error('Error fetching Santa slots:', error);
    return json(
      { error: 'Failed to fetch Santa slots' },
      { status: 500 }
    );
  }
}

// Book a Santa slot
export async function action({ request }: ActionFunctionArgs) {
  try {
    const { slotId, orderId, customerEmail, customerName, numChildren = 1 } = await request.json();

    // Validate input
    if (!slotId || !orderId || !customerEmail || !customerName) {
      return json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if slot exists and has availability
    const { data: slot, error: slotError } = await supabase
      .from('SantaTimeSlots')
      .select('*')
      .eq('id', slotId)
      .single();

    if (slotError || !slot) {
      return json(
        { error: 'Slot not found' },
        { status: 404 }
      );
    }

    if (slot.booked >= slot.capacity) {
      return json(
        { error: 'Slot is fully booked' },
        { status: 400 }
      );
    }

    // Begin a transaction
    const { error: bookingError } = await supabase
      .from('SantaBookings')
      .insert({
        slot_id: slotId,
        order_id: orderId,
        customer_email: customerEmail,
        customer_name: customerName,
        num_children: numChildren
      });

    if (bookingError) throw bookingError;

    // Update the slot's booked count
    const { error: updateError } = await supabase
      .from('SantaTimeSlots')
      .update({ booked: slot.booked + 1 })
      .eq('id', slotId);

    if (updateError) throw updateError;

    return json({ success: true, message: 'Slot booked successfully' });
  } catch (error) {
    console.error('Error booking Santa slot:', error);
    return json(
      { error: 'Failed to book Santa slot' },
      { status: 500 }
    );
  }
}
