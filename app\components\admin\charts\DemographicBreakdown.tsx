import { useMemo, useState } from 'react';
import { ResponsiveBar } from '@nivo/bar';

interface DemographicCategory {
  id: string;
  label: string;
  value: number;
  percentage?: number;
  color?: string;
}

interface DemographicBreakdownProps {
  data: DemographicCategory[];
  title: string;
  type: 'age' | 'gender' | 'device' | 'custom';
  height?: number;
}

export function DemographicBreakdown({
  data,
  title,
  type,
  height = 300
}: DemographicBreakdownProps) {
  const [hoveredCategory, setHoveredCategory] = useState<DemographicCategory | null>(null);

  // Calculate total for percentages
  const total = data.reduce((sum, category) => sum + category.value, 0);

  // Calculate percentages and assign colors
  const categoriesWithPercentage = data.map((category, index) => {
    // Generate colors based on category type
    let color;
    if (category.color) {
      color = category.color;
    } else if (type === 'age') {
      // Blue gradient for age
      const intensity = 100 - (index * 10);
      color = `hsl(210, 100%, ${intensity}%)`;
    } else if (type === 'gender') {
      // Predefined colors for gender
      const genderColors = {
        male: { light: '#3b82f6', dark: '#1d4ed8' },
        female: { light: '#ec4899', dark: '#be185d' },
        other: { light: '#8b5cf6', dark: '#6d28d9' },
        'prefer-not-to-say': { light: '#6b7280', dark: '#4b5563' }
      };
      const key = category.id.toLowerCase() as keyof typeof genderColors;
      color = genderColors[key] ? genderColors[key]['dark'] : '#6b7280';
    } else if (type === 'device') {
      // Predefined colors for devices
      const deviceColors = {
        desktop: { light: '#10b981', dark: '#047857' },
        mobile: { light: '#f59e0b', dark: '#b45309' },
        tablet: { light: '#8b5cf6', dark: '#6d28d9' },
        other: { light: '#6b7280', dark: '#4b5563' }
      };
      const key = category.id.toLowerCase() as keyof typeof deviceColors;
      color = deviceColors[key] ? deviceColors[key]['dark'] : '#6b7280';
    } else {
      // Default color scheme for custom types
      const colors = [
        { light: '#ef4444', dark: '#b91c1c' }, // red
        { light: '#3b82f6', dark: '#1d4ed8' }, // blue
        { light: '#10b981', dark: '#047857' }, // green
        { light: '#f59e0b', dark: '#b45309' }, // amber
        { light: '#8b5cf6', dark: '#6d28d9' }, // purple
        { light: '#ec4899', dark: '#be185d' }, // pink
        { light: '#06b6d4', dark: '#0e7490' }, // cyan
        { light: '#84cc16', dark: '#4d7c0f' }, // lime
      ];
      color = colors[index % colors.length]['dark'];
    }

    return {
      ...category,
      percentage: total > 0 ? (category.value / total) * 100 : 0,
      color
    };
  });

  // Sort categories by value (descending)
  const sortedCategories = [...categoriesWithPercentage].sort((a, b) => b.value - a.value);

  // Format data for Nivo bar chart
  const barData = useMemo(() => {
    return sortedCategories.map(category => ({
      id: category.id,
      label: category.label,
      value: category.value,
      percentage: category.percentage,
      color: category.color
    }));
  }, [sortedCategories]);

  // Custom tooltip
  const CustomTooltip = ({ id, value, percentage, color }: any) => (
    <div className="bg-zinc-800 p-2 rounded shadow-lg border border-zinc-700">
      <div className="font-semibold text-gray-200">{id}</div>
      <div className="flex items-center mt-1">
        <div
          className="w-3 h-3 rounded-full mr-2"
          style={{ backgroundColor: color }}
        />
        <span className="text-sm text-gray-300">
          {value} ({percentage.toFixed(1)}%)
        </span>
      </div>
    </div>
  );

  return (
    <div className="bg-zinc-800 rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold text-gray-200 mb-4">{title}</h3>

      <div className="flex flex-col md:flex-row gap-6">
        {/* Bar Chart */}
        <div className="w-full md:w-1/2 h-80">
          <ResponsiveBar
            data={barData}
            keys={['value']}
            indexBy="label"
            layout="horizontal"
            margin={{ top: 20, right: 60, bottom: 50, left: 100 }}
            padding={0.3}
            valueScale={{ type: 'linear' }}
            indexScale={{ type: 'band', round: true }}
            colors={({ data }) => data.color}
            borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
            axisTop={null}
            axisRight={null}
            axisBottom={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legend: 'Count',
              legendPosition: 'middle',
              legendOffset: 32,
              format: ' >-'
            }}
            axisLeft={{
              tickSize: 5,
              tickPadding: 5,
              tickRotation: 0,
              legendPosition: 'middle',
              legendOffset: -80,
            }}
            enableGridX={true}
            enableGridY={false}
            labelSkipWidth={12}
            labelSkipHeight={12}
            labelTextColor="#ffffff"
            tooltip={({ data }) => (
              <CustomTooltip
                id={data.label}
                value={data.value}
                percentage={data.percentage}
                color={data.color}
              />
            )}
            theme={{
              axis: {
                ticks: {
                  text: {
                    fill: '#9ca3af',
                    fontSize: '12px'
                  }
                },
                legend: {
                  text: {
                    fill: '#9ca3af',
                    fontSize: '12px'
                  }
                }
              },
              grid: {
                line: {
                  stroke: '#374151',
                  strokeWidth: 1,
                  strokeDasharray: '3 3'
                }
              },
              tooltip: {
                container: {
                  background: '#1f2937',
                  border: '1px solid #4b5563',
                  borderRadius: '0.375rem',
                  padding: '0.5rem',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }
              }
            }}
            animate={true}
            motionStiffness={90}
            motionDamping={15}
          />
        </div>

        {/* Pie Chart */}
        <div className="w-full md:w-1/2 h-80 flex items-center justify-center">
          <div className="relative w-full h-full">
            <ResponsiveBar
              data={barData}
              keys={['percentage']}
              indexBy="label"
              layout="vertical"
              margin={{ top: 20, right: 20, bottom: 50, left: 60 }}
              padding={0.3}
              valueScale={{ type: 'linear' }}
              indexScale={{ type: 'band', round: true }}
              colors={({ data }) => data.color}
              borderColor={{ from: 'color', modifiers: [['darker', 1.6]] }}
              axisTop={null}
              axisRight={null}
              axisBottom={{
                tickSize: 5,
                tickPadding: 5,
                tickRotation: 0,
                legend: 'Percentage',
                legendPosition: 'middle',
                legendOffset: 32,
                format: (v) => `${v}%`
              }}
              axisLeft={{
                tickSize: 5,
                tickPadding: 5,
                tickRotation: 0,
                legend: 'Category',
                legendPosition: 'middle',
                legendOffset: -50,
              }}
              enableGridX={false}
              enableGridY={false}
              labelSkipWidth={12}
              labelSkipHeight={12}
              labelTextColor="#ffffff"
              tooltip={({ data }) => (
                <CustomTooltip
                  id={data.label}
                  value={data.value}
                  percentage={data.percentage}
                  color={data.color}
                />
              )}
              theme={{
                axis: {
                  ticks: {
                    text: {
                      fill: '#9ca3af',
                      fontSize: '12px'
                    }
                  },
                  legend: {
                    text: {
                      fill: '#9ca3af',
                      fontSize: '12px'
                    }
                  }
                },
                tooltip: {
                  container: {
                    background: '#1f2937',
                    border: '1px solid #4b5563',
                    borderRadius: '0.375rem',
                    padding: '0.5rem',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                  }
                }
              }}
              animate={true}
              motionStiffness={90}
              motionDamping={15}
            />
          </div>
        </div>
      </div>
    </div>
  );
};