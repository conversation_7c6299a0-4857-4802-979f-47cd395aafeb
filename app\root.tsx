import {
  <PERSON>s,
  Meta,
  Meta<PERSON>unction,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  isRouteErrorResponse,
  useRouteError,
  Link,
} from "@remix-run/react";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import { generateMetaDescriptors } from "./metadata";
import "./tailwind.css";
import { ClientProviders } from "./components/client-providers";
import Header from "./components/Layout/Header";
import Footer from "./components/Layout/Footer";
import { HeadScripts } from "./components/head-scripts";
import { AnalyticsTracker } from "./components/analytics/AnalyticsTracker";

export const meta: MetaFunction = () => {
  return generateMetaDescriptors();
};

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  { rel: "preconnect", href: "https://fonts.gstatic.com", crossOrigin: "anonymous" },
  { rel: "preconnect", href: "https://connect.facebook.net", crossOrigin: "anonymous" },
  { rel: "preconnect", href: "https://www.facebook.com", crossOrigin: "anonymous" },
  { rel: "stylesheet", href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" },
];

export async function loader({ request }: LoaderFunctionArgs) {
  return {
    ENV: {
      SITE_URL: process.env.SITE_URL,
      NODE_ENV: process.env.NODE_ENV,
      STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_KEY: process.env.SUPABASE_KEY,
    },
  };
}

// Define the type for our environment variables
interface WindowEnv {
  SITE_URL?: string;
  NODE_ENV?: string;
  STRIPE_PUBLISHABLE_KEY?: string;
  SUPABASE_URL?: string;
  SUPABASE_KEY?: string;
  [key: string]: string | undefined;
}

export default function App() {
  const data = useLoaderData<{ ENV: WindowEnv }>();
  const env = data?.ENV || {};
  return (
    <html lang="en">
      <head>
        <Meta />
        <Links />
        <HeadScripts />
        <script
          dangerouslySetInnerHTML={{
            __html: `window.ENV = ${JSON.stringify(env)}`,
          }}
        />
      </head>
      <body>
        <ClientProviders>
          <AnalyticsTracker />
          <Header />
          <Outlet />
          <Footer />
        </ClientProviders>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

/**
 * ErrorBoundary handles both route and runtime errors, including 500s.
 */
export function ErrorBoundary({ error }: { error: unknown }) {
  // Log the error
  //console.error(error);
  const err = useRouteError();

  // If this is a response error (like 404)
  if (isRouteErrorResponse(err)) {
    return (
      <html lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <HeadScripts />
          <Meta />
          <Links />
        </head>
        <body>
          <div className="min-h-screen flex items-center justify-center bg-black text-white p-4">
            <div className="max-w-md w-full space-y-6 text-center">
              <h1 className="text-4xl font-bold text-[var(--darkGold)]">
                {err.status} {err.statusText}
              </h1>
              <p>{err.data || "The page you requested could not be found."}</p>
              <Link
                to="/"
                className="inline-block px-6 py-2 bg-[var(--darkGold)] text-white rounded-md"
              >
                Return Home
              </Link>
            </div>
          </div>
          <Scripts />
        </body>
      </html>
    );
  }

  // Generic JavaScript error (500 etc.)
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <HeadScripts />
        <Meta />
        <Links />
      </head>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-black text-white p-4">
          <div className="max-w-md w-full space-y-6 text-center">
            <h1 className="text-4xl font-bold text-red-600">Unexpected Error</h1>
            <p className="text-red-400">
              Sorry, an unexpected error has occurred.
            </p>
            <pre className="text-xs text-left bg-zinc-800 p-2 rounded">
              {error instanceof Error ? error.message : String(error)}
            </pre>
            <Link
              to="/"
              className="inline-block px-6 py-2 bg-[var(--darkGold)] text-white rounded-md"
            >
              Return Home
            </Link>
          </div>
        </div>
        <Scripts />
      </body>
    </html>
  );
};