import { useState, useEffect } from 'react';
import { Link } from '@remix-run/react';
import { Bell, User, LogOut } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useAdminAuth, useSupabase } from '@/app/context/AdminAuthContext';
import { useAppSelector, useAppDispatch } from '@/app/store/hooks';
import {
  markNotificationAsRead,
  markAllNotificationsAsRead,
} from '@/app/store/slices/adminSlice';
import {
  startRealtimeMonitoring,
  stopRealtimeMonitoring,
} from '@/app/store/slices/realtimeSlice';
import {
  selectRecentNotifications,
  selectIsConnected,
} from '@/app/store/slices/realtimeSlice';
import { useRealtimeEvents } from '@/app/hooks/useRealtimeEvents';

interface AdminTopbarProps {
  sidebarCollapsed: boolean;
  userEmail?: string;
}

export function AdminTopbar({ sidebarCollapsed, userEmail }: AdminTopbarProps) {
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const dispatch = useAppDispatch();
  const { logout } = useAdminAuth();

  // Set up real-time event listening
  useRealtimeEvents();

  // 1️⃣ Kick off Supabase Realtime on mount
  useEffect(() => {
    dispatch(startRealtimeMonitoring());
    return () => {
      dispatch(stopRealtimeMonitoring());
    };
  }, [dispatch]);

  // 2️⃣ Grab notifications from the realtime slice
  const notifications = useAppSelector(state =>
    selectRecentNotifications(state, 20)  // show last 20
  );

  // Count unread
  const unreadCount = notifications.filter((n: any) => !n.read).length;

  const toggleProfileMenu = () => {
    setShowProfileMenu(v => !v);
    if (showNotifications) {
      setShowNotifications(false);
    }
  };

  const toggleNotifications = () => {
    setShowNotifications(v => !v);
    if (showProfileMenu) {
      setShowProfileMenu(false);
    }
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <header className="bg-zinc-800 border-b border-amber-600 h-16 flex items-center justify-between px-4">
      {/* BRANDING */}
      <div className="flex items-center">
        <div className="ml-4">
          <h2 className="text-lg font-semibold text-amber-200">
            The Convention Before Christmas
          </h2>
          <p className="text-sm text-amber-100">Admin Portal - Relay v0.1</p>
        </div>
      </div>

      {/* CONTROLS */}
      <div className="flex items-center space-x-4">
        {/* NOTIFICATIONS */}
        <div className="relative">
          <button
            onClick={toggleNotifications}
            className="p-2 rounded-full text-amber-200 hover:text-amber-400 hover:bg-amber-900/50 relative"
          >
            <Bell className="w-5 h-5" />
            {unreadCount > 0 && (
              <span className="absolute top-1 right-1 w-2 h-2 bg-amber-400 rounded-full" />
            )}
          </button>

          {showNotifications && (
            <div className="absolute right-0 mt-2 w-80 bg-zinc-700 rounded-md shadow-lg overflow-hidden z-20 border border-amber-600">
              <div className="p-4 border-b border-amber-600">
                <h3 className="text-lg font-medium text-amber-200">
                  Notifications
                </h3>
              </div>
              <div className="divide-y divide-amber-600 max-h-96 overflow-y-auto">
                {notifications.map((notification: any) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-amber-900/30 cursor-pointer ${notification.read ? 'opacity-50' : 'bg-amber-900/10'
                      }`}
                    onClick={() =>
                      dispatch(markNotificationAsRead(notification.id))
                    }
                  >
                    <p className="text-sm text-amber-100">
                      {notification.message}
                    </p>
                    <p className="text-xs text-amber-200/70">
                      {formatDistanceToNow(new Date(notification.timestamp), {
                        addSuffix: true,
                      })}
                    </p>
                  </div>
                ))}
                {!notifications.length && (
                  <div className="p-4 text-center text-sm text-amber-100/70">
                    No notifications yet
                  </div>
                )}
              </div>
              <div className="p-2 text-center border-t border-amber-600">
                <button
                  onClick={() => dispatch(markAllNotificationsAsRead())}
                  className="text-sm font-medium text-amber-400 hover:underline"
                >
                  Mark all as read
                </button>
              </div>
            </div>
          )}
        </div>

        {/* PROFILE */}
        <div className="relative">
          <button
            onClick={toggleProfileMenu}
            className="flex items-center space-x-2 focus:outline-none"
          >
            <div className="w-8 h-8 rounded-full bg-amber-900/50 flex items-center justify-center">
              <User className="w-5 h-5 text-amber-200" />
            </div>
            {!sidebarCollapsed && (
              <span className="text-sm font-medium text-amber-200">
                {userEmail}
              </span>
            )}
          </button>

          {showProfileMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-zinc-700 rounded-md shadow-lg py-1 z-20 border border-amber-600">
              <Link
                to="/b7a6791b18efd0f58ca1fb26a0ef58dc/profile"
                className="block px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
              >
                Your Profile
              </Link>
              <Link
                to="/b7a6791b18efd0f58ca1fb26a0ef58dc/settings"
                className="block px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
              >
                Settings
              </Link>
              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 text-sm text-amber-300 hover:bg-amber-900/30 flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign out</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};