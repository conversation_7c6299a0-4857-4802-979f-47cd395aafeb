import { useState, useEffect } from 'react';
import { TicketType } from '@/types/ticket';
import TicketSelectStep from '@/app/components/BookTickets/ticket-select-step';
import { TicketDetailsStep } from '@/app/components/BookTickets/ticket-details-step';
import { TicketPaymentStep } from '@/app/components/BookTickets/ticket-payment-step';
import { TicketConfirmationStep } from '@/app/components/BookTickets/ticket-confirmation-step';
import { BackButton } from '@/app/components/BookTickets/back-button';
import { InfoPanels } from '@/app/components/BookTickets/info-panels';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store/store';
import { getTickets } from '@/lib/tickets';
import DaySelectionModal from '@/app/components/BookTicketsPopup/DaySelectionModal';
import { updateCartItemQuantity, addToCart, removeFromCart, clearCart } from '@/store/slices/booking-slice';
import SantaInfoModal from '@/app/components/BookTicketsPopup/SantaInfoModal';
import CharityInfoModal from '@/app/components/BookTicketsPopup/CharityInfoModal';
import SantaSlotModal from '@/app/components/BookTicketsPopup/SantaSlotModal';
import FamilyPassInfoModal from '@/app/components/BookTicketsPopup/FamilyPassInfoModal';
import { loadStripe } from '@stripe/stripe-js';
import { env } from '@/app/utils/env.client';
import { useAnalytics } from '@/app/hooks/useAnalytics';

// Initialize Stripe promise using window.ENV which is set by Remix
const stripePromise = typeof window !== 'undefined' && env.stripePublishableKey
  ? loadStripe(env.stripePublishableKey)
  : null;

type Step = 'select' | 'details' | 'payment' | 'confirmation';

interface BookTicketsClientProps {
  initialTickets: TicketType[];
}

export default function BookTicketsClient({ initialTickets }: BookTicketsClientProps) {
  const [currentStep, setCurrentStep] = useState<Step>('select');
  const [tickets, setTickets] = useState<TicketType[]>(initialTickets);
  const [loading, setLoading] = useState(true);
  const [isDaySelectionModalOpen, setIsDaySelectionModalOpen] = useState(false);
  const [selectedTicketForDay, setSelectedTicketForDay] = useState<TicketType | null>(null);
  const [selectedDays, setSelectedDays] = useState<Record<string, 'Saturday' | 'Sunday'>>({});
  const [isSantaInfoOpen, setIsSantaInfoOpen] = useState(false);
  const [isCharityInfoOpen, setIsCharityInfoOpen] = useState(false);
  const [isSantaSlotModalOpen, setIsSantaSlotModalOpen] = useState(false);
  const [selectedSantaTicket, setSelectedSantaTicket] = useState<TicketType | null>(null);
  const [charityMultiplier, setCharityMultiplier] = useState<number | null>(null);
  const cart = useSelector((state: RootState) => state.booking.cart);
  const dispatch = useDispatch();
  const [isRemoveDayModalOpen, setIsRemoveDayModalOpen] = useState(false);
  const [ticketToRemove, setTicketToRemove] = useState<TicketType | null>(null);
  const [isFamilyInfoOpen, setIsFamilyInfoOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Analytics tracking
  const { trackTicketJourney, trackDonationJourney } = useAnalytics();
  const orderDetails = useSelector((state: RootState) => state.booking.orderDetails);

  // Add this useEffect to handle Stripe redirect after payment
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const paymentIntentId = urlParams.get('payment_intent');
    const redirectStatus = urlParams.get('redirect_status');

    if (paymentIntentId && redirectStatus === 'succeeded') {
      setCurrentStep('confirmation');
      // Clean up the URL to remove Stripe query parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []); // Run only once on component mount

  // Handle day removal for 1-day tickets
  const handleDayRemoval = (day: 'Saturday' | 'Sunday') => {
    if (ticketToRemove) {
      const ticketDayId = `${ticketToRemove.id}-${day.toLowerCase()}`;
      const currentDayVariantQuantity = cart.find(item => item.ticketType.id === ticketDayId)?.quantity || 0;

      if (currentDayVariantQuantity <= 1) {
        dispatch(removeFromCart(ticketDayId));
      } else {
        dispatch(updateCartItemQuantity({ ticketId: ticketDayId, quantity: currentDayVariantQuantity - 1 }));
      }

      setIsRemoveDayModalOpen(false);
    }
  };

  useEffect(() => {
    const fetchTickets = async () => {
      try {
        const { tickets: fetchedTickets, availability: fetchedAvailability } = await getTickets();
        setTickets(fetchedTickets);
      } catch (error) {
        console.error('Error fetching tickets:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTickets();
  }, []);

  const getPreviousStep = (currentStep: Step): Step => {
    switch (currentStep) {
      case 'details':
        return 'select';
      case 'payment':
        return 'details';
      default:
        return 'select';
    }
  };

  // Add smooth scroll function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Update step changes to include scroll
  const handleStepChange = (newStep: Step) => {
    // Track step transitions
    if (newStep === 'details') {
      const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
      const totalValue = cart.reduce((sum, item) => sum + (item.ticketType.price * item.quantity), 0);
      trackTicketJourney.checkoutStarted(totalValue, totalItems);
    } else if (newStep === 'payment') {
      // Track when payment step is reached
      const totalValue = cart.reduce((sum, item) => sum + (item.ticketType.price * item.quantity), 0);
      trackTicketJourney.paymentInitiated(totalValue);
    }

    setCurrentStep(newStep);
    scrollToTop();
  };

  // Function to check if a ticket is a 1-day ticket
  const isOneDayTicket = (ticketId: string) => {
    return ticketId.includes('1day');
  };

  // Function to get ticket quantity including day variants
  const getTicketQuantity = (ticketId: string) => {
    return cart.reduce((total, item) => {
      if (item.ticketType.id === ticketId ||
        (item.ticketType.originalId && item.ticketType.originalId === ticketId)) {
        return total + item.quantity;
      }
      return total;
    }, 0);
  };

  // Handle day selection for 1-day tickets
  const handleDaySelect = (day: 'Saturday' | 'Sunday') => {
    if (selectedTicketForDay) {
      // Create a unique ID for this ticket+day combination
      const ticketDayId = `${selectedTicketForDay.id}-${day.toLowerCase()}`;

      // Store the selected day for this ticket
      setSelectedDays(prev => ({
        ...prev,
        [ticketDayId]: day
      }));

      // Create a modified ticket type with day information
      const ticketWithDay = {
        ...selectedTicketForDay,
        id: ticketDayId,
        originalId: selectedTicketForDay.id,
        selectedDay: day
      };

      // Add the ticket to the cart, explicitly passing selectedDay at the cart item level
      dispatch(addToCart({ ticketType: ticketWithDay, quantity: 1, selectedDay: day }));

      // Close the modal
      setIsDaySelectionModalOpen(false);
    }
  };

  // Override the default handlers in TicketSelectStep
  const handleUpdateQuantity = (ticketType: TicketType, newQuantity: number) => {
    if (isOneDayTicket(ticketType.id)) {
      const currentQuantity = getTicketQuantity(ticketType.id);

      if (newQuantity > currentQuantity) {
        setSelectedTicketForDay(ticketType);
        setIsDaySelectionModalOpen(true);
        return;
      }
      // For decrement (removing a ticket)
      if (newQuantity < currentQuantity) {
        // Find all day variants of this ticket
        const variants = cart.filter(item =>
          (item.ticketType.originalId === ticketType.id ||
            item.ticketType.id === ticketType.id) &&
          item.quantity > 0
        );

        // If there's only one variant, decrement it
        if (variants.length === 1) {
          const variant = variants[0];
          // If quantity becomes 0, explicitly remove it.
          if (newQuantity === 0) {
            dispatch(removeFromCart(variant.ticketType.id));
            // Track ticket removal
            trackTicketJourney.removedFromCart(variant.ticketType.name, variant.quantity);
          } else {
            dispatch(updateCartItemQuantity({
              ticketId: variant.ticketType.id,
              quantity: Math.max(0, variant.quantity - 1),
              selectedDay: variant.selectedDay
            }));
            // Track cart update
            trackTicketJourney.cartUpdated(cart.length, cart.reduce((sum, item) => sum + (item.ticketType.price * item.quantity), 0));
          }
        } else if (variants.length > 1) {
          setTicketToRemove(ticketType);
          setIsRemoveDayModalOpen(true);
        }
        return;
      }
    }

    // Handle charity donation quantity updates
    if (ticketType.id === 'charity-donation') {
      if (newQuantity === 0) {
        dispatch(removeFromCart(ticketType.id));
        setCharityMultiplier(null); // Reset multiplier when removing donation
        // Track donation removal
        trackDonationJourney.started(); // Track that donation interaction occurred
      } else {
        const currentItem = cart.find(item => item.ticketType.id === ticketType.id);
        if (currentItem && currentItem.ticketType.multiplier) {
          // If there's an existing multiplier, maintain it
          const updatedTicket = {
            ...ticketType,
            price: ticketType.price * currentItem.ticketType.multiplier,
            originalPrice: ticketType.price,
            multiplier: currentItem.ticketType.multiplier
          };
          dispatch(updateCartItemQuantity({
            ticketId: ticketType.id,
            quantity: newQuantity
          }));
          // Track donation amount update
          trackDonationJourney.amountSelected(ticketType.price * newQuantity * currentItem.ticketType.multiplier);
        } else {
          // No multiplier, use base price
          dispatch(updateCartItemQuantity({
            ticketId: ticketType.id,
            quantity: newQuantity
          }));
          // Track donation amount update
          trackDonationJourney.amountSelected(ticketType.price * newQuantity);
        }
      }
      return;
    }

    // For tickets that already have a day assigned or for 2-day tickets, or if the 1-day ticket logic above didn't return
    const currentCartItem = cart.find(item => item.ticketType.id === ticketType.id);

    if (newQuantity === 0) {
      if (currentCartItem) { // Only remove if it exists in cart
        dispatch(removeFromCart(ticketType.id));
        // Track ticket removal
        trackTicketJourney.removedFromCart(ticketType.name, currentCartItem.quantity);
      }
    } else {
      if (currentCartItem) { // If item exists, update quantity
        dispatch(updateCartItemQuantity({
          ticketId: ticketType.id,
          quantity: newQuantity,
          selectedDay: currentCartItem.selectedDay
        }));
        // Track cart update
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
        const totalValue = cart.reduce((sum, item) => sum + (item.ticketType.price * item.quantity), 0);
        trackTicketJourney.cartUpdated(totalItems, totalValue);
      } else { // If item does not exist and newQuantity > 0, add to cart
        dispatch(addToCart({ ticketType: ticketType, quantity: newQuantity, selectedDay: ticketType.selectedDay }));
        // Track ticket addition
        trackTicketJourney.addedToCart(ticketType.name, newQuantity, ticketType.price);
      }
    }
  };

  const handlePlusClick = (ticketType: TicketType) => {
    if (isOneDayTicket(ticketType.id)) {
      setSelectedTicketForDay(ticketType);
      setIsDaySelectionModalOpen(true);
      return;
    }
    if (ticketType.id.includes('santa-option')) {
      setSelectedSantaTicket(ticketType);
      setIsSantaSlotModalOpen(true);
      return;
    }
    if (ticketType.id === 'charity-donation') {
      const currentQuantity = getTicketQuantity(ticketType.id);
      if (currentQuantity === 0) {
        // First time adding charity donation
        dispatch(addToCart({ ticketType, quantity: 1 }));
      } else {
        // Increment existing charity donation
        handleUpdateQuantity(ticketType, currentQuantity + 1);
      }
      return;
    }
    // For all other tickets
    const currentQuantity = getTicketQuantity(ticketType.id);
    handleUpdateQuantity(ticketType, currentQuantity + 1);
  };

  const handleSantaSlotSelect = (ticket: TicketType, slotId: number, day: string, timeRange: string, numChildren: number = 1) => {
    if (ticket) {
      // Calculate the total price based on number of children
      const additionalChildrenCount = numChildren - 1;
      const additionalCost = additionalChildrenCount * 6;
      const totalPrice = ticket.price + additionalCost;

      const modifiedTicketType = {
        ...ticket,
        price: totalPrice,
        description: `10-minute slot with Santa (includes ${numChildren} ${numChildren === 1 ? 'child' : 'children'})`,
      };

      const santaSlotDetails = {
        id: slotId,
        day,
        timeRange,
        numChildren
      };

      // Check if there's an existing Santa option in the cart
      const existingItem = cart.find(item => item.ticketType.id === ticket.id);
      if (existingItem) {
        // Remove existing item if changing slot
        dispatch(removeFromCart(ticket.id));
      }

      // Add the new item
      dispatch(addToCart({
        ticketType: modifiedTicketType,
        quantity: 1,
        santaSlot: santaSlotDetails
      }));

      // Close the modal after successful selection
      setIsSantaSlotModalOpen(false);
      setSelectedSantaTicket(null); // Clear selected Santa ticket after processing
    }
  };

  const renderCharityMultipliers = (ticket: TicketType) => {
    if (ticket.id !== 'charity-donation') return null;

    const multipliers = [10, 100, 1000];
    const currentQuantity = getTicketQuantity(ticket.id);
    const currentItem = cart.find(item => item.ticketType.id === ticket.id);

    return (
      <div className="flex gap-2 mt-4 items-center justify-between">
        {multipliers.map((multiplier) => (
          <button
            key={multiplier}
            onClick={() => {
              setCharityMultiplier(multiplier);
              const basePrice = ticket.price;
              const newPrice = basePrice * multiplier;
              const updatedTicket = {
                ...ticket,
                price: newPrice,
                originalPrice: basePrice,
                multiplier: multiplier
              };

              // Track multiplier application
              trackDonationJourney.multiplierApplied(multiplier, basePrice, newPrice);

              if (currentQuantity === 0) {
                dispatch(addToCart({ ticketType: updatedTicket, quantity: 1 }));
                // Track donation started
                trackDonationJourney.started();
                trackDonationJourney.amountSelected(newPrice);
              } else if (currentItem) {
                // Update existing item with new price
                dispatch(removeFromCart(ticket.id));
                dispatch(addToCart({ ticketType: updatedTicket, quantity: currentQuantity }));
                // Track donation amount update
                trackDonationJourney.amountSelected(newPrice * currentQuantity);
              }
            }}
            className={`px-3 py-1 text-sm ${charityMultiplier === multiplier
              ? 'bg-[var(--darkGold)] text-white'
              : 'bg-gray-700 text-white hover:bg-gray-600'} rounded`}
          >
            {multiplier}x
          </button>
        ))}
      </div>
    );
  };

  const handlePaymentSuccess = async () => {
    try {
      setIsProcessing(true);
      setErrorMessage('');

      const totalAmount = cart.reduce((total, item) => total + (item.ticketType.price * item.quantity), 0);

      // Track payment processing
      trackTicketJourney.paymentProcessing(orderDetails.paymentIntentId || '');

      // Include total in orderDetailsWithMultiplier
      const orderDetailsWithMultiplier = {
        ...orderDetails,
        tickets: cart.map(item => {
          if (isOneDayTicket(item.ticketType.id) && selectedDays[item.ticketType.id] && !item.selectedDay) {
            return {
              ...item,
              selectedDay: selectedDays[item.ticketType.id]
            };
          }
          return item;
        }),
        total: totalAmount,
        charityMultiplier: charityMultiplier,
        // Ensure we keep the orderId and paymentIntentId from the original orderDetails
        orderId: orderDetails.orderId,
        paymentIntentId: orderDetails.paymentIntentId
      };

      const response = await fetch('/api/send-ticket-confirmation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: totalAmount,
          orderDetails: {
            ...orderDetailsWithMultiplier,
            total: totalAmount,
          },
        }),
      });

      const data = await response.json();

      if (!response.ok || data.message?.includes('Failed to send confirmation email')) {
        setErrorMessage(data.message || 'Failed to send confirmation');
        setIsProcessing(false);
        // Track payment failure
        trackTicketJourney.paymentFailed(data.message || 'Failed to send confirmation', orderDetails.paymentIntentId);
        return;
      }

      // Track successful payment completion
      trackTicketJourney.paymentCompleted(totalAmount, orderDetails.paymentIntentId || '', orderDetails.orderId);

      // Track donation completion if charity donation exists
      const charityDonation = cart.find(item => item.ticketType.id === 'charity-donation');
      if (charityDonation) {
        const donationAmount = charityDonation.ticketType.price * charityDonation.quantity;
        trackDonationJourney.paymentCompleted(donationAmount, orderDetails.paymentIntentId || '', charityMultiplier || undefined);
      }

      // Clear any existing error message
      setErrorMessage('');
      handleStepChange('confirmation');
      setIsProcessing(false);

      // Track confirmation view
      trackTicketJourney.confirmationViewed(orderDetails.orderId || '');
    } catch (error: any) {
      console.error('Error sending confirmation:', error.message);
      setErrorMessage(error.message || 'Failed to send confirmation');
      setIsProcessing(false);
      // Track payment failure
      trackTicketJourney.paymentFailed(error.message, orderDetails.paymentIntentId);
    }
  };

  return (
    <>
      {/* Back button */}
      {(currentStep !== 'select' && currentStep !== 'confirmation') && (
        <BackButton onClick={() => handleStepChange(getPreviousStep(currentStep))} />
      )}

      {/* Render Steps */}
      {currentStep === 'select' && (
        <TicketSelectStep
          ticketTypes={tickets}
          loading={loading}
          onContinue={() => handleStepChange('details')}
          onOpenFamilyInfo={() => setIsFamilyInfoOpen(true)}
          onOpenSantaInfo={() => setIsSantaInfoOpen(true)}
          onOpenSantaSlot={(ticket: TicketType) => {
            setSelectedSantaTicket(ticket);
            setIsSantaSlotModalOpen(true);
          }}
          onOpenCharityInfo={() => setIsCharityInfoOpen(true)}
          hasRegularTickets={() => cart.some(item => !item.ticketType.id.includes('santa-option') && !item.ticketType.id.includes('charity-donation'))}
          renderCharityMultipliers={renderCharityMultipliers}
          calculateTotal={() => cart.reduce((total, item) => total + (item.ticketType.price * item.quantity), 0)}
          updateQuantity={handleUpdateQuantity}
          handlePlusClick={handlePlusClick}
        />
      )}

      {isFamilyInfoOpen &&
        <FamilyPassInfoModal
          isOpen={isFamilyInfoOpen}
          onClose={() => setIsFamilyInfoOpen(false)}
        />
      }

      {currentStep === 'details' && (
        <TicketDetailsStep
          onContinue={() => handleStepChange('payment')}
        />
      )}

      {currentStep === 'payment' && (
        <>
          <TicketPaymentStep
            onSuccess={handlePaymentSuccess}
            stripePromise={stripePromise}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
          />
          {errorMessage && (
            <div className="mt-1 mb-2 p-2 bg-red-700/50 border border-red-500 rounded-lg text-white text-center">
              {errorMessage}
            </div>
          )}
        </>
      )}

      {currentStep === 'confirmation' && (
        <TicketConfirmationStep />
      )}

      {/* Info Panels */}
      <InfoPanels
        isFamilyInfoOpen={isFamilyInfoOpen}
        isSantaInfoOpen={isSantaInfoOpen}
        isCharityInfoOpen={isCharityInfoOpen}
        closeFamilyInfo={() => setIsFamilyInfoOpen(false)}
        closeSantaInfo={() => setIsSantaInfoOpen(false)}
        closeCharityInfo={() => setIsCharityInfoOpen(false)}
      />

      {/* Day Selection Modal */}
      <DaySelectionModal
        isOpen={isDaySelectionModalOpen}
        onClose={() => setIsDaySelectionModalOpen(false)}
        onDaySelect={handleDaySelect}
      />

      {/* Santa Info Modal */}
      <SantaInfoModal
        isOpen={isSantaInfoOpen}
        onClose={() => setIsSantaInfoOpen(false)}
      />

      {/* Santa Slot Modal */}
      <SantaSlotModal
        isOpen={isSantaSlotModalOpen}
        onClose={() => setIsSantaSlotModalOpen(false)}
        onSlotSelect={(ticket, slotId, day, timeRange, numChildren) => handleSantaSlotSelect(ticket, slotId, day, timeRange, numChildren)}
        selectedTicket={selectedSantaTicket}
      />

      {/* Charity Info Modal */}
      <CharityInfoModal
        isOpen={isCharityInfoOpen}
        onClose={() => setIsCharityInfoOpen(false)}
      />

      {/* Add the Day Removal Modal */}
      {isRemoveDayModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
          <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-white mb-4">
              Select Day to Remove
            </h3>
            <p className="text-white mb-4">
              Please select which day&apos;s ticket you want to remove:
            </p>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => handleDayRemoval('Saturday')}
                className="px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
              >
                Saturday
              </button>
              <button
                onClick={() => handleDayRemoval('Sunday')}
                className="px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
              >
                Sunday
              </button>
            </div>
            <button
              onClick={() => setIsRemoveDayModalOpen(false)}
              className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors w-full"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

    </>
  );
};