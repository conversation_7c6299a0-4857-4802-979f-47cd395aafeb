import { TicketType } from '@/types/ticket';
import { QuantitySelector } from './quantity-selector';

interface TicketCardProps {
  ticket: TicketType;
  quantity: number;
  onQuantityChange: (quantity: number) => void;
  onInfoClick: () => void;
}

export function TicketCard({ ticket, quantity, onQuantityChange, onInfoClick }: TicketCardProps) {
  const isOption = ticket.type === 'option';
  const isCharity = ticket.id === 'charity-donation';

  return (
    <div className={`p-6 rounded-lg border ${
      isOption
        ? 'bg-[#1e1e1e] border-[var(--darkGold)]'
        : 'bg-[#1e1e1e] border-gray-700'
    }`}>
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-semibold text-white">{ticket.name}</h3>
          <p className="text-gray-400 mt-1">{ticket.description}</p>
        </div>
        <div className="flex items-center gap-2">
          {!isCharity && (
            <button
              onClick={onInfoClick}
              className="text-[var(--darkGold)] hover:text-[#95784a] transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </button>
          )}
          <span className="text-xl font-bold text-white">€{ticket.price.toFixed(2)}</span>
        </div>
      </div>

      {!isCharity && (
        <QuantitySelector
          quantity={quantity}
          onChange={onQuantityChange}
          min={0}
          max={ticket.maxQuantity || 10}
        />
      )}
    </div>
  );
};