import { useState, ReactNode, useEffect } from 'react';
import { Outlet } from '@remix-run/react';
import { AdminSidebar } from './AdminSidebar';
import { AdminTopbar } from './AdminTopbar';
import { useAdminAuth } from '@/app/context/AdminAuthContext';
import { useAppDispatch } from '@/app/store/hooks';
import { fetchCurrentUser, setCurrentUser } from '@/app/store/slices/userSlice';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface AdminLayoutProps {
  children?: ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user } = useAdminAuth();
  const dispatch = useAppDispatch();

  const toggleSidebar = () => {
    // On mobile, toggle the mobile menu state
    if (window.innerWidth < 768) {
      setIsMobileMenuOpen(!isMobileMenuOpen);
    } else {
      // On desktop, toggle the collapsed state
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  // Close mobile menu when window is resized to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Load current user into Redux when admin layout mounts
  useEffect(() => {
    if (user) {
      // Convert AdminUser to User format and set directly in Redux
      const reduxUser = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: true,
        createdAt: new Date(user.created_at),
        updatedAt: new Date(user.updated_at),
        lastLogin: new Date(), // Current time as last login
      };

      // Set the current user directly in Redux
      dispatch(setCurrentUser(reduxUser));

      // Also try to fetch from service as backup
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, user]);

  return (
    <div className="flex h-screen bg-zinc-900 overflow-hidden">
      <AdminSidebar
        toggleSidebar={toggleSidebar}
        collapsed={sidebarCollapsed}
        userRole={user?.role}
        isMobileOpen={isMobileMenuOpen}
      />

      <div className="flex flex-col flex-1 min-h-0 min-w-0">
        <AdminTopbar
          sidebarCollapsed={sidebarCollapsed}
          userEmail={user?.email}
          toggleSidebar={toggleSidebar}
          isMobileMenuOpen={isMobileMenuOpen}
        />

        <main className="flex-1 overflow-y-auto p-3 sm:p-4 md:p-6 bg-zinc-900 text-gray-100">
          <div className="max-w-8xl mx-auto">
            <div className="space-y-4 sm:space-y-6">
              {children || <Outlet />}
            </div>
          </div>
        </main>
      </div>

      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        toastClassName="bg-zinc-800 text-white"
        progressClassName="bg-amber-500"
      />
    </div>
  );
}