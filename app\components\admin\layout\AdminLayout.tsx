import { useState, ReactNode, useEffect } from 'react';
import { Outlet } from '@remix-run/react';
import { AdminSidebar } from './AdminSidebar';
import { AdminTopbar } from './AdminTopbar';
import { useAdminAuth } from '@/app/context/AdminAuthContext';
import { useAppDispatch } from '@/app/store/hooks';
import { fetchCurrentUser, setCurrentUser } from '@/app/store/slices/userSlice';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface AdminLayoutProps {
  children?: ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user } = useAdminAuth();
  const dispatch = useAppDispatch();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Load current user into Redux when admin layout mounts
  useEffect(() => {
    if (user) {
      // Convert AdminUser to User format and set directly in Redux
      const reduxUser = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: true,
        createdAt: new Date(user.created_at),
        updatedAt: new Date(user.updated_at),
        lastLogin: new Date(), // Current time as last login
      };

      // Set the current user directly in Redux
      dispatch(setCurrentUser(reduxUser));

      // Also try to fetch from service as backup
      dispatch(fetchCurrentUser());
    }
  }, [dispatch, user]);

  return (
    <div className="flex h-screen bg-zinc-900 overflow-hidden">
      <AdminSidebar toggleSidebar={toggleSidebar} collapsed={sidebarCollapsed} userRole={user?.role} />

      <div className="flex flex-col flex-1 min-h-0 min-w-0">
        <AdminTopbar
          sidebarCollapsed={sidebarCollapsed}
          userEmail={user?.email}
        />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-zinc-900 text-gray-100">
          <div className="max-w-8xl mx-auto">
            {children || <Outlet />}
          </div>
        </main>
      </div>

      {/* Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
        toastClassName="bg-zinc-800 text-white"
        progressClassName="bg-amber-500"
      />
    </div>
  );
}