import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface InfoPanelsState {
  isFamilyInfoOpen: boolean;
  isSantaInfoOpen: boolean;
  isCharityInfoOpen: boolean;
}

const initialState: InfoPanelsState = {
  isFamilyInfoOpen: false,
  isSantaInfoOpen: false,
  isCharityInfoOpen: false,
};

const infoPanelsSlice = createSlice({
  name: 'infoPanels',
  initialState,
  reducers: {
    openFamilyInfo: (state) => {
      state.isFamilyInfoOpen = true;
    },
    closeFamilyInfo: (state) => {
      state.isFamilyInfoOpen = false;
    },
    openSantaInfo: (state) => {
      state.isSantaInfoOpen = true;
    },
    closeSantaInfo: (state) => {
      state.isSantaInfoOpen = false;
    },
    openCharityInfo: (state) => {
      state.isCharityInfoOpen = true;
    },
    closeCharityInfo: (state) => {
      state.isCharityInfoOpen = false;
    },
  },
});

export const {
  openFamilyInfo,
  closeFamilyInfo,
  openSantaInfo,
  closeSantaInfo,
  openCharityInfo,
  closeCharityInfo,
} = infoPanelsSlice.actions;

export default infoPanelsSlice.reducer;