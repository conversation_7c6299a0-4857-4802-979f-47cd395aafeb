import { json, type ActionFunctionArgs } from "@remix-run/node";
import Strip<PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { env } from '@/app/utils/env.server';

const stripe = new Stripe(env.stripeSecretKey, {
  apiVersion: '2025-05-28.basil',
});

const supabase = createClient(
  env.supabase.url,
  env.supabase.key
);

const endpointSecret = env.stripeWebhookSecret;

export async function action({ request }: ActionFunctionArgs) {
  const body = await request.text();
  const sig = request.headers.get('stripe-signature');

  if (!sig) {
    console.error('No Stripe signature found');
    return json({ error: 'No signature' }, { status: 400 });
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message);
    return json({ error: 'Invalid signature' }, { status: 400 });
  }

  console.log('Stripe webhook event received:', event.type);

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.created':
        await handlePaymentCreated(event.data.object as Stripe.PaymentIntent);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}

async function handlePaymentCreated(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment intent created:', paymentIntent.id);

  const metadata = paymentIntent.metadata;
  const transactionType = metadata.type || 'ticket';

  try {
    if (transactionType === 'vendor_stand') {
      // Update vendor stand status to pending
      const { error } = await supabase
        .from('VendorStands')
        .update({
          payment_status: 'pending',
          payment_intent_id: paymentIntent.id
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;
    } else {
      // For tickets and donations, update Orders table if it exists
      const { error } = await supabase
        .from('Orders')
        .update({
          status: 'pending'
        })
        .eq('payment_intent_id', paymentIntent.id);

      // Don't throw error if order doesn't exist yet (it might be created later)
      if (error && !error.message.includes('No rows')) {
        console.error('Error updating order status to pending:', error);
      }
    }
  } catch (error) {
    console.error('Error handling payment created:', error);
  }
}

async function handlePaymentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment succeeded:', paymentIntent.id);

  const metadata = paymentIntent.metadata;
  const transactionType = metadata.type || 'ticket';

  try {
    if (transactionType === 'vendor_stand') {
      // Update vendor stand payment status
      const { error } = await supabase
        .from('VendorStands')
        .update({
          payment_status: 'paid',
          status: 'confirmed'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;

      console.log(`Vendor stand payment confirmed for payment intent: ${paymentIntent.id}`);
    } else {
      // Handle ticket purchases and donations
      const { error } = await supabase
        .from('Orders')
        .update({
          status: 'paid'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;

      // Update charity donations if this order contains donations
      if (metadata.hasDonation === 'true') {
        const { error: donationError } = await supabase
          .from('CharityDonations')
          .update({
            status: 'completed'
          })
          .eq('payment_intent_id', paymentIntent.id);

        if (donationError) {
          console.error('Error updating donation status:', donationError);
        }
      }

      console.log(`Order payment confirmed for payment intent: ${paymentIntent.id}`);
    }
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
    throw error;
  }
}

async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment failed:', paymentIntent.id);

  const metadata = paymentIntent.metadata;
  const transactionType = metadata.type || 'ticket';

  try {
    if (transactionType === 'vendor_stand') {
      // Update vendor stand payment status
      const { error } = await supabase
        .from('VendorStands')
        .update({
          payment_status: 'failed',
          status: 'cancelled'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;
    } else {
      // Handle failed ticket purchases and donations
      const { error } = await supabase
        .from('Orders')
        .update({
          status: 'failed'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;

      // Update charity donations if this order contains donations
      if (metadata.hasDonation === 'true') {
        const { error: donationError } = await supabase
          .from('CharityDonations')
          .update({
            status: 'failed'
          })
          .eq('payment_intent_id', paymentIntent.id);

        if (donationError) {
          console.error('Error updating donation status:', donationError);
        }
      }
    }

    console.log(`Payment failed status updated for payment intent: ${paymentIntent.id}`);
  } catch (error) {
    console.error('Error handling payment failed:', error);
    throw error;
  }
}

async function handlePaymentCanceled(paymentIntent: Stripe.PaymentIntent) {
  console.log('Payment canceled:', paymentIntent.id);

  const metadata = paymentIntent.metadata;
  const transactionType = metadata.type || 'ticket';

  try {
    if (transactionType === 'vendor_stand') {
      // Update vendor stand payment status
      const { error } = await supabase
        .from('VendorStands')
        .update({
          payment_status: 'cancelled',
          status: 'cancelled'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;
    } else {
      // Handle canceled ticket purchases and donations
      const { error } = await supabase
        .from('Orders')
        .update({
          status: 'cancelled'
        })
        .eq('payment_intent_id', paymentIntent.id);

      if (error) throw error;

      // Update charity donations if this order contains donations
      if (metadata.hasDonation === 'true') {
        const { error: donationError } = await supabase
          .from('CharityDonations')
          .update({
            status: 'cancelled'
          })
          .eq('payment_intent_id', paymentIntent.id);

        if (donationError) {
          console.error('Error updating donation status:', donationError);
        }
      }
    }

    console.log(`Payment canceled status updated for payment intent: ${paymentIntent.id}`);
  } catch (error) {
    console.error('Error handling payment canceled:', error);
    throw error;
  }
}
