// app/routes/admin.tsx
import { Outlet, useLoaderData } from '@remix-run/react';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { AdminAuthProvider } from '@/app/context/AdminAuthContext';
import { SupabaseProvider } from '@/app/context/AdminAuthContext';
import React from 'react';
import { ReduxProvider } from '@/app/providers/ReduxProvider';
import { useEffect, useState } from 'react';

export async function loader({ request }: LoaderFunctionArgs) {
  return {
    ENV: {
      SITE_URL: process.env.SITE_URL,
      NODE_ENV: process.env.NODE_ENV,
      STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_KEY: process.env.SUPABASE_KEY,
    },
  };
}

export default function AdminRoot() {
  const data = useLoaderData<typeof loader>();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // This effect only runs on the client
    setIsClient(true);

    // Set environment variables on the client
    if (data?.ENV) {
      window.ENV = data.ENV;
    }
  }, [data?.ENV]);

  // Don't render anything on the server
  if (!isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-zinc-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
      </div>
    );
  }

  // Only render the admin content on the client
  return (
    <SupabaseProvider>
     <AdminAuthProvider>
        <div className="min-h-screen bg-zinc-900 text-amber-50">
          <Outlet />
        </div>
      </AdminAuthProvider>
    </SupabaseProvider>
  );
}