/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useAnalytics } from '@/app/hooks/useAnalytics';

const Newsletter = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  // Analytics tracking
  const { trackFormJourney } = useAnalytics();

  const Toast = () => toast('');

  // Clear success message after 5 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (message?.type === 'success') {
      timeoutId = setTimeout(() => {
        setMessage(null);
      }, 5000); // 5 seconds
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [message]);

  // Clear error message after 1 minute or when user types
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (message?.type === 'error') {
      timeoutId = setTimeout(() => {
        setMessage(null);
      }, 60000); // 1 minute
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [message]);

  // Clear error message when user types
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (message?.type === 'error') {
      setMessage(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    // Track newsletter signup started
    trackFormJourney.newsletterStarted();

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Something went wrong');
      }

      setMessage({ type: 'success', text: 'You have successfully subscribed to our newsletter!' });
      setEmail('');

      // Track successful newsletter signup
      trackFormJourney.newsletterCompleted(email);
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message || 'Failed to subscribe to our newsletter' });

      // Track failed newsletter signup
      trackFormJourney.newsletterFailed(error.message || 'Failed to subscribe');
    } finally {
      setLoading(false);
    }
  };

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--darkGold)]">
            Stay Updated
          </h2>
          <p className="text-white/60 mb-8">
            Subscribe to our newsletter for the latest updates about The Convention Before Christmas
          </p>
          <form onSubmit={handleSubmit} className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                value={email}
                onChange={handleEmailChange}
                placeholder="Enter your email"
                required
                className="flex-1 px-4 py-2 rounded-md bg-white/10 border border-[var(--darkGold)] text-white placeholder-white/40 focus:outline-none focus:border-[var(--lightGold)]"
              />
              <button
                type="submit"
                disabled={loading}
                className={`px-6 py-2 bg-[var(--darkGold)] text-white rounded-md transition-colors ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {loading ? 'Subscribing...' : 'Subscribe'}
              </button>
            </div>

            {/* Message display */}
            {message && (
              <div
                className={`mt-4 px-4 py-2 rounded-md text-center mx-auto max-w-md ${message.type === 'success'
                  ? 'bg-green-500/10 text-green-500 border border-green-500/20'
                  : 'bg-red-500/10 text-red-500 border border-red-500/20'
                  }`}
              >
                {message.text}
              </div>
            )}
          </form>
        </div>
      </div>
    </section>
  );
};
export default Newsletter;
