'use client';

import React, { useState, useEffect, ReactNode } from 'react';
import { ChakraProvider, createSystem, defaultConfig, defineConfig } from '@chakra-ui/react';
import { AudioProvider } from '@/app/context/AudioContext';
import { ToastContainer } from 'react-toastify';
import Loader from '@/app/components/ui/tree-loader';
import Aoscompo from '@/utils/aos';
import ScrollToTop from '@/app/components/ScrollToTop';
import { ReduxProvider } from '@/app/providers/ReduxProvider';

import 'react-toastify/dist/ReactToastify.css';
import 'animate.css';
import '@/public/styles/style.css';

const customConfig = defineConfig({
  ...defaultConfig,
});

const system = createSystem(customConfig);

interface ClientProvidersProps {
  children: ReactNode;
}

const ClientProviders: React.FC<ClientProvidersProps> = ({ children }) => {
  const [mounted, setMounted] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [contentVisible, setContentVisible] = useState<boolean>(false);
  const [scrollY, setScrollY] = useState<number>(0);

  const handleLoadComplete = (): void => {
    setLoading(false);
  };

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (!loading) {
      const timer = setTimeout(() => {
        setContentVisible(true);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setContentVisible(false);
    }
  }, [loading]);

  useEffect(() => {
    if (!mounted) return;

    if (loading) {
      // Save the current scroll position
      setScrollY(window.scrollY);
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.top = `-${window.scrollY}px`;
    } else {
      // Restore scroll position
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      window.scrollTo(0, scrollY);
    }

    return () => {
      // Cleanup function to ensure styles are reset if component unmounts
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
    };
  }, [loading, mounted, scrollY]);

  // Early return for server-side rendering
  if (typeof window === 'undefined') {
    return (
      <ChakraProvider value={system}>
        <AudioProvider>
          <div className="opacity-0">
            {children}
          </div>
        </AudioProvider>
      </ChakraProvider>
    );
  }

  return (
    <ReduxProvider>
      <ChakraProvider value={system}>
        <AudioProvider>
          {loading && <Loader onLoadComplete={handleLoadComplete} />}
          <Aoscompo>
            <div className={contentVisible ? 'opacity-100' : 'opacity-0'}>
              {children}
            </div>
          </Aoscompo>
          <ScrollToTop />
          <ToastContainer
            position="bottom-left"
            autoClose={5000}
            style={{ zIndex: 100001 }}
            icon={false}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            draggable
            pauseOnHover
            theme="colored"
          />
        </AudioProvider>
      </ChakraProvider>
    </ReduxProvider>
  );
};

export { ClientProviders };