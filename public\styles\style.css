@font-face {
    font-family: 'Arkhip';
    src: url('/fonts/Arkhip.eot');
    src: url('/fonts/Arkhip.eot?#iefix') format('embedded-opentype'),
        url('/fonts/Arkhip.woff2') format('woff2'),
        url('/fonts/Arkhip.woff') format('woff'),
        url('/fonts/Arkhip.ttf') format('truetype'),
        url('/fonts/Arkhip.svg#Arkhip') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.footer-social-icons svg {
    fill: #fff;
    width: 25px;
    height: 25px;
}

.star-five {
    background: transparent;
    margin: 50px 0;
    position: relative;
    display: block;
    color: var(--darkGold);
    width: 0px;
    height: 0px;
    border-right: 100px solid transparent;
    border-bottom: 70px solid var(--darkGold);
    border-left: 100px solid transparent;
    /* transform: rotate(35deg) scale(0.1) translate(-1450px, -250px); */
}

.star-five:before {
    border-bottom: 80px solid var(--darkGold);
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    background-color: transparent;
    position: absolute;
    height: 0;
    width: 0;
    top: -45px;
    left: -65px;
    display: block;
    content: '';
    transform: rotate(-35deg);
}

.star-five:after {
    background-color: transparent;
    position: absolute;
    display: block;
    color: #ffffff;
    top: 3px;
    left: -105px;
    width: 0px;
    height: 0px;
    border-right: 100px solid transparent;
    border-bottom: 70px solid #ffffff;
    border-left: 100px solid transparent;
    transform: rotate(-70deg);
    content: '';
}

.banner-mask::after {
    content: '';
    position: absolute;
    pointer-events: none;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle farthest-side at 0 0, rgba(255, 255, 255, 0) 90%, rgba(255, 255, 255, 1) 98%, rgba(255, 255, 255, 0) 100%) no-repeat;
    background-position: inherit;
    -webkit-mask: url('/images/logo-banner.webp') center center / contain no-repeat;
    mask: url('/images/logo-banner.webp') center center / contain no-repeat;
}

.banner-mask {
    position: relative;
    background-position: -640px 0;
    transition: all 9s;
    animation: backgroundShine 8s infinite forwards;
    aspect-ratio: 1/1.1;
}

.banner-mask:hover,
.banner-mask:focus {
    background-position: 60px 0;
    /*change speed to see in slow motion*/
    transition: all 7s;
}

.button-glare:hover a,
.button-glare.hover a {
    color: #fff;
}

.button-glare {
    overflow: hidden;
    position: relative;
    display: inline-block;
}

.button-glare:before {
    content: "";
    position: absolute;
    top: -110%;
    left: -210%;
    width: 200%;
    height: 200%;
    opacity: 0;
    transform: rotate(-30deg);

    background: rgba(255, 255, 255, 0.5);
    background: linear-gradient(to right,
            rgba(255, 255, 255, 0.5) 0%,
            rgba(255, 255, 255, 0.5) 77%,
            rgba(255, 255, 255, 0.5) 92%,
            rgba(255, 255, 255, 0.0) 100%);
}

/* Hover state - trigger effect */

.button-glare:hover:before,
.button-glare.hover:before {
    opacity: 1;
    top: 60%;
    left: 60%;
    transition-property: left, top, opacity;
    transition-duration: 0.7s, 0.7s, 0.15s;
    transition-timing-function: ease;
}

/* Active state */

.button-glare:active:before {
    opacity: 0;
}

/* Custom scrollbar styles */
html .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

html .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

html .custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--darkGold);
    border-radius: 10px;
}

html .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--gold);
}

@keyframes backgroundShine {
    0% {
        background-position: -640px 0;
    }

    10% {
        background-position: -640px 0;
    }

    20% {
        background-position: 840px 0;
    }

    100% {
        background-position: 840px 0;
    }
}




.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loader {
    width: 200px;
    height: 200px;
}

.loader-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 200px;
}

.loading-progress-container {
    width: 100%;
    height: 6px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
    margin-top: 30px;
}

.loading-progress-bar {
    height: 100%;
    background-color: var(--darkGold);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.loading-percentage {
    position: absolute;
    top: 10px;
    left: 0;
    width: 100%;
    text-align: center;
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

@keyframes animation {
    0% {
        stroke-dasharray: 1 98;
        stroke-dashoffset: -105;
    }

    50% {
        stroke-dasharray: 80 10;
        stroke-dashoffset: -160;
    }

    100% {
        stroke-dasharray: 1 98;
        stroke-dashoffset: -300;
    }
}

#spinner {
    transform-origin: center;
    animation-name: animation;
    animation-duration: 1.2s;
    animation-timing-function: cubic-bezier;
    animation-iteration-count: infinite;
}


@media all and (min-width: 1024px) and (max-width: 1280px) {}

@media all and (min-width: 768px) and (max-width: 1024px) {}

@media all and (min-width: 480px) and (max-width: 768px) {}

@media all and (max-width: 1024px) {
    html .curtain-text-container {
        width: 100%;
        top: 60%;
    }
}

@media all and (max-width: 960px) {
    html .pull-rope-container {
        right: 1%;
        height: 180px;
        top: 0;
    }
}

@media all and (max-width: 480px) {
    html .pull-rope-container {
        right: 1%;
        height: 180px;
        top: 0;
    }

    html .pull-rope {
        height: 100%;
        max-height: 170px;
    }

    html .curtain-text-container {
        width: 100%;
        top: 60%;
    }
}

@media all and (max-width: 320px) {
    html .pull-rope {
        height: 100%;
        max-height: 145px;
    }
}

@media all and (max-width: 280px) {
    html .pull-rope {
        height: 100%;
        max-height: 45px;
    }

    html .curtain-text {
        font-size: 13px;
        line-height: 1;
    }

    html .event-countdown .number-container .event-countdown-number {
        font-size: 18px;
        line-height: 1;
    }
}

@media (max-width: 1199px) and (min-width: 992px) {}

@media only screen and (min-device-width: 1400px) {}

@media only screen and (min-device-width: 2500px) {}

@media only screen and (min-device-width: 1900px) and (max-device-width: 2000px) {}

@media only screen and (min-device-width: 1600px) and (max-device-width: 1700px) {}

@media only screen and (max-device-width: 1600px) and (max-device-height: 950px) and (orientation: landscape) {}

@media only screen and (min-device-width: 1586px) and (min-device-width: 1586px) and (max-device-height: 864px) and (min-device-height: 864px) {}

@media only screen and (min-device-width: 1400px) and (max-device-width: 1500px) {}

@media only screen and (min-device-width: 1300px) and (max-device-width: 1400px) {}

@media only screen and (min-device-width: 1400px) and (max-device-width: 1480px) and (max-device-height: 950px) and (orientation: landscape) {}

@media only screen and (max-device-width: 1280px) and (max-device-height: 950px) and (orientation: landscape) {}

@media only screen and (max-device-width: 1280px) and (max-device-height: 850px) and (orientation: landscape) {}

@media only screen and (max-device-height: 500px) {}

@media only screen and (max-device-width: 1100px) and (max-device-height: 2000px) and (orientation: portrait) {}

@media only screen and (max-device-width: 1050px) and (max-device-height: 1400px) and (orientation: portrait) {}

@media only screen and (max-device-width: 1050px) and (min-device-height: 1200px) and (max-device-height: 1400px) and (orientation: portrait) {}

@media only screen and (min-device-width: 1280px) and (max-device-height: 1100px) and (orientation: landscape) {}

@media only screen and (min-device-width: 1110px) and (max-device-width: 1130px) and (max-height: 750px) and (orientation: landscape) {}

@media only screen and (max-device-width: 1024px) and (max-device-height: 1800px) and (orientation: portrait) {}

@media only screen and (max-device-width: 900px) and (max-device-height: 500px) and (orientation: landscape) {}

@media only screen and (min-device-width: 950px) and (max-device-width: 990px) and (max-device-height: 650px) and (orientation: landscape) {}

@media only screen and (max-device-width: 870px) and (max-device-height: 415px) and (orientation: landscape) {}

@media only screen and (max-device-width: 850px) and (max-device-height: 1300px) and (orientation: portrait) {}

@media only screen and (min-device-width: 840px) and (max-device-width: 870px) and (max-device-height: 490px) and (orientation: landscape) {}

@media only screen and (min-device-width: 810px) and (max-device-width: 840px) and (max-device-height: 430px) and (orientation: landscape) {}

@media only screen and (max-device-width: 750px) and (max-device-height: 1300px) and (orientation: portrait) {}

@media only screen and (min-device-width: 750px) and (max-device-width: 800px) and (max-device-height: 1024px) and (orientation: portrait) {}

@media only screen and (max-device-width: 760px) and (orientation: landscape) {}

@media only screen and (min-device-width: 710px) and (max-device-width: 730px) and (max-device-height: 1120px) and (orientation: portrait) {}

@media only screen and (min-device-width: 710px) and (max-device-width: 730px) and (max-device-height: 550px) and (orientation: landscape) {}

@media only screen and (min-device-width: 720px) and (max-device-width: 750px) and (max-device-height: 430px) and (orientation: landscape) {}

@media only screen and (min-device-width: 660px) and (max-device-width: 680px) and (max-device-height: 390px) and (orientation: landscape) {}

@media only screen and (min-device-width: 640px) and (max-device-width: 660px) and (max-device-height: 290px) and (orientation: landscape) {}

@media only screen and (min-device-width: 630px) and (max-device-width: 650px) and (max-device-height: 390px) and (orientation: landscape) {
    html .pull-rope-container {
        right: 1%;
        height: 180px;
        top: 0;
    }

    html .pull-rope {
        height: 110px;
    }

    html .curtain-text-container {
        width: 100%;
        top: 65%;
    }
}

@media only screen and (min-device-width: 540px) and (max-device-width: 570px) and (max-device-height: 330px) and (orientation: landscape) {}

@media only screen and (min-device-width: 520px) and (max-device-width: 540px) and (max-device-height: 330px) and (orientation: landscape) {}

@media only screen and (min-device-width: 510px) and (max-device-width: 530px) and (max-device-height: 730px) and (orientation: portrait) {}

@media only screen and (min-device-width: 460px) and (max-device-width: 490px) and (max-device-height: 330px) and (orientation: landscape) {}

@media only screen and (max-device-width: 600px) and (max-device-height: 1024px) and (orientation: portrait) {}

@media only screen and (max-device-width: 600px) and (max-device-height: 850px) and (orientation: portrait) {}

@media only screen and (max-device-width: 480px) and (max-device-height: 854px) and (orientation: portrait) {}

@media only screen and (max-device-width: 450px) and (max-device-height: 854px) and (orientation: portrait) {}

@media only screen and (max-device-width: 430px) and (max-device-height: 900px) and (orientation: portrait) {}

@media only screen and (max-device-width: 415px) and (max-device-height: 870px) and (orientation: portrait) {}

@media only screen and (max-device-width: 400px) and (max-device-height: 800px) and (orientation: portrait) {}

@media only screen and (max-device-width: 430px) and (max-device-height: 750px) and (orientation: portrait) {}

@media only screen and (max-device-width: 400px) and (max-device-height: 700px) and (orientation: portrait) {}

@media only screen and (max-device-width: 400px) and (max-device-height: 640px) and (orientation: portrait) {}

@media only screen and (max-device-width: 384px) and (max-device-height: 640px) and (orientation: portrait) {}

@media only screen and (max-device-width: 375px) and (max-device-height: 812px) and (orientation: portrait) {}

@media only screen and (max-device-width: 375px) and (max-device-height: 700px) and (orientation: portrait) {}

@media only screen and (max-device-width: 360px) and (max-device-height: 750px) and (orientation: portrait) {}

@media only screen and (max-device-width: 360px) and (max-device-height: 670px) and (orientation: portrait) {}

@media only screen and (max-device-width: 360px) and (max-device-height: 640px) and (orientation: portrait) {}

@media only screen and (max-device-width: 320px) and (max-device-height: 550px) and (orientation: portrait) {}

@media only screen and (max-device-width: 320px) and (max-device-height: 500px) and (orientation: portrait) {}

@media only screen and (max-device-width: 240px) and (max-device-height: 400px) and (orientation: portrait) {}

@media only screen and (max-device-width: 240px) and (max-device-height: 400px) and (orientation: portrait) {}

@media only screen and (max-device-height: 400px) {}