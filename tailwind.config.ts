import type { Config } from "tailwindcss";
import { default as flattenColorPalette } from "tailwindcss/lib/util/flattenColorPalette";

export default {
  content: ["./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          "Inter",
          "ui-sans-serif",
          "system-ui",
          "sans-serif",
          "Apple Color Emoji",
          "Segoe UI Emoji",
          "Segoe UI Symbol",
          "Noto Color Emoji",
        ],
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        darkGold: "var(--darkGold)",
        gold: "var(--gold)",
      },
      maxWidth: {
        // Your custom overrides
        custom: '800px',
        '2xl': '42rem',
        '3xl': '48rem',
        '4xl': '56rem',
        '5xl': '64rem',
        '6xl': '72rem',
        '7xl': '80rem',
        '8xl': '90rem',
        '9xl': '100rem',
        '10xl': '110rem',
        '11xl': '120rem',
        '12xl': '130rem',
        '13xl': '140rem',
        '14xl': '150rem',
        '15xl': '160rem',
        '16xl': '170rem',
        '17xl': '180rem',
        '18xl': '190rem',
        '19xl': '200rem',
      },
    },
    screens: {
      'xs': '480px',    // Small screens (phones)
      'sm': '640px',    // Small screens
      'md': '768px',    // Medium screens
      'lg': '1024px',   // Large screens
      'xl': '1280px',   // Extra-large screens
      '2xl': '1536px',  // 2x extra-large screens
    },
  },
  plugins: [addVariablesForColors],
} satisfies Config;

function addVariablesForColors({ addBase, theme }: any) {
  const allColors = flattenColorPalette(theme("colors"));
  const newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}