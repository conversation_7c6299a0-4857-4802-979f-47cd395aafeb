export function HeadScripts() {
  return (
    <>
      {/* JSON-LD <PERSON>a <PERSON> */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Event",
            "name": "The Convention Before Christmas",
            "description": "Dublin's magical Christmas experience featuring Craft & Collectibles, Live Shows, Fun Fair, Toy Fair, Comic Con, Christmas Market and more!",
            "startDate": "2025-12-06T09:00",
            "endDate": "2025-12-07T17:00",
            "location": {
              "@type": "Place",
              "name": "The National Show Centre",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "Stockhole Lane, Cloghran",
                "addressLocality": "Swords",
                "addressRegion": "Dublin",
                "postalCode": "",
                "addressCountry": "IE"
              }
            },
            "organizer": {
              "@type": "Organization",
              "name": "Hidden Dublin Ltd",
              "url": "https://www.hiddendublintours.com"
            },
            "offers": {
              "@type": "Offer",
              "url": "https://theconbeforechristmas.ie",
              "availability": "https://schema.org/InStock"
            }
          })
        }}
      />

      {/* Google Analytics */}
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-TRMVV2VYWG"></script>
      <script
        dangerouslySetInnerHTML={{
          __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag() {
                    window.dataLayer.push(arguments);
                }
                gtag('js', new Date());
                gtag('config', 'G-TRMVV2VYWG');
            `
        }}
      />
    </>
  );
}