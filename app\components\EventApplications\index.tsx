import React, { useState, useEffect, useRef } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Button } from "@/app/components/ui/button";
import { toast } from "react-toastify";
import { useAnalytics } from '@/app/hooks/useAnalytics';

// Define the form schema with conditional validation and superRefine function to check field requirements
const formSchema = z.object({
  type: z.string().min(1, "Please select what you're interested in"),
  applicantName: z.string().min(2, "Applicant name is required").optional(),
  contactEmail: z.string().email("Invalid email address").optional(),
  // Remove .min() here so it's conditionally enforced in superRefine
  businessName: z.string().optional(),
  website: z.string().optional(),
  phoneNumber: z.string().min(5, "Contact number is required").optional(),
  typesOfGoods: z.string().optional(),
  socialMediaLinks: z.string().optional(),
  country: z.string().optional(),
  vatNumber: z.string().optional(),
  introduction: z.string().optional(),
}).superRefine((data: any, ctx: any) => {
  // Only validate fields that are relevant to the selected type
  const requiredFields: Record<string, string[]> = {
    applicantName: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    contactEmail: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    businessName: ["artist", "exhibitor", "advertiser"], // Not required for guest
    website: ["exhibitor", "artist", "advertiser", "performer", "costume"],
    phoneNumber: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    typesOfGoods: ["artist", "exhibitor", "advertiser"],
    socialMediaLinks: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    country: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    vatNumber: ["artist", "exhibitor", "advertiser"],
  };

  // Check each field individually and add specific error messages
  for (const [field, types] of Object.entries(requiredFields)) {
    const config = fieldConfig[field as keyof typeof fieldConfig];
    const isFieldRequiredForType = types.includes(data.type);
    const isFieldShownForType = config?.showFor?.includes(data.type);

    if (isFieldRequiredForType && config?.required && isFieldShownForType) {
      const value = data[field as keyof typeof data];
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${config.label || field} is required`,
          path: [field as string],
        });
      }
    }
  }
});

// Define field configuration map
type FieldType = {
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  shortDescription?: string;
  getLabel?: (type: string) => string;
  getPlaceholder?: (type: string) => string;
  showFor?: string[];
  gridColumn?: boolean;
  alwaysShow?: boolean;
  options?: Array<{ value: string; label: string }>;
  isTextArea?: boolean;
};

type FieldConfig = {
  [K in keyof z.infer<typeof formSchema>]: FieldType;
};

// Update the fieldConfig definition
const fieldConfig: FieldConfig = {
  type: {
    name: "type",
    label: "I want to go as:",
    placeholder: "-Select-",
    options: [
      { value: "artist", label: "Artist" },
      { value: "guest", label: "Guest" },
      { value: "exhibitor", label: "Exhibitor" },
      { value: "content_creator", label: "Content Creator" },
      { value: "advertiser", label: "Advertiser" },
      { value: "performer", label: "Performer" },
      { value: "costume", label: "Costume Club" },
    ],
    alwaysShow: true,
  },
  applicantName: {
    name: "applicantName",
    label: "Applicant's Name",
    placeholder: "Your name",
    showFor: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    gridColumn: true,
    required: true
  },
  contactEmail: {
    name: "contactEmail",
    label: "Email Address",
    placeholder: "<EMAIL>",
    showFor: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    required: true
  },
  businessName: {
    name: "businessName",
    getLabel: (type: string) => type === 'guest' ? 'Full Name' : 'Company Name',
    placeholder: "Your company name",
    showFor: ["artist", "exhibitor", "advertiser"],
    gridColumn: true,
    required: true
  },
  website: {
    name: "website",
    label: 'Website or Social Media',
    placeholder: "Enter your website or social media link",
    showFor: ["exhibitor", "artist", "advertiser", "performer", "costume"],
    required: true
  },
  phoneNumber: {
    name: "phoneNumber",
    label: "Contact Number",
    placeholder: "+353",
    showFor: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    required: true
  },
  typesOfGoods: {
    name: "typesOfGoods",
    label: "Products Being Sold",
    placeholder: "What types of products will you be selling?",
    showFor: ["artist", "exhibitor", "advertiser"],
    required: true
  },
  socialMediaLinks: {
    name: "socialMediaLinks",
    label: "Social Media Link",
    placeholder: "Type your social media link",
    showFor: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    required: true
  },
  country: {
    name: "country",
    label: "Country",
    placeholder: "Ireland",
    showFor: ["artist", "guest", "exhibitor", "content_creator", "advertiser", "performer", "costume"],
    required: true
  },
  vatNumber: {
    name: "vatNumber",
    label: "VAT/PPS Number or tax equivalent",
    shortDescription: "Anyone selling goods or services during the event, including traders, artists, and others, in accordance with Irish Revenue regulations, Irish and non-ROI traders who wish to purchase a trade booth or table must have a valid VAT number, PPS number, social security number, or its equivalent. Please fill it out here.",
    placeholder: "VAT/PPS Number",
    showFor: ["artist", "exhibitor", "advertiser"],
    required: true
  },
  introduction: {
    name: "introduction",
    label: "Introduction",
    placeholder: "Tell us a bit about yourself",
    showFor: ["artist", "exhibitor", "advertiser", "performer", "costume", "guest"],
    required: false,
    isTextArea: true
  }
} as any;

const EventApplications = () => {
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [isButtonHighlighted, setIsButtonHighlighted] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const santaAudioRef = useRef<HTMLAudioElement | null>(null);

  // Analytics tracking
  const { trackFormJourney } = useAnalytics();

  const Toast = () => toast('');

  const settings = {
    dots: false,
    infinite: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    autoplay: true,
    cssEase: "linear",
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
        }
      },
      {
        breakpoint: 700,
        settings: {
          slidesToShow: 1,
        }
      },
      {
        breakpoint: 500,
        settings: {
          slidesToShow: 1,
        }
      }
    ]
  };

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "",
      applicantName: "",
      contactEmail: "",
      businessName: "",
      website: "",
      phoneNumber: "",
      typesOfGoods: "",
      socialMediaLinks: "",
      country: "",
      vatNumber: "",
      introduction: "",
    },
  });

  // Watch for changes to the type field
  useEffect(() => {
    const subscription = form.watch((value: any, { name }: any) => {
      if (name === 'type') {
        setSelectedType(value.type || null);
      }
    }) as any;
    return () => subscription.unsubscribe();
  }, [form.watch]);

  // Add this at the top of your component
  useEffect(() => {
    const originalConsoleError = console.error;
    console.error = (...args) => {
      originalConsoleError(...args);
      if (typeof args[0] === 'string' && args[0].includes('form')) {
        toast.error("Form error detected. Check console for details.");
      }
    };

    return () => {
      console.error = originalConsoleError;
    };
  }, []);

  const clearFormFields = () => {
    form.reset();
    setSelectedType(null);
  };

  // Update the onSubmit function to handle the validation better
  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Get visible fields
    const visibleFields = Object.keys(fieldConfig).filter(key => {
      const config = fieldConfig[key as keyof typeof fieldConfig];
      return config?.alwaysShow || (config?.showFor && selectedType && config?.showFor.includes(selectedType as string));
    });

    // Check if there are any validation errors
    if (Object.keys(form.formState.errors).length > 0) {
      // Check if errors are for fields that should be shown
      const relevantErrors = Object.entries(form.formState.errors)
        .filter(([key]) => visibleFields.includes(key));

      if (relevantErrors.length > 0) {
        toast.error("Please fix the highlighted fields");
        return;
      }
    }

    setIsSending(true);

    // Track event application started
    trackFormJourney.eventApplicationStarted(values.type);

    try {
      // Filter out empty fields and fields not relevant to the selected type
      const relevantFields = Object.keys(fieldConfig).filter(key => {
        const config = fieldConfig[key as keyof typeof fieldConfig];
        return config?.alwaysShow || (config?.showFor && config?.showFor.includes(values.type));
      });

      const filteredValues = Object.fromEntries(
        Object.entries(values).filter(([key, value]) =>
          relevantFields.includes(key) && value !== ""
        )
      );

      // Add selected type as a label
      const typeLabel = fieldConfig.type.options?.find(
        option => option.value === values.type
      )?.label || values.type;

      const dataToSubmit = {
        ...filteredValues,
        typeLabel
      };

      const response = await fetch("/api/event-application", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dataToSubmit),
      });

      const responseData = await response.json();

      if (response.ok) {
        // Play santa sound on successful submission
        if (santaAudioRef.current) {
          santaAudioRef.current.currentTime = 0;
          santaAudioRef.current.play().catch(error => {
            console.error("Audio playback failed:", error);
          });
        }

        setIsSubmitted(true);
        clearFormFields();

        // Track successful event application completion
        trackFormJourney.eventApplicationCompleted(values.type, values.applicantName || '', values.contactEmail || '');
      } else {
        throw new Error(responseData.message || "Failed to submit application");
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      toast.error(error.message || "An error occurred while submitting your application");

      // Track failed event application
      trackFormJourney.eventApplicationFailed(values.type, error.message || "Failed to submit application");
    } finally {
      setIsSending(false);
    }
  };

  // Helper function to render form fields based on configuration
  const renderField = (fieldName: keyof z.infer<typeof formSchema>) => {
    const config = fieldConfig[fieldName];
    const hasError = !!form.formState.errors[fieldName];

    return (
      <FormField
        key={fieldName}
        control={form.control}
        name={fieldName}
        render={({ field }) => (
          <FormItem className={`${config?.alwaysShow ? "mb-8" : ""} ${hasError ? "border-l-4 border-red-500 pl-2" : ""}`}>
            <FormLabel className="text-white text-xl">
              {typeof config?.label === 'string'
                ? config?.label
                : config?.getLabel?.(selectedType || '')}
              {config?.required && <span className="text-red-500 ml-1">*</span>}
            </FormLabel>

            {config?.shortDescription && (
              <p className="text-gray-400 text-sm mb-2">{config.shortDescription}</p>
            )}

            {fieldName === 'type' ? (
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  setSelectedType(value);
                }}
                value={field.value || ""}
              >
                <FormControl>
                  <SelectTrigger className="bg-white/10 border-white/20 text-white">
                    <SelectValue placeholder={config?.placeholder} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="bg-black/70 border-white/20">
                  {config?.options?.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : config?.isTextArea ? (
              <FormControl>
                <Textarea
                  placeholder={typeof config?.placeholder === 'string'
                    ? config?.placeholder
                    : config?.getPlaceholder?.(selectedType || '')}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400 min-h-[120px]"
                  {...field}
                />
              </FormControl>
            ) : (
              <FormControl>
                <Input
                  placeholder={typeof config?.placeholder === 'string'
                    ? config?.placeholder
                    : config?.getPlaceholder?.(selectedType || '')}
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                  {...field}
                />
              </FormControl>
            )}
            <FormMessage className="text-red-500" />
          </FormItem>
        )}
      />
    );
  };

  // Get grid fields and non-grid fields
  const getGridFields = () => {
    return (Object.keys(fieldConfig) as Array<keyof typeof fieldConfig>)
      .filter(key => {
        const field = fieldConfig[key];
        return field?.gridColumn &&
          selectedType &&
          field?.showFor?.includes(selectedType);
      })
      .map(key => renderField(key));
  };

  const getNonGridFields = () => {
    return (Object.keys(fieldConfig) as Array<keyof typeof fieldConfig>)
      .filter(key => {
        const field = fieldConfig[key];
        return !field?.gridColumn &&
          !field?.alwaysShow &&
          selectedType &&
          field?.showFor?.includes(selectedType);
      })
      .map(key => renderField(key));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setIsButtonHighlighted(true);

      // Reset the highlight after a short duration
      setTimeout(() => {
        setIsButtonHighlighted(false);
      }, 1000); // Highlight for 1 second
    }, 5000); // Repeat every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-20">
      {/* Add the audio element */}
      <audio ref={santaAudioRef} preload="auto">
        <source src="/media/santa.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4">Event Applications</h2>
          <p className="text-gray-300">Apply to participate at the Con Before Christmas</p>
        </div>
      </div>
      <div className="w-full mb-12 overflow-hidden flex justify-center">
        <img className="w-auto h-[400px] object-cover" src="/images/trader-application/2.jpg" />
      </div>
      <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4 min-h-[60vh]">
        {isSubmitted ? (
          <div className="bg-green-900/30 border border-green-500 rounded-lg p-6 text-center my-8">
            <h3 className="text-2xl font-bold text-green-400 mb-4">Application Submitted!</h3>
            <p className="text-white text-lg">
              Your application was submitted successfully. One of our Elves will be in touch soon!
            </p>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit, (errors) => {

              // Check if errors are for fields that should be shown for the selected type
              const relevantErrors = Object.keys(errors).filter(key => {
                const config = fieldConfig[key as keyof typeof fieldConfig];
                // Only consider errors for fields that are visible AND required for the selected type
                return config &&
                  (config.alwaysShow || (config.showFor && selectedType && config.showFor.includes(selectedType))) &&
                  config.required;
              });

              if (relevantErrors.length > 0) {
                toast.error("Please complete all required fields");
              }
            })}
              className="max-w-2xl mx-auto space-y-6"
            >
              {/* Type selection - always visible */}
              {renderField('type')}

              {/* Conditional fields - only shown after type selection */}
              {selectedType && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {getGridFields()}
                  </div>

                  {getNonGridFields()}

                  <div className="w-full flex justify-center">
                    <Button
                      type="submit"
                      className={`button-glare overflow-hidden text-white bg-[var(--darkGold)] hover:bg-[#A67C00] px-2 py-2 rounded-md ${isButtonHighlighted ? 'hover' : ''}`}
                      disabled={isSending || form.formState.isSubmitting}
                      onClick={() => {
                        if (Object.keys(form.formState.errors).length > 0) {
                          toast.error("Please fix the errors in the form");
                        }
                      }}
                    >
                      {isSending ? 'Submitting...' : 'Submit Application'}
                    </Button>
                  </div>
                </>
              )}
            </form>
          </Form>
        )}
      </div>
    </section>
  );
};
export default EventApplications;