// app/routes/admin/dashboard.tsx  (or wherever your admin entry is)
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminRealtimeProvider } from '@/app/components/admin/AdminRealtimeProvider';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { DashboardOverview } from '@/app/components/admin/dashboard/DashboardOverview';

export default function AdminDashboard() {
  return (
    <ProtectedRoute>
      <AdminRealtimeProvider>
        <AdminLayout>
          <DashboardOverview />
        </AdminLayout>
      </AdminRealtimeProvider>
    </ProtectedRoute>
  );
};