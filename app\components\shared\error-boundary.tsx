'use client';

import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
    children: ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
    retryCount: number;
}

export class ErrorBoundary extends Component<Props, State> {
    public state: State = {
        hasError: false,
        retryCount: 0
    };

    public static getDerivedStateFromError(error: Error): State {
        return { hasError: true, error, retryCount: 0 };
    }

    public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // Log error to your error tracking service
        console.error('Uncaught error:', error, errorInfo);

        // You can add error reporting service integration here
        // Example: Sentry.captureException(error);
    }

    private handleRetry = () => {
        this.setState({ hasError: false, error: undefined, retryCount: this.state.retryCount + 1 });
    };

    private handleRefresh = () => {
        window.location.reload();
    };

    public render() {
        if (this.state.hasError) {
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white p-4">
                    <div className="max-w-md w-full space-y-8 text-center">
                        <h2 className="text-3xl font-bold">Oops! Something went wrong</h2>
                        <p className="text-gray-300">
                            {this.state.error?.message || 'We apologize for the inconvenience. Please try again.'}
                        </p>
                        <div className="flex flex-col gap-4 items-center">
                            <button
                                onClick={this.handleRetry}
                                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                disabled={this.state.retryCount >= 3}
                            >
                                {this.state.retryCount >= 3 ? 'Max retries reached' : 'Try Again'}
                            </button>
                            <button
                                onClick={this.handleRefresh}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                            >
                                Refresh Page
                            </button>
                        </div>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}