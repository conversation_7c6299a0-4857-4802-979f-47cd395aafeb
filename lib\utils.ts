import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

export function elFormatter(num: number, digits?: number) {
    const decimalOptions = {
        minimumFractionDigits: 0,
        maximumFractionDigits: digits || 2
    };

    if (num === 0) {
        return '0';
    }

    if (typeof num !== 'number') {
        num = parseFloat(num);
    }

    if (num < 10000) {
        return new Intl.NumberFormat('en-US', decimalOptions).format(num);
    }

    const lookup = [
        { value: 1e3, symbol: "k" },
        { value: 1e6, symbol: "M" },
        { value: 1e9, symbol: "G" },
        { value: 1e12, symbol: "T" },
        { value: 1e15, symbol: "P" },
        { value: 1e18, symbol: "E" }
    ];

    let divisor = lookup[lookup.length - 1].value;
    for (let i = lookup.length - 1; i >= 0; i--) {
        if (num >= lookup[i].value) {
            divisor = lookup[i].value;
            break;
        }
    }

    const formattedNum = (num / divisor).toFixed(digits || 2).replace(/(\.[0-9]*[1-9])0+$/, "$1");

    return formattedNum + (divisor > 1 ? lookup.find(l => l.value === divisor)?.symbol || "" : "");
};

// Cache duration in milliseconds (24 hours)
const IP_CACHE_DURATION = 24 * 60 * 60 * 1000;

export const getIpAddress = async () => {
    const userAgent = navigator.userAgent;
    const referrer = document.referrer || '';

    try {
        // Check for valid cache first
        const cachedResult = localStorage.getItem('cachedIPResults');
        if (cachedResult) {
            const { ipAddress, sessionId, timestamp } = JSON.parse(cachedResult);
            // Return cached result if it's still valid
            if (Date.now() - timestamp < IP_CACHE_DURATION) {
                return {
                    success: true,
                    message: 'IP address and session ID retrieved from cache',
                    ipAddress,
                    sessionId,
                    referrer,
                    userAgent
                };
            }
        }

        // If no valid cache, fetch fresh data
        const res = await fetch('https://api.ipify.org?format=json');
        if (!res.ok) throw new Error('Failed to fetch IP address');

        const data = await res.json();
        const ipAddress = data.ip;
        const sessionId = btoa(`${ipAddress}-${userAgent}`);

        // Cache the new result with timestamp
        localStorage.setItem('cachedIPResults', JSON.stringify({
            ipAddress,
            sessionId,
            referrer,
            userAgent,
            timestamp: Date.now()
        }));

        return {
            success: true,
            message: 'IP address and session ID retrieved successfully',
            ipAddress,
            sessionId,
            referrer,
            userAgent
        };
    } catch {
        return {
            success: false,
            message: 'Failed to obtain IP address and/or session ID',
            ipAddress: null,
            sessionId: null,
            referrer,
            userAgent
        }
    }
};