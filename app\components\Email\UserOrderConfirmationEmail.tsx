import * as React from 'react';
import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Img,
    Preview,
    Section,
    Text,
    Row,
    Column,
    Link,
} from '@react-email/components';

interface UserOrderConfirmationEmailProps {
    name: string;
    email: string;
    phoneNumber: string;
    tickets: Array<{
        ticketType: {
            name: string;
            price: number;
            id?: string;
            multiplier?: number;
        };
        quantity: number;
        selectedDay?: 'Saturday' | 'Sunday';
        santaSlot?: {
            id: number;
            day: string;
            timeRange: string;
            numChildren?: number;
        };
    }>;
    total: number;
    orderId: string;
    qrCodeDataUrl: string;
    charityMultiplier?: {
        multiplier: number;
        appliedAmount: number;
        originalAmount: number;
    };
}

const baseUrl = process.env.SITE_URL || 'https://theconventionbeforechristmas.ie';

// Styles
const main = {
    backgroundColor: '#121212',
    color: '#e0e0e0',
    fontFamily: 'Arial, sans-serif',
    margin: '0',
    padding: '20px',
};

const container = {
    maxWidth: '600px',
    margin: '0 auto',
    border: '1px solid #ab8e56',
    borderRadius: '5px',
    overflow: 'hidden',
};

const header = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '20px',
    height: '100px',
    textAlign: 'center' as const,
};

const headerText = {
    color: '#ffffff',
    margin: '0',
};

const bookingDetails = {
    padding: '20px',
    backgroundColor: '#1e1e1e',
};

const bookingId = {
    fontSize: '18px',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: '#ab8e56',
};

const bookingNote = {
    marginBottom: '20px',
    color: '#e0e0e0',
};

const eventBox = {
    display: 'flex',
    marginBottom: '20px',
    border: '1px solid #ab8e56',
    padding: '10px',
    backgroundColor: '#2a2a2a',
};

const divider = {
    height: '1px',
    backgroundColor: '#ab8e56',
    margin: '20px 0',
};

const footer = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '15px',
    textAlign: 'center' as const,
    fontSize: '12px',
    color: '#ffffff',
};

export const UserOrderConfirmationEmail = ({
    name,
    email,
    phoneNumber,
    tickets,
    total,
    orderId,
    qrCodeDataUrl,
    charityMultiplier,
}: UserOrderConfirmationEmailProps) => {
    const totalItems = tickets.reduce((sum, item) => sum + item.quantity, 0);

    const currentDate = new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });

    const purchaseDate = new Date().toLocaleString();

    return (
        <Html>
            <Head />
            <Preview>Ticket Confirmation - The Convention Before Christmas</Preview>
            <Body style={main}>
                <Container style={container}>
                    <Section style={header}>
                        <Heading as="h2" style={headerText}>Your Ticket Confirmation</Heading>
                    </Section>

                    <Section style={bookingDetails}>
                        <Text style={bookingId}>Booking #{orderId}</Text>
                        <Text style={bookingNote}>Thank you for your purchase, {name}!</Text>

                        <Row style={eventBox}>
                            <Column style={{ width: '100px', marginRight: '15px' }}>
                                <Img
                                    src={`${baseUrl}/images/logo-banner.webp`}
                                    alt="Event"
                                    width="100"
                                    height="100"
                                    style={{ objectFit: 'cover' }}
                                />
                            </Column>
                            <Column>
                                <Heading as="h3" style={{ marginTop: '0', marginBottom: '5px', color: '#ab8e56', marginLeft: '15px' }}>
                                    The Convention Before Christmas
                                </Heading>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '15px' }}>{currentDate}</Text>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '15px' }}>Total Items: {totalItems}</Text>
                            </Column>
                        </Row>

                        <Section>
                            <Text><strong>Name:</strong> {name}</Text>
                            <Text><strong>Email:</strong> {email}</Text>
                            <Text><strong>Phone:</strong> {phoneNumber}</Text>
                        </Section>

                        <Heading as="h3">Your Tickets</Heading>

                        {/* Table representation for email */}
                        {tickets.map((item, index) => (
                            <Section key={index} style={{ marginBottom: '10px', borderBottom: '1px solid #333' }}>
                                <Text>
                                    <strong>{item.ticketType.name}</strong>
                                    {item.selectedDay && (
                                        <Text style={{ color: '#ab8e56', fontSize: '12px' }}>
                                            ({item.selectedDay === 'Saturday' ? 'Saturday, December 6th, 2025' : 'Sunday, December 7th, 2025'})
                                        </Text>
                                    )}
                                    {/* Handle originalId for day-specific tickets */}
                                    {!item.selectedDay && item.ticketType.id && item.ticketType.id.includes('-saturday') && (
                                        <Text style={{ color: '#ab8e56', fontSize: '12px' }}>
                                            (Saturday, December 6th, 2025)
                                        </Text>
                                    )}
                                    {!item.selectedDay && item.ticketType.id && item.ticketType.id.includes('-sunday') && (
                                        <Text style={{ color: '#ab8e56', fontSize: '12px' }}>
                                            (Sunday, December 7th, 2025)
                                        </Text>
                                    )}
                                    {item.santaSlot && (
                                        <Text style={{ color: '#ab8e56', fontSize: '12px' }}>
                                            {item.santaSlot.day === 'Saturday' ? 'Saturday, December 6th, 2025' : 'Sunday, December 7th, 2025'}, {item.santaSlot.timeRange}
                                            {item.santaSlot.numChildren && item.santaSlot.numChildren > 1 && (
                                                <span>, {item.santaSlot.numChildren} children</span>
                                            )}
                                        </Text>
                                    )}
                                </Text>
                                <Row>
                                    <Column><Text>Quantity: {item.quantity}</Text></Column>
                                    <Column><Text>Price: €{item.ticketType.price.toFixed(2)}</Text></Column>
                                    <Column><Text>Subtotal: €{(item.ticketType.price * item.quantity).toFixed(2)}</Text></Column>
                                </Row>
                            </Section>
                        ))}

                        <Text style={{ textAlign: 'right', fontWeight: 'bold' }}>
                            Total: €{total.toFixed(2)} <span style={{ fontSize: '12px', fontWeight: 'normal' }}>(incl. 13.5% VAT)</span>
                        </Text>

                        <Section style={divider} />

                        <Heading as="h3">Order Information</Heading>
                        <Text>Order ID: {orderId}</Text>
                        <Text>Purchase Date: {purchaseDate}</Text>

                        <Section style={{ textAlign: 'center', margin: '20px 0' }}>
                            <Text style={{ marginBottom: '15px', textAlign: 'center' }}>
                                Please present this QR code at the entrance:
                            </Text>
                            <div style={{ textAlign: 'center', width: '100%' }}>
                                <Img
                                    src={qrCodeDataUrl}
                                    alt="Ticket QR Code"
                                    width={200}
                                    height={200}
                                    style={{ margin: '0 auto' }}
                                />
                            </div>
                        </Section>

                    </Section>

                    <Section style={footer}>
                        <Text style={{ marginBottom: '10px' }}>
                            We look forward to seeing you at The Convention Before Christmas!
                        </Text>

                        <Section style={{ textAlign: 'center', margin: '20px 0' }}>
                            <Text style={{ marginBottom: '10px' }}>
                                Follow us on social media:
                            </Text>
                            <Row>
                                <Column style={{ width: '25%', textAlign: 'center' }}>
                                    <Link href="https://www.facebook.com/profile.php?id=61572625690312" style={{ display: 'inline-block', textDecoration: 'none' }}>
                                        <Img
                                            src="https://cdn4.iconfinder.com/data/icons/social-media-logos-6/512/83-facebook-512.png"
                                            width="32"
                                            height="32"
                                            alt="Facebook"
                                            style={{ backgroundColor: '#ab8e56', padding: '8px', borderRadius: '8px' }}
                                        />
                                    </Link>
                                </Column>
                                <Column style={{ width: '25%', textAlign: 'center' }}>
                                    <Link href="https://instagram.com/theconbeforechristmas" style={{ display: 'inline-block', textDecoration: 'none' }}>
                                        <Img
                                            src="https://cdn2.iconfinder.com/data/icons/social-icons-33/128/Instagram-512.png"
                                            width="32"
                                            height="32"
                                            alt="Instagram"
                                            style={{ backgroundColor: '#ab8e56', padding: '8px', borderRadius: '8px' }}
                                        />
                                    </Link>
                                </Column>
                                <Column style={{ width: '25%', textAlign: 'center' }}>
                                    <Link href="https://youtube.com/@theconbeforechristmas" style={{ display: 'inline-block', textDecoration: 'none' }}>
                                        <Img
                                            src="https://cdn1.iconfinder.com/data/icons/logotypes/32/youtube-512.png"
                                            width="32"
                                            height="32"
                                            alt="YouTube"
                                            style={{ backgroundColor: '#ab8e56', padding: '8px', borderRadius: '8px' }}
                                        />
                                    </Link>
                                </Column>
                                <Column style={{ width: '25%', textAlign: 'center' }}>
                                    <Link href="https://www.tiktok.com/@the.con.before.christmas" style={{ display: 'inline-block', textDecoration: 'none' }}>
                                        <Img
                                            src="https://cdn4.iconfinder.com/data/icons/social-media-flat-7/64/Social-media_Tiktok-512.png"
                                            width="32"
                                            height="32"
                                            alt="TikTok"
                                            style={{ backgroundColor: '#ab8e56', padding: '8px', borderRadius: '8px' }}
                                        />
                                    </Link>
                                </Column>
                            </Row>
                        </Section>

                        <Text style={{ fontSize: '10px', marginTop: '15px' }}>
                            © {new Date().getFullYear()} The Convention Before Christmas. All rights reserved.
                        </Text>
                        <Text style={{ fontSize: '10px' }}>
                            If you have any questions, please contact us at <Link href="mailto:<EMAIL>" style={{ color: '#fff' }}><EMAIL></Link>
                        </Text>
                        <Text style={{ fontSize: '10px', marginTop: '5px' }}>
                            <Link href={`${baseUrl}/unsubscribe?email=${email}`} style={{ color: '#fff', textDecoration: 'underline' }}>
                                Unsubscribe from our emails
                            </Link>
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};
export default UserOrderConfirmationEmail;