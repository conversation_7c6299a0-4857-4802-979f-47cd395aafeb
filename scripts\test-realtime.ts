/**
 * Real-time Database Monitoring Test Script
 *
 * This script tests the real-time functionality by simulating database changes
 * and verifying that the admin interface receives and displays updates correctly.
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://trbcgeffweprfnovhggm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYmNnZWZmd2VwcmZub3ZoZ2dtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mjc1NjI0NSwiZXhwIjoyMDU4MzMyMjQ1fQ.ZkQQbOrwUN7Eqert9omwEPwLSj6itNr6vZB4dv1tXOI';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  duration?: number;
}

class RealtimeTestSuite {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Real-time Database Monitoring Tests\n');

    await this.testDatabaseConnection();
    await this.testOrderInsertion();
    await this.testOrderStatusUpdate();
    await this.testVendorStandInsertion();
    await this.testCharityDonationInsertion();
    await this.testMultipleRapidUpdates();

    this.printResults();
  }

  private async testDatabaseConnection(): Promise<void> {
    const startTime = Date.now();

    try {
      const { data, error } = await supabase
        .from('Orders')
        .select('count')
        .limit(1);

      if (error) throw error;

      this.addResult({
        test: 'Database Connection',
        passed: true,
        message: 'Successfully connected to Supabase database',
        duration: Date.now() - startTime
      });
    } catch (error) {
      this.addResult({
        test: 'Database Connection',
        passed: false,
        message: `Failed to connect: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async testOrderInsertion(): Promise<void> {
    const startTime = Date.now();

    try {
      const testOrder = {
        name: 'Test Customer',
        email: '<EMAIL>',
        total: 25.00,
        status: 'pending',
        payment_intent_id: `test_${Date.now()}`
      };

      const { data, error } = await supabase
        .from('Orders')
        .insert([testOrder])
        .select()
        .single();

      if (error) throw error;

      console.log('📝 Created test order:', data.id);

      this.addResult({
        test: 'Order Insertion',
        passed: true,
        message: `Successfully created order ${data.id}`,
        duration: Date.now() - startTime
      });

      // Clean up test data
      await this.cleanupTestOrder(data.id);

    } catch (error) {
      this.addResult({
        test: 'Order Insertion',
        passed: false,
        message: `Failed to create order: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async testOrderStatusUpdate(): Promise<void> {
    const startTime = Date.now();

    try {
      // First create a test order
      const testOrder = {
        name: 'Test Customer Update',
        email: '<EMAIL>',
        total: 30.00,
        status: 'pending',
        payment_intent_id: `test_update_${Date.now()}`
      };

      const { data: insertData, error: insertError } = await supabase
        .from('Orders')
        .insert([testOrder])
        .select()
        .single();

      if (insertError) throw insertError;

      // Wait a moment then update the status
      await new Promise(resolve => setTimeout(resolve, 1000));

      const { data: updateData, error: updateError } = await supabase
        .from('Orders')
        .update({ status: 'paid' })
        .eq('id', insertData.id)
        .select()
        .single();

      if (updateError) throw updateError;

      console.log('🔄 Updated order status:', updateData.id, 'to', updateData.status);

      this.addResult({
        test: 'Order Status Update',
        passed: true,
        message: `Successfully updated order ${updateData.id} status to ${updateData.status}`,
        duration: Date.now() - startTime
      });

      // Clean up test data
      await this.cleanupTestOrder(insertData.id);

    } catch (error) {
      this.addResult({
        test: 'Order Status Update',
        passed: false,
        message: `Failed to update order status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async testVendorStandInsertion(): Promise<void> {
    const startTime = Date.now();

    try {
      const testVendorStand = {
        business_name: 'Test Vendor Business',
        contact_name: 'Test Vendor',
        email: '<EMAIL>',
        phone: '************',
        stand_number: 999, // Use a high number to avoid conflicts
        price: 150.00,
        payment_status: 'pending',
        addons: [],
        discount_applied: false,
        discount_percentage: null,
        original_price: null
      };

      const { data, error } = await supabase
        .from('VendorStands')
        .insert([testVendorStand])
        .select()
        .single();

      if (error) throw error;

      console.log('🏪 Created test vendor stand:', data.id);

      this.addResult({
        test: 'Vendor Stand Insertion',
        passed: true,
        message: `Successfully created vendor stand ${data.id}`,
        duration: Date.now() - startTime
      });

      // Clean up test data
      await this.cleanupTestVendorStand(data.id);

    } catch (error) {
      this.addResult({
        test: 'Vendor Stand Insertion',
        passed: false,
        message: `Failed to create vendor stand: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async testCharityDonationInsertion(): Promise<void> {
    const startTime = Date.now();

    try {
      const testDonation = {
        amount: 50.00,
        metadata: {
          donor_name: 'Test Donor',
          donor_email: '<EMAIL>',
          message: 'Test donation for real-time monitoring'
        },
        multiplier: 1,
        original_amount: 50.00
      };

      const { data, error } = await supabase
        .from('CharityDonations')
        .insert([testDonation])
        .select()
        .single();

      if (error) throw error;

      console.log('💝 Created test charity donation:', data.id);

      this.addResult({
        test: 'Charity Donation Insertion',
        passed: true,
        message: `Successfully created charity donation ${data.id}`,
        duration: Date.now() - startTime
      });

      // Clean up test data
      await this.cleanupTestDonation(data.id);

    } catch (error) {
      this.addResult({
        test: 'Charity Donation Insertion',
        passed: false,
        message: `Failed to create charity donation: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async testMultipleRapidUpdates(): Promise<void> {
    const startTime = Date.now();

    try {
      console.log('⚡ Testing rapid updates (creating 5 orders quickly)...');

      const promises = Array.from({ length: 5 }, (_, i) => {
        const testOrder = {
          name: `Rapid Test Customer ${i + 1}`,
          email: `rapid-test-${i + 1}@example.com`,
          total: (i + 1) * 10,
          status: 'pending',
          payment_intent_id: `rapid_test_${Date.now()}_${i}`
        };

        return supabase
          .from('Orders')
          .insert([testOrder])
          .select()
          .single();
      });

      const results = await Promise.all(promises);
      const createdIds = results.map(result => result.data?.id).filter(Boolean);

      console.log('📊 Created orders:', createdIds);

      this.addResult({
        test: 'Multiple Rapid Updates',
        passed: true,
        message: `Successfully created ${createdIds.length} orders rapidly`,
        duration: Date.now() - startTime
      });

      // Clean up test data
      for (const id of createdIds) {
        if (id) await this.cleanupTestOrder(id);
      }

    } catch (error) {
      this.addResult({
        test: 'Multiple Rapid Updates',
        passed: false,
        message: `Failed rapid updates test: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration: Date.now() - startTime
      });
    }
  }

  private async cleanupTestOrder(id: string): Promise<void> {
    try {
      await supabase.from('Orders').delete().eq('id', id);
      console.log('🧹 Cleaned up test order:', id);
    } catch (error) {
      console.warn('⚠️ Failed to cleanup test order:', id);
    }
  }

  private async cleanupTestVendorStand(id: string): Promise<void> {
    try {
      await supabase.from('VendorStands').delete().eq('id', id);
      console.log('🧹 Cleaned up test vendor stand:', id);
    } catch (error) {
      console.warn('⚠️ Failed to cleanup test vendor stand:', id);
    }
  }

  private async cleanupTestDonation(id: string): Promise<void> {
    try {
      await supabase.from('CharityDonations').delete().eq('id', id);
      console.log('🧹 Cleaned up test donation:', id);
    } catch (error) {
      console.warn('⚠️ Failed to cleanup test donation:', id);
    }
  }

  private addResult(result: TestResult): void {
    this.results.push(result);
    const status = result.passed ? '✅' : '❌';
    const duration = result.duration ? ` (${result.duration}ms)` : '';
    console.log(`${status} ${result.test}: ${result.message}${duration}\n`);
  }

  private printResults(): void {
    console.log('\n📋 Test Results Summary');
    console.log('========================');

    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (passed === total) {
      console.log('\n🎉 All tests passed! Real-time functionality is working correctly.');
      console.log('\n📝 Manual Testing Instructions:');
      console.log('1. Open the admin panel at http://localhost:5174/admin');
      console.log('2. Navigate to the Transactions page');
      console.log('3. Run this script again to see real-time updates');
      console.log('4. Check that toast notifications appear');
      console.log('5. Verify dashboard metrics update automatically');
    } else {
      console.log('\n⚠️ Some tests failed. Please check the error messages above.');
    }
  }
}

// Run the tests
const testSuite = new RealtimeTestSuite();
testSuite.runAllTests().catch(console.error);