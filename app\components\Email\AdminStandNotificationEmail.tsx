import * as React from 'react';
import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Preview,
    Section,
    Text,
    Row,
    Column,
} from '@react-email/components';

interface AdminStandNotificationEmailProps {
    businessName: string;
    contactName: string;
    email: string;
    phone: string;
    standNumber: number;
    totalPrice: number;
    addons?: Array<{
        id: string;
        name: string;
        quantity: number;
        price: number;
    }>;
}

export const AdminStandNotificationEmail = ({
    businessName,
    contactName,
    email,
    phone,
    standNumber,
    totalPrice,
    addons = []
}: AdminStandNotificationEmailProps) => {

    const bookingDate = new Date().toLocaleString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }).replace(/(\d+)\/(\d+)\/(\d+),/, '$1/$2/$3,');

    return (
        <Html>
            <Head />
            <Preview>New Stand Booking: #{String(standNumber)}</Preview>
            <Body style={body}>
                <Container style={container}>
                    <Heading style={header}>New Stand Booking</Heading>
                    <Text style={bookingId}>Stand #{standNumber}</Text>
                    <Text style={customerName}>Business: {businessName}</Text>

                    <Section style={divider} />

                    <Section style={detailsSection}>
                        <Row>
                            <Column><Text style={detailLabel}>Business:</Text></Column>
                            <Column><Text style={detailValue}>{businessName}</Text></Column>
                        </Row>
                        <Row>
                            <Column><Text style={detailLabel}>Contact:</Text></Column>
                            <Column><Text style={detailValue}>{contactName}</Text></Column>
                        </Row>
                        <Row>
                            <Column><Text style={detailLabel}>Email:</Text></Column>
                            <Column><Text style={detailValue}>{email}</Text></Column>
                        </Row>
                        <Row>
                            <Column><Text style={detailLabel}>Phone:</Text></Column>
                            <Column><Text style={detailValue}>{phone}</Text></Column>
                        </Row>
                    </Section>

                    <Section style={divider} />

                    <Heading as="h3" style={sectionHeader}>Payment Details</Heading>
                    <Section style={table}>
                        <Row style={tableHeader}>
                            <Column><Text style={headerText}>Item</Text></Column>
                            <Column><Text style={headerText}>Quantity</Text></Column>
                            <Column><Text style={headerText}>Price</Text></Column>
                            <Column><Text style={headerText}>Subtotal</Text></Column>
                        </Row>

                        <Row style={tableRow}>
                            <Column><Text style={ticketName}>Stand #{standNumber}</Text></Column>
                            <Column><Text style={cellText}>1</Text></Column>
                            <Column><Text style={cellText}>€{totalPrice.toFixed(2)}</Text></Column>
                            <Column><Text style={cellText}>€{totalPrice.toFixed(2)}</Text></Column>
                        </Row>

                        {addons && addons.map((addon, index) => (
                            <Row key={index} style={tableRow}>
                                <Column><Text style={ticketName}>{addon.name}</Text></Column>
                                <Column><Text style={cellText}>{addon.quantity}</Text></Column>
                                <Column><Text style={cellText}>€{addon.price.toFixed(2)}</Text></Column>
                                <Column><Text style={cellText}>€{(addon.price * addon.quantity).toFixed(2)}</Text></Column>
                            </Row>
                        ))}
                    </Section>

                    <Section style={totalSection}>
                        <Row>
                            <Column></Column>
                            <Column></Column>
                            <Column><Text style={totalLabel}>Total:</Text></Column>
                            <Column><Text style={totalValue}>€{totalPrice.toFixed(2)}</Text></Column>
                        </Row>
                    </Section>

                    <Section style={divider} />

                    <Section style={orderInfo}>
                        <Text style={infoText}>Stand Number: {standNumber}</Text>
                        <Text style={infoText}>Booking Date: {bookingDate}</Text>
                    </Section>

                    <Section style={footer}>
                        <Text style={footerText}>
                            You received this email because a new stand booking was made on your website.
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};

// Styles
const body = {
    backgroundColor: '#ffffff',
    color: '#333333',
    fontFamily: 'Arial, sans-serif',
    margin: 0,
    padding: '20px',
};

const container = {
    maxWidth: '600px',
    margin: '0 auto',
};

const header = {
    fontSize: '24px',
    fontWeight: 'bold',
    margin: '0 0 10px 0',
    color: '#333333',
};

const bookingId = {
    fontSize: '18px',
    fontWeight: 'bold',
    margin: '0 0 5px 0',
    color: '#666666',
};

const customerName = {
    fontSize: '16px',
    margin: '0 0 20px 0',
    color: '#333333',
};

const divider = {
    height: '1px',
    backgroundColor: '#dddddd',
    margin: '20px 0',
};

const detailsSection = {
    margin: '0 0 20px 0',
};

const detailLabel = {
    fontWeight: 'bold',
    width: '80px',
    color: '#333333',
};

const detailValue = {
    color: '#666666',
};

const sectionHeader = {
    fontSize: '20px',
    fontWeight: 'bold',
    margin: '0 0 15px 0',
    color: '#333333',
};

const table = {
    width: '100%',
    margin: '0 0 20px 0',
};

const tableHeader = {
    backgroundColor: '#f5f5f5',
    padding: '10px 0',
    borderBottom: '2px solid #dddddd',
};

const headerText = {
    fontWeight: 'bold',
    color: '#333333',
};

const tableRow = {
    padding: '10px 0',
    borderBottom: '1px solid #eeeeee',
};

const ticketName = {
    color: '#333333',
    margin: 0,
};

const cellText = {
    color: '#666666',
    margin: 0,
};

const totalSection = {
    margin: '20px 0 0 0',
};

const totalLabel = {
    fontWeight: 'bold',
    color: '#333333',
    textAlign: 'right' as const,
};

const totalValue = {
    fontWeight: 'bold',
    color: '#333333',
};

const orderInfo = {
    margin: '20px 0',
};

const infoText = {
    color: '#666666',
    margin: '5px 0',
};

const footer = {
    margin: '30px 0 0 0',
    padding: '15px 0 0 0',
    borderTop: '1px solid #eeeeee',
};

const footerText = {
    fontSize: '12px',
    color: '#999999',
    textAlign: 'center' as const,
};

export default AdminStandNotificationEmail;


