"use client";

import React, { useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';

interface DaySelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDaySelect: (day: 'Saturday' | 'Sunday') => void;
}

const DaySelectionModal: React.FC<DaySelectionModalProps> = ({ isOpen, onClose, onDaySelect }) => {
  const [selectedDay, setSelectedDay] = useState<'Saturday' | 'Sunday'>('Saturday');
  const modalRef = useRef<HTMLDivElement>(null);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, onClose);

  const handleConfirm = () => {
    onDaySelect(selectedDay);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            ref={modalRef}
            className="bg-[var(--background)] rounded-xl p-6 w-full max-w-md max-h-[90vh] overflow-hidden flex flex-col"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">Select Day</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex-grow overflow-y-auto custom-scrollbar">
              <p className="text-white mb-4">Please select which day you would like to attend:</p>

              <div className="grid grid-cols-1 gap-4">
                <button
                  onClick={() => setSelectedDay('Saturday')}
                  className={`p-4 rounded-lg text-center transition-colors ${
                    selectedDay === 'Saturday'
                      ? 'bg-[var(--darkGold)] text-white'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  <div className="font-medium text-lg">Saturday</div>
                  <div className="text-sm mt-1">December 6th, 2024</div>
                </button>

                <button
                  onClick={() => setSelectedDay('Sunday')}
                  className={`p-4 rounded-lg text-center transition-colors ${
                    selectedDay === 'Sunday'
                      ? 'bg-[var(--darkGold)] text-white'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                  }`}
                >
                  <div className="font-medium text-lg">Sunday</div>
                  <div className="text-sm mt-1">December 7th, 2024</div>
                </button>
              </div>
            </div>

            <div className="w-full mt-6 flex justify-center gap-2">
              <button
                onClick={handleConfirm}
                className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a]"
              >
                Confirm
              </button>
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600"
              >
                Cancel
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default DaySelectionModal;