import { InstagramIcon, TikTokIcon, FacebookIcon, YouTubeIcon } from './social-icons';

interface Social {
    icon: React.ReactNode;
    href: string;
    label: string;
}

const socialLinks: Social[] = [
    {
        icon: <InstagramIcon />,
        href: "https://www.instagram.com/theconbeforechristmas",
        label: "Follow us on Instagram"
    },
    {
        icon: <TikTokIcon />,
        href: "https://www.tiktok.com/@the.con.before.christmas",
        label: "Follow us on TikTok"
    },
    {
        icon: <FacebookIcon />,
        href: "https://www.facebook.com/profile.php?id=61572625690312",
        label: "Follow us on Facebook"
    },
    {
        icon: <YouTubeIcon />,
        href: "https://www.youtube.com/@theconbeforechristmas",
        label: "Subscribe to our YouTube channel"
    }
];

interface SocialLinksProps {
    className?: string;
}

export const SocialLinks = ({ className = '' }: SocialLinksProps) => {
    return (
        <div className={`flex justify-center items-center gap-4 ${className}`}>
            {socialLinks.map((item, i) => (
                <a
                    href={item.href}
                    target="_blank"
                    rel="nofollow noreferrer noopener"
                    key={i}
                    className="relative w-10 h-10 flex items-center justify-center footer-social-icons border border-solid border-white rounded-md hover:scale-110 transition-transform cursor-pointer"
                    aria-label={item.label}
                >
                    {item.icon}
                </a>
            ))}
        </div>
    );
};