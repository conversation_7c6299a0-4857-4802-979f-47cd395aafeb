'use client';

import { ResponsivePie } from '@nivo/pie';
import { useTheme } from 'next-themes';
import { useMemo } from 'react';

export interface DeviceDataPoint {
  id: string;
  label: string;
  value: number;
  color?: string;
}

interface DeviceDistributionChartProps {
  data: DeviceDataPoint[];
  height?: number;
  colors?: string[];
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  theme?: 'light' | 'dark';
}

export function DeviceDistributionChart({
  data,
  height = 400,
  colors = ['#f59e0b', '#3b82f6', '#10b981', '#8b5cf6'],
  margin = { top: 40, right: 40, bottom: 60, left: 60 },
  theme: themeVariant = 'dark'
}: DeviceDistributionChartProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark' || themeVariant === 'dark';

  const textColor = isDark ? '#E5E7EB' : '#1F2937';
  const gridColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
  const tooltipBg = isDark ? '#1F2937' : '#FFFFFF';
  const tooltipBorder = isDark ? '#4B5563' : '#E5E7EB';
  const tooltipText = isDark ? '#F3F4F6' : '#111827';

  // Transform data for pie chart
  const pieData = useMemo(() => {
    return data.map((item, index) => ({
      id: item.id,
      label: item.label,
      value: item.value,
      color: item.color || colors[index % colors.length]
    }));
  }, [data, colors]);

  const chartTheme = useMemo(() => ({
    tooltip: {
      container: {
        background: tooltipBg,
        color: tooltipText,
        border: `1px solid ${tooltipBorder}`,
        borderRadius: '0.375rem',
        padding: '0.5rem 0.75rem',
        fontSize: '0.875rem',
        lineHeight: '1.25rem',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
      }
    },
    labels: {
      text: {
        fill: textColor,
        fontSize: 12,
        fontWeight: 500
      }
    },
    legends: {
      text: {
        fill: textColor,
        fontSize: 11
      }
    }
  }), [textColor, tooltipBg, tooltipBorder, tooltipText]);

  if (!pieData || pieData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full" style={{ height }}>
        <p className="text-amber-100/50">No device data available</p>
      </div>
    );
  }

  return (
    <div style={{ height, width: '100%' }}>
      <ResponsivePie
        data={pieData}
        margin={margin}
        innerRadius={0.5}
        padAngle={1}
        cornerRadius={4}
        activeOuterRadiusOffset={8}
        colors={pieData.map(d => d.color || '#f59e0b')}
        borderWidth={1}
        borderColor={{
          from: 'color',
          modifiers: [
            [
              'darker',
              0.2
            ]
          ]
        }}
        enableArcLinkLabels={true}
        arcLinkLabel={d => `${d.id}: ${d.value}`}
        arcLinkLabelsSkipAngle={10}
        arcLinkLabelsTextColor={textColor}
        arcLinkLabelsThickness={2}
        arcLinkLabelsColor={{ from: 'color' }}
        arcLabelsSkipAngle={10}
        arcLabelsTextColor={{
          from: 'color',
          modifiers: [
            [
              'darker',
              2
            ]
          ]
        }}
        theme={chartTheme}
        tooltip={({ datum: { id, value, color } }) => (
          <div
            className="p-2 rounded-md shadow-lg border flex flex-col gap-1"
            style={{
              background: tooltipBg,
              color: tooltipText,
              borderColor: tooltipBorder,
              fontSize: '0.875rem',
              lineHeight: '1.25rem'
            }}
          >
            <div className="font-semibold flex items-center gap-2">
              <span 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: color }}
              />
              {id}
            </div>
            <div className="text-amber-400">{value.toLocaleString()} users</div>
            <div className="text-xs text-amber-100/50">
              {((value / pieData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}% of total
            </div>
          </div>
        )}
        legends={[
          {
            anchor: 'right',
            direction: 'column',
            justify: false,
            translateX: 20,
            translateY: 0,
            itemsSpacing: 8,
            itemWidth: 100,
            itemHeight: 20,
            itemTextColor: textColor,
            itemDirection: 'left-to-right',
            itemOpacity: 1,
            symbolSize: 12,
            symbolShape: 'circle',
            effects: [
              {
                on: 'hover',
                style: {
                  itemTextColor: '#f59e0b',
                  itemOpacity: 1
                }
              }
            ]
          }
        ]}
        animate={true}
        motionConfig="gentle"
      />
    </div>
  );
};