import type { MetaFunction } from "@remix-run/react";
import { SocialLinks } from '@/app/components/shared/social-links';

export const meta: MetaFunction = () => {
    return [
        { title: "Event Schedule | The Con Before Christmas" },
        { name: "description", content: "Stay tuned for the full event schedule of The Con Before Christmas. Follow our social media for updates on exciting events and activities." },
    ];
};

export default function EventSchedulePage() {
    return (
        <main className="relative w-full h-full overflow-hidden">
            <div className="container mx-auto px-4 py-16 w-full h-svh">
                <div className="w-full mb-8">
                    <h1 className="text-4xl font-bold text-center mb-8">Event Schedule</h1>
                    <p className="text-xl text-center max-w-2xl mx-auto">
                        Our elves are busy finalizing plans with our sponsors, and the North Pole's secrets remain under wraps—for now! Stay tuned and keep an eye on our social media, where the full event schedule will be revealed closer to the big day. Exciting things are coming!
                    </p>
                </div>
                <div className="w-full flex flex-col items-center justify-center">
                    <h3 className="text-xl font-bold text-center mb-8">Social Media Links</h3>
                    <SocialLinks className="mb-8" />
                </div>
            </div>
        </main>
    );
}