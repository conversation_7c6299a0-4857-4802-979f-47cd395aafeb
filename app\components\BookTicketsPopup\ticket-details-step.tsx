"use client";

import React from 'react';
import { OrderDetails } from '@/types/ticket';

interface TicketDetailsStepProps {
  orderDetails: OrderDetails;
  setOrderDetails: React.Dispatch<React.SetStateAction<OrderDetails>>;
  isSending: boolean;
  handleSubmitDetails: (e: React.FormEvent) => void;
}

const TicketDetailsStep: React.FC<TicketDetailsStepProps> = ({
  orderDetails,
  setOrderDetails,
  isSending,
  handleSubmitDetails,
}) => {
  return (
    <div className="p-4">
      <div className="flex items-center mb-4">
        <h3 className="text-xl font-semibold text-white">
          Your Details
        </h3>
      </div>
      <form onSubmit={handleSubmitDetails}>
        <div>
          <label className="block text-white mb-2">Name</label>
          <input
            type="text"
            name="name"
            value={orderDetails.name}
            onChange={e => setOrderDetails({ ...orderDetails, name: e.target.value })}
            required
            className="w-full p-2 rounded bg-gray-800 text-white border border-[var(--darkGold)]"
          />
        </div>
        <div className="mt-3">
          <label className="block text-white mb-2">Email</label>
          <input
            type="email"
            name="email"
            value={orderDetails.email}
            onChange={e => setOrderDetails({ ...orderDetails, email: e.target.value })}
            required
            className="w-full p-2 rounded bg-gray-800 text-white border border-[var(--darkGold)]"
          />
        </div>
        <div className="mt-3">
          <label className="block text-white mb-2">Phone Number</label>
          <input
            type="tel"
            name="phoneNumber"
            value={orderDetails.phoneNumber}
            onChange={e => setOrderDetails({ ...orderDetails, phoneNumber: e.target.value })}
            required
            placeholder="+353"
            className="w-full p-2 rounded bg-gray-800 text-white border border-[var(--darkGold)]"
          />
        </div>
        <button
          type="submit"
          className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg disabled:opacity-50 mt-4"
          disabled={isSending}
        >
          {isSending ? 'Sending...' : 'Proceed to Payment'}
        </button>
      </form>
    </div>
  );
};
export default TicketDetailsStep;