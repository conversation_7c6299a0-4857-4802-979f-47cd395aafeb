import { useEffect } from 'react';
import { useLocation } from '@remix-run/react';
import { trackPageView } from '@/app/utils/analytics';

/**
 * Component to track page views and user interactions
 * Add this to your root layout or _app.tsx
 */
export function AnalyticsTracker() {
  const location = useLocation();

  // Track page views
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Track the initial page view with empty metadata
      trackPageView(location.pathname + location.search, { type: 'page_view' });

      // Track additional metrics after page load
      const handleLoad = () => {
        // Track time to interactive
        const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        console.log({ navTiming })
        if (navTiming) {
          // Track page load performance
          trackPageView(location.pathname + location.search, {
            type: 'page_load',
            metrics: {
              load_time: navTiming.loadEventEnd - navTiming.startTime,
              dom_content_loaded: navTiming.domContentLoadedEventEnd - navTiming.startTime,
              first_paint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
              first_contentful_paint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
            }
          });
        }
      };

      // Add event listeners for user interactions
      const handleClick = (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (target.closest('a, button, [role="button"], [data-track]')) {
          trackPageView(location.pathname + location.search, {
            type: 'click',
            target: target.tagName.toLowerCase(),
            text: target.textContent?.trim().substring(0, 100),
            href: (target as HTMLAnchorElement).href || null,
            id: target.id || null,
            class: target.className || null,
            dataset: target.dataset.track || null
          });
        }
      };

      // Set up event listeners
      window.addEventListener('load', handleLoad, { once: true });
      document.addEventListener('click', handleClick);

      // Clean up
      return () => {
        window.removeEventListener('load', handleLoad);
        document.removeEventListener('click', handleClick);
      };
    }
  }, [location]);

  return null;
};