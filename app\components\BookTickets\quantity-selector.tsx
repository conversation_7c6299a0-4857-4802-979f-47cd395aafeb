interface QuantitySelectorProps {
  quantity: number;
  onChange: (quantity: number) => void;
  min?: number;
  max?: number;
}

export function QuantitySelector({
  quantity,
  onChange,
  min = 0,
  max = 10
}: QuantitySelectorProps) {
  const handleDecrement = () => {
    if (quantity > min) {
      onChange(quantity - 1);
    }
  };

  const handleIncrement = () => {
    if (quantity < max) {
      onChange(quantity + 1);
    }
  };

  return (
    <div className="flex items-center gap-4">
      <button
        onClick={handleDecrement}
        disabled={quantity <= min}
        className={`p-2 rounded-lg transition-colors ${
          quantity <= min
            ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
            : 'bg-[var(--darkGold)] text-white hover:bg-[#95784a]'
        }`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      </button>

      <span className="text-xl font-semibold text-white w-8 text-center">
        {quantity}
      </span>

      <button
        onClick={handleIncrement}
        disabled={quantity >= max}
        className={`p-2 rounded-lg transition-colors ${
          quantity >= max
            ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
            : 'bg-[var(--darkGold)] text-white hover:bg-[#95784a]'
        }`}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
}