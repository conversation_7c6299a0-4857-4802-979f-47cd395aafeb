/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from 'react';
import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Preview,
    Section,
    Text,
    Row,
    Column,
} from '@react-email/components';

interface AdminOrderNotificationEmailProps {
    name: string;
    email: string;
    phoneNumber: string;
    tickets: Array<{
        ticketType: {
            id: any;
            originalId: boolean;
            name: string;
            price: number;
        };
        quantity: number;
        selectedDay?: 'Saturday' | 'Sunday';
        santaSlot?: {
            id: number;
            day: string;
            timeRange: string;
            numChildren?: number;
        };
    }>;
    total: number;
    orderId: string;
    hasDonation?: boolean;
    donationAmount?: number;
    charityMultiplier?: {
        multiplier: number;
        appliedAmount: number;
        originalAmount: number;
    };
};


// Styles
const body = {
    backgroundColor: '#121212',
    color: '#e0e0e0',
    fontFamily: 'Arial, sans-serif',
    margin: 0,
    padding: '20px',
};

const container = {
    maxWidth: '600px',
    margin: '0 auto',
    border: '1px solid #ab8e56',
    borderRadius: '5px',
    overflow: 'hidden',
};

const header = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '20px',
    textAlign: 'center' as const,
    color: '#ffffff',
    margin: '0',
};

const bookingId = {
    fontSize: '18px',
    fontWeight: 'bold',
    margin: '0 0 5px 0',
    color: '#ab8e56',
};

const customerName = {
    fontSize: '16px',
    margin: '0 0 20px 0',
    color: '#e0e0e0',
};

const divider = {
    height: '1px',
    backgroundColor: '#ab8e56',
    margin: '20px 0',
};

const sectionHeader = {
    fontSize: '20px',
    fontWeight: 'bold',
    margin: '0 0 15px 0',
    color: '#ab8e56',
};

const headerText = {
    fontWeight: 'bold',
    color: '#fff',
};

const infoText = {
    color: '#e0e0e0',
    margin: '5px 0',
};

const footer = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '15px',
    textAlign: 'center' as const,
    color: '#ffffff',
};

const footerText = {
    fontSize: '12px',
    color: '#ffffff',
};

const sectionStyle = {
    backgroundColor: '#1e1e1e',
    padding: '15px',
    borderRadius: '4px',
    marginBottom: '20px',
};

const h3Style = {
    fontSize: '20px',
    fontWeight: 'bold',
    color: '#ab8e56',
    marginBottom: '10px',
};

const textStyle = {
    color: '#e0e0e0',
    fontSize: '16px',
    lineHeight: '1.5',
};

const paragraph = {
    color: '#e0e0e0',
    fontSize: '16px',
    lineHeight: '1.5',
};

export const AdminOrderNotificationEmail = ({
    name,
    email,
    phoneNumber,
    tickets,
    total,
    orderId,
    hasDonation,
    donationAmount,
    charityMultiplier
}: AdminOrderNotificationEmailProps) => {
    const purchaseDate = new Date().toLocaleString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }).replace(/(\d+)\/(\d+)\/(\d+),/, '$1/$2/$3,');

    return (
        <Html>
            <Head />
            <Preview>New Ticket Order: {orderId}</Preview>
            <Body style={body}>
                <Container style={container}>
                    <Section style={header}>
                        <Heading style={headerText}>New Ticket Order</Heading>
                    </Section>

                    <Section style={{ padding: '20px' }}>
                        <Text style={bookingId}>Booking #{orderId}</Text>
                        <Text style={customerName}>Customer: {name}</Text>

                        <Section style={divider} />

                        <Section style={{ marginBottom: '20px', backgroundColor: '#1e1e1e', borderRadius: '4px', padding: '15px' }}>
                            <Row>
                                <Column style={{ width: '120px', fontWeight: 'bold', color: '#ab8e56' }}>Name:</Column>
                                <Column style={{ color: '#e0e0e0' }}>{name}</Column>
                            </Row>
                            <Row>
                                <Column style={{ width: '120px', fontWeight: 'bold', color: '#ab8e56' }}>Email:</Column>
                                <Column style={{ color: '#e0e0e0' }}>{email}</Column>
                            </Row>
                            <Row>
                                <Column style={{ width: '120px', fontWeight: 'bold', color: '#ab8e56' }}>Phone:</Column>
                                <Column style={{ color: '#e0e0e0' }}>{phoneNumber}</Column>
                            </Row>
                        </Section>

                        <Section style={divider} />

                        {/* Ticket details section */}
                        <Heading as="h3" style={sectionHeader}>Ticket Details</Heading>

                        {/* Header row */}
                        <Section style={{ backgroundColor: '#2a2a2a', padding: '10px', marginBottom: '5px' }}>
                            <Row>
                                <Column style={{ width: '40%' }}><Text style={{ fontWeight: 'bold', color: '#ab8e56' }}>Ticket</Text></Column>
                                <Column style={{ width: '20%' }}><Text style={{ fontWeight: 'bold', color: '#ab8e56' }}>Quantity</Text></Column>
                                <Column style={{ width: '20%' }}><Text style={{ fontWeight: 'bold', color: '#ab8e56' }}>Price</Text></Column>
                                <Column style={{ width: '20%' }}><Text style={{ fontWeight: 'bold', color: '#ab8e56' }}>Subtotal</Text></Column>
                            </Row>
                        </Section>

                        {/* Ticket rows */}
                        {tickets.map((item, index) => (
                            <Section key={index} style={{ backgroundColor: '#1e1e1e', padding: '10px', marginBottom: '5px' }}>
                                <Row>
                                    <Column style={{ width: '40%' }}>
                                        <Text style={{ color: '#e0e0e0', margin: '0' }}>
                                            {item.ticketType.name}
                                        </Text>
                                        {item.selectedDay && (
                                            <Text style={{ color: '#ab8e56', fontSize: '12px', margin: '2px 0' }}>
                                                {item.selectedDay === 'Saturday' ? 'Saturday, December 6th, 2025' : 'Sunday, December 7th, 2025'}
                                            </Text>
                                        )}
                                        {/* Handle originalId for day-specific tickets */}
                                        {!item.selectedDay && item.ticketType.originalId && item.ticketType.id.includes('-saturday') && (
                                            <Text style={{ color: '#ab8e56', fontSize: '12px', margin: '2px 0' }}>
                                                Saturday, December 6th, 2025
                                            </Text>
                                        )}
                                        {!item.selectedDay && item.ticketType.originalId && item.ticketType.id.includes('-sunday') && (
                                            <Text style={{ color: '#ab8e56', fontSize: '12px', margin: '2px 0' }}>
                                                Sunday, December 7th, 2025
                                            </Text>
                                        )}
                                        {item.santaSlot && (
                                            <Text style={{ color: '#ab8e56', fontSize: '12px', margin: '2px 0' }}>
                                                {item.santaSlot.day === 'Saturday' ? 'Saturday, December 6th, 2025' : 'Sunday, December 7th, 2025'}, {item.santaSlot.timeRange}
                                                {item.santaSlot.numChildren && item.santaSlot.numChildren > 1 &&
                                                    `, ${item.santaSlot.numChildren} children`}
                                            </Text>
                                        )}
                                    </Column>
                                    <Column style={{ width: '20%' }}>
                                        <Text style={{ color: '#e0e0e0' }}>{item.quantity}</Text>
                                    </Column>
                                    <Column style={{ width: '20%' }}>
                                        <Text style={{ color: '#e0e0e0' }}>€{item.ticketType.price.toFixed(2)}</Text>
                                    </Column>
                                    <Column style={{ width: '20%' }}>
                                        <Text style={{ color: '#e0e0e0' }}>€{(item.ticketType.price * item.quantity).toFixed(2)}</Text>
                                    </Column>
                                </Row>
                            </Section>
                        ))}

                        {/* Total row */}
                        <Section style={{ backgroundColor: '#2a2a2a', padding: '10px', marginTop: '10px' }}>
                            <Row>
                                <Column style={{ width: '80%' }}>
                                    <Text style={{ fontWeight: 'bold', color: '#ab8e56', textAlign: 'right' }}>Total:</Text>
                                </Column>
                                <Column style={{ width: '20%' }}>
                                    <Text style={{ fontWeight: 'bold', color: '#e0e0e0' }}>€{total.toFixed(2)}</Text>
                                </Column>
                            </Row>
                        </Section>

                        <Section style={divider} />

                        <Section style={{ marginBottom: '20px', backgroundColor: '#1e1e1e', borderRadius: '4px', padding: '15px' }}>
                            <Text style={infoText}>Order ID: {orderId}</Text>
                            <Text style={infoText}>Purchase Date: {purchaseDate}</Text>
                        </Section>

                        {hasDonation && (
                            <Section style={sectionStyle}>
                                <Heading as="h3" style={h3Style}>
                                    Charity Donation
                                </Heading>
                                <Text style={textStyle}>
                                    This order includes a €{donationAmount?.toFixed(2)} donation to LauraLynn Children&apos;s Hospice. {charityMultiplier && `The donation was multiplied by ${typeof charityMultiplier === 'number' ? charityMultiplier : charityMultiplier.multiplier}x.`}
                                </Text>
                            </Section>
                        )}

                        {charityMultiplier && (
                            <Text style={paragraph}>
                                Charity Donation Multiplier Applied: {typeof charityMultiplier === 'number' ? charityMultiplier : charityMultiplier.multiplier}x
                                {typeof charityMultiplier === 'object' && (
                                    ` (Original Amount: €${charityMultiplier.originalAmount.toFixed(2)} →
                                    Final Amount: €${charityMultiplier.appliedAmount.toFixed(2)})`
                                )}
                            </Text>
                        )}
                    </Section>

                    <Section style={footer}>
                        <Text style={footerText}>
                            You received this email because a new booking was made on your website.
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};