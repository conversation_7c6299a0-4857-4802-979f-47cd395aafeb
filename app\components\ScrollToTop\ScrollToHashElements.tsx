import { useEffect } from "react";
import { useLocation } from "@remix-run/react";

export function ScrollToHashElement() {
  const location = useLocation();

  useEffect(() => {
    if (location.hash) {
      const element = document.querySelector(location.hash) as any;

      if (element) {
        // Single smooth scroll to the element
        element.scrollIntoView({ behavior: "smooth" });

        // Remove the hash from the URL after scrolling
        /* if (window.history && window.history.replaceState) {
          window.history.replaceState({}, document.title, window.location.pathname);
        } */
      }
    }
  }, [location]);

  return null;
}
