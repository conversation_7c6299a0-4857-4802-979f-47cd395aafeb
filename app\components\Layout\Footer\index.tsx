"use client";

import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Link } from '@remix-run/react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    mapboxgl.accessToken = 'pk.eyJ1IjoiY2hpbGxlZGtvbmdzIiwiYSI6ImNtMHptbWtybDA3eGEyanNnejhmeG03NnUifQ.koQ7k5Km5ttOZP9gKN9uzA';

    if (mapContainer.current && !map.current) {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/dark-v11',
        center: [-6.2251355, 53.4368267],
        zoom: 14
      });

      // Create a popup that's always open
      const popup = new mapboxgl.Popup({
        closeOnClick: false,
        closeButton: false,
        offset: 25
      })
        .setLngLat([-6.2251355, 53.4368267])
        .setHTML('<h3 class="text-black font-bold">The National Show Centre</h3><p class="text-black">Stockhole Lane, Cloghran, Dublin</p>')
        .addTo(map.current);

      // Add marker
      const marker = new mapboxgl.Marker()
        .setLngLat([-6.2251355, 53.4368267])
        .setPopup(popup)
        .addTo(map.current);

      // Trigger popup to open
      marker.togglePopup();
    }
  }, []);

  return (
    <footer className="relative">
      <div className='flex flex-col items-center justify-center'>
        <div className="flex items-center justify-center gap-2 w-full px-4 py-2 border-t border-[var(--darkGold)]">
          <h3 className='text-center text-white text-xs'>
            @ {currentYear} - All Rights Reserved. Con Before Christmas. Brought to you by <a className="underline" href="https://www.hiddendublintours.com" rel="noopener nofollow noreferrer" target="_blank">Hidden Dublin Ltd</a>. Website built & powered by <a className="underline">Cosbomb</a>.
          </h3>

          <Link to="/privacy-policy" className="text-gray-300 hover:text-white transition-colors underline text-xs">
            Privacy Policy
          </Link>
        </div>
      </div>
    </footer>
  );
};
export default Footer;