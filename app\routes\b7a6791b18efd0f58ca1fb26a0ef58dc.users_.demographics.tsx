// Removed server loader; using client-side Redux data
import { useNavigate } from "@remix-run/react";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/store";
import { fetchUserBehavior, setUserBehaviorTimeRange } from "@/app/store/slices/adminSlice";
import { useState, useEffect } from "react";
import {
  MapPin,
  RefreshCw,
  ArrowLeft,
  Smartphone,
} from "lucide-react";
import { AdminLayout } from "@/app/components/admin/layout/AdminLayout";
import { ProtectedRoute } from "@/app/components/admin/ProtectedRoute";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { WorldMap } from "@/app/components/admin/charts/WorldMap";

export default function UserDemographics() {
  const dispatch = useDispatch<AppDispatch>();
  const { data: behaviorData, loading: behaviorLoading, error: behaviorError } = useSelector((state: RootState) => state.admin.userBehavior);
  const navigate = useNavigate();
  const [dateRange, setDateRange] = useState<'7days' | '30days' | '90days' | 'year'>('30days');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("geography");

  const navigateToOverview = () => navigate("/b7a6791b18efd0f58ca1fb26a0ef58dc/users");

  const handleRefresh = async () => {
    setIsLoading(true);
    await dispatch(fetchUserBehavior({ timeRange: dateRange }));
    setIsLoading(false);
  };

  const handleDateRangeChange = (range: '7days' | '30days' | '90days' | 'year') => {
    setDateRange(range);
    dispatch(setUserBehaviorTimeRange(range));
    dispatch(fetchUserBehavior({ timeRange: range }));
  };

  return (
    <ProtectedRoute requiredRole={["super_admin", "finance_admin", "support_admin"]}>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <div className="w-full flex items-center space-x-2 justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateToOverview}
                  className="text-amber-100 hover:bg-amber-900/30 h-8 w-8 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold text-white">User Demographics</h1>
              </div>
              <p className="mt-1 text-sm text-amber-100/70 ml-10">
                Detailed demographic information about your users
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
                className="border-amber-600/30 text-amber-100 bg-zinc-800 hover:bg-amber-900/30"
              >
                <RefreshCw
                  className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
                />
                Refresh
              </Button>
              <div className="relative">
                <select
                  value={dateRange}
                  onChange={(e) => handleDateRangeChange(e.target.value)}
                  className="appearance-none bg-zinc-800 border border-amber-600/30 rounded-md pl-3 pr-8 py-2 text-sm text-amber-100 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                >
                  <option value="7days">Last 7 days</option>
                  <option value="30days">Last 30 days</option>
                  <option value="90days">Last 90 days</option>
                  <option value="year">This year</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-100">
                  <svg
                    className="h-4 w-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Demographics Tabs */}
          <div className="border-b border-amber-600/30 mb-6">
            <nav className="-mb-px flex space-x-8">
              {["geography", "devices"].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`${activeTab === tab
                    ? "border-amber-500 text-amber-100"
                    : "border-transparent text-amber-100/70 hover:text-amber-100 hover:border-amber-300"
                    } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium transition-colors`}
                >
                  {tab === "geography" && "Geography"}
                  {tab === "devices" && "Devices"}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === "geography" && (
              <>
                <Card className="bg-zinc-800/50 border border-amber-600/30">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-white">
                      <MapPin className="inline mr-2 h-5 w-5 text-amber-400" />
                      Geographic Distribution
                    </CardTitle>
                    <p className="text-sm text-amber-100/70">User locations around the world</p>
                  </CardHeader>
                  <CardContent>
                    <WorldMap data={behaviorData?.locationAnalytics ?? []} height={400} />

                    <div className="mt-6 space-y-2">
                      <h3 className="text-sm font-medium text-amber-100">Top Countries</h3>
                      <div className="space-y-2">
                        {behaviorData?.locationAnalytics?.map((country: any) => (
                          <div key={country.id} className="flex items-center">
                            <div className="w-24 text-sm text-amber-100/70">
                              {country.label}
                            </div>
                            <div className="flex-1 mx-2">
                              <div className="h-2 bg-amber-900/50 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-amber-500/80 rounded-full"
                                  style={{ width: `${country.percent ?? 0}%` }}
                                />
                              </div>
                            </div>
                            <div className="w-16 text-right text-sm font-medium text-amber-100">
                              {country.percent}%
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
            {activeTab === "devices" && (
              <Card className="bg-zinc-800/50 border border-amber-600/30">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-white">
                    <Smartphone className="inline mr-2 h-5 w-5 text-amber-400" />
                    Device Usage
                  </CardTitle>
                  <p className="text-sm text-amber-100/70">User device preferences</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {behaviorData?.deviceTypes?.map((device: any) => (
                      <div key={device.id} className="flex items-center">
                        <div className="w-24 text-sm text-amber-100/70">
                          {device.label}
                        </div>
                        <div className="flex-1 mx-2">
                          <div className="h-2 bg-amber-900/50 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-amber-500/80 rounded-full"
                              style={{ width: `${device.value}%` }}
                            />
                          </div>
                        </div>
                        <div className="w-16 text-right text-sm font-medium text-amber-100">
                          {device.value}%
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </AdminLayout >
    </ProtectedRoute >
  );
};