import * as React from 'react';
import {
    Body,
    Container,
    Head,
    Heading,
    Html,
    Img,
    Preview,
    Section,
    Text,
    Row,
    Column,
    Link,
} from '@react-email/components';

interface VendorStandConfirmationEmailProps {
    businessName: string;
    contactName: string;
    email: string;
    phone: string;
    standNumber: number;
    totalPrice: number;
    addons?: Array<{
        id: string;
        name: string;
        quantity: number;
        price: number;
    }>;
    isArtist?: boolean;
    price?: number;
    discountApplied?: boolean;
    discountPercentage?: number;
    originalPrice?: number;
}

const baseUrl = process.env.SITE_URL || 'https://theconventionbeforechristmas.ie';

// Styles
const main = {
    backgroundColor: '#121212',
    color: '#fff',
    fontFamily: 'Arial, sans-serif',
    margin: '0',
    padding: '20px',
};

const container = {
    maxWidth: '600px',
    margin: '0 auto',
    border: '1px solid #ab8e56',
    borderRadius: '5px',
    overflow: 'hidden',
};

const header = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '20px',
    textAlign: 'center' as const,
};

const headerText = {
    color: '#ffffff',
    margin: '0',
};

const bookingDetails = {
    padding: '20px',
    backgroundColor: '#1e1e1e',
};

const bookingId = {
    fontSize: '18px',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: '#ab8e56',
};

const bookingNote = {
    marginBottom: '20px',
    color: '#fff',
};

const eventBox = {
    display: 'flex',
    marginBottom: '20px',
    border: '1px solid #ab8e56',
    padding: '10px',
    backgroundColor: '#2a2a2a',
};

const divider = {
    height: '1px',
    backgroundColor: '#ab8e56',
    margin: '20px 0',
};

const footer = {
    background: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '15px',
    textAlign: 'center' as const,
    fontSize: '12px',
    color: '#ffffff',
};

export const VendorStandConfirmationEmail = ({
    businessName,
    contactName,
    email,
    phone,
    standNumber,
    totalPrice,
    addons = [],
    isArtist = false,
    price = 0,
    discountApplied = false,
    discountPercentage = 0,
    originalPrice = 0
}: VendorStandConfirmationEmailProps) => {
    const purchaseDate = new Date().toLocaleString();
    const eventDates = "December 6th & 7th, 2024";
    const eventLocation = "The National Show Center, Swords, Co. Dublin, Ireland";

    // Function to determine table info based on price
    const getTableInfo = () => {
      if (price === 1350 || price === 1100) {
        return "2 tables and 1 chair";
      } else {
        return "1 6ft table and 1 chair";
      }
    };

    return (
        <Html>
            <Head />
            <Preview>{isArtist ? 'Artist' : 'Stand'} Booking Confirmation - The Convention Before Christmas</Preview>
            <Body style={main}>
                <Container style={container}>
                    <Section style={header}>
                        <Heading as="h2" style={headerText}>
                            {isArtist ? 'Artist Space' : 'Vendor Stand'} Booking Confirmation
                        </Heading>
                    </Section>

                    <Section style={bookingDetails}>
                        <Text style={bookingId}>
                            {isArtist ? 'Artist Space' : 'Stand'} #{standNumber}
                        </Text>
                        <Text style={bookingNote}>Thank you for your booking, {contactName}!</Text>

                        <Row style={eventBox}>
                            <Column style={{ width: '100px', marginRight: '15px' }}>
                                <Img
                                    src={`${baseUrl}/images/logo-banner.png`}
                                    alt="Event"
                                    width="100"
                                    height="100"
                                    style={{ objectFit: 'cover' }}
                                />
                            </Column>
                            <Column>
                                <Heading as="h3" style={{ marginTop: '0', marginBottom: '5px', paddingLeft: '5px', color: '#ab8e56' }}>
                                    The Convention Before Christmas
                                </Heading>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '5px' }}>Business: {businessName}</Text>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '5px' }}>Stand Number: {standNumber}</Text>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '5px' }}>Includes: {getTableInfo()}</Text>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '5px' }}>{eventDates}</Text>
                                <Text style={{ margin: '5px 0', color: '#e0e0e0', paddingLeft: '5px' }}>{eventLocation}</Text>
                            </Column>
                        </Row>

                        <Section style={divider} />

                        <Heading as="h3">Booking Information</Heading>
                        <Text>Business Name: {businessName}</Text>
                        <Text>Contact Name: {contactName}</Text>
                        <Text>Email: {email}</Text>
                        <Text>Phone: {phone}</Text>
                        <Text>Purchase Date: {purchaseDate}</Text>

                        <Section style={divider} />

                        <Heading as="h3">Payment Details</Heading>
                        {discountApplied && originalPrice > 0 && (
                            <>
                                <Text>Original Price: <span style={{ textDecoration: 'line-through' }}>€{originalPrice.toFixed(2)}</span></Text>
                                <Text>Early Bird Discount: {discountPercentage}% off</Text>
                                <Text>Discounted Price: €{(originalPrice * (1 - discountPercentage/100)).toFixed(2)}</Text>
                            </>
                        )}
                        {!discountApplied && (
                            <Text>Stand Fee: €{price.toFixed(2)}</Text>
                        )}

                        {addons && addons.length > 0 && (
                            <>
                                <Heading as="h4" style={{ color: '#ab8e56', marginBottom: '10px' }}>Add-ons:</Heading>
                                {addons.map((addon, index) => (
                                    <Text key={index}>
                                        {addon.quantity} x {addon.name}: €{(addon.price * addon.quantity).toFixed(2)}
                                    </Text>
                                ))}
                            </>
                        )}

                        <Text style={{ fontWeight: 'bold', marginTop: '20px' }}>
                            Total: €{totalPrice.toFixed(2)} (incl. 23% VAT)
                        </Text>

                        <Section style={divider} />

                        {/* <Heading as="h3">Important Information</Heading>
                        <Text>Setup Time: December 6th, 2024 (2pm - 8pm)</Text>
                        <Text>Event Hours: 10am - 6pm (both days)</Text>
                        <Text>Vendor Entry: 8am (both days)</Text>
                        <Text>Breakdown: December 8th, 2024 (6pm - 10pm)</Text>

                        <Text style={{ marginTop: '15px', fontStyle: 'italic' }}>
                            A detailed vendor information pack will be sent to you closer to the event date.
                        </Text> */}
                    </Section>

                    <Section style={footer}>
                        <Text style={{ marginBottom: '10px', color: '#fff' }}>
                            We look forward to seeing you at The Convention Before Christmas!
                        </Text>
                        <Text style={{ fontSize: '10px', marginTop: '15px' }}>
                            © {new Date().getFullYear()} The Convention Before Christmas. All rights reserved.
                        </Text>
                        <Text style={{ fontSize: '10px', marginTop: '5px' }}>
                            <Link href={`${baseUrl}/unsubscribe?email=${email}`} style={{ color: '#fff', textDecoration: 'underline' }}>
                                Unsubscribe from our emails
                            </Link>
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};
export default VendorStandConfirmationEmail;
