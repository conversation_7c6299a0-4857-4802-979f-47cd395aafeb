import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchTransactions } from '@/app/store/slices/adminSlice';
import { useSupabase } from '@/app/context/AdminAuthContext';
import {
  selectConnectionStatus,
  startRealtimeMonitoring,
  stopRealtimeMonitoring
} from '@/app/store/slices/realtimeSlice';
import { useTransactionRefresh } from '@/app/hooks/useRealtimeRefresh';
import { format } from 'date-fns';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { exportToCSV, exportToPDF, exportToExcel, formatDataForExport, generateFilename } from '@/app/utils/exportUtils';
import { showExportStartToast, showExportSuccessToast, showExportErrorToast, updateToastToSuccess, updateToastToError } from '@/app/utils/toast';
import { RealtimeToastContainer } from '@/app/components/admin/RealtimeToast';
import { RealtimeConnectionStatus } from '@/app/components/admin/RealtimeConnectionStatus';
import { RealtimeErrorBoundary } from '@/app/components/admin/RealtimeErrorBoundary';
import {
  RefreshCw,
  Download,
  Calendar,
  Search,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  X,
  Loader2,
  ArrowLeft,
  CreditCard,
  User,
  Mail,
  Ticket,
  Calendar as CalendarIcon,
  Check,
  X as XIcon,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';

interface TransactionItem {
  id: number | string;
  ticket_id?: string;
  quantity: number;
  price: number;
  order_id?: string;
  original_ticket_id?: string;
  selected_day?: string;
  ticket_name?: string;
  ticket_description?: string;
  // For vendor stands
  stand_number?: number;
  business_name?: string;
  addons?: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  // For donations
  donation_type?: string;
  campaign?: string;
  multiplier?: number;
  original_amount?: number;
}

interface Transaction {
  id: string;
  payment_intent_id: string;
  name: string;
  email: string;
  total: number;
  status: 'success' | 'failed' | 'pending' | 'refunded' | 'cancelled';
  created_at: string;
  items: TransactionItem[];
  transaction_type: 'ticket' | 'vendor_stand' | 'donation';
  // Additional fields for vendor stands
  stand_number?: number;
  business_name?: string;
  contact_name?: string;
  phone?: string;
  discount_applied?: boolean;
  discount_percentage?: number | null;
  original_price?: number | null;
  // Additional fields for donations
  donation_campaign?: string;
  multiplier?: number;
  original_amount?: number;
  is_recurring?: boolean;
}

const TransactionsPage = () => {
  const dispatch = useAppDispatch();
  const supabase = useSupabase();

  const {
    data: transactions = [],
    loading,
    error,
    page: currentPage = 1,
    totalPages = 1,
    count: totalCount = 0
  } = useAppSelector(state => state.admin.transactions);

  // Real-time state
  const connectionStatus = useAppSelector(selectConnectionStatus);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'failed' | 'pending'>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | 'ticket' | 'vendor_stand' | 'donation'>('all');
  const [dateRange, setDateRange] = useState('30days');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [showDateDropdown, setShowDateDropdown] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  // Load initial data and reset to page 1 when filters change
  useEffect(() => {
    setLastUpdated(new Date());
    loadTransactions(1);
  }, [statusFilter, typeFilter, dateRange, debouncedSearchTerm]);

  // Reset to page 1 if current page becomes invalid after filtering
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      loadTransactions(1);
    }
  }, [currentPage, totalPages]);

  // Initialize real-time monitoring
  useEffect(() => {
    if (!supabase) {
      console.warn('[TransactionsPage] Supabase client not available for real-time monitoring');
      return;
    }

    // Start real-time monitoring
    const startMonitoring = async () => {
      try {
        await dispatch(startRealtimeMonitoring());
        console.log('[TransactionsPage] Real-time monitoring started');
      } catch (error) {
        console.error('[TransactionsPage] Failed to start real-time monitoring:', error);
      }
    };

    startMonitoring();

    // Cleanup on unmount
    return () => {
      const stopMonitoring = async () => {
        try {
          await dispatch(stopRealtimeMonitoring());
          console.log('[TransactionsPage] Real-time monitoring stopped');
        } catch (error) {
          console.error('[TransactionsPage] Failed to stop real-time monitoring:', error);
        }
      };

      stopMonitoring();
    };
  }, [supabase, dispatch]);

  // Set up real-time refresh for transaction data
  useTransactionRefresh(() => {
    setLastUpdated(new Date());
    // Optionally reload current page of transactions
    loadTransactions(currentPage);
  });

  const loadTransactions = (page: number) => {
    dispatch(fetchTransactions({
      page,
      pageSize: 10,
      status: statusFilter === 'all' ? undefined : statusFilter,
      transactionType: typeFilter === 'all' ? undefined : typeFilter,
      searchQuery: debouncedSearchTerm
    }));
    setLastUpdated(new Date());
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      loadTransactions(newPage);
    }
  };

  const handleRefresh = () => {
    loadTransactions(1);
  };

  const handleDateRangeChange = (range: string) => {
    setDateRange(range);
    setShowDateDropdown(false);
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    const toastId = showExportStartToast(format);

    try {
      setShowExportDropdown(false);

      // Get all transactions for export (not just current page)
      const allTransactions: any[] = [];

      // Fetch orders
      const { data: orders, error: ordersError } = await supabase
        .from('Orders')
        .select('*')
        .order('created_at', { ascending: false });

      if (!ordersError && orders) {
        orders.forEach(order => {
          allTransactions.push({
            id: order.id,
            type: 'Order',
            amount: order.total_amount,
            status: order.status,
            customer_email: order.customer_email,
            created_at: order.created_at,
            description: `Order #${order.id}`
          });
        });
      }

      // Fetch vendor stands
      const { data: vendors, error: vendorsError } = await supabase
        .from('VendorStands')
        .select('*')
        .order('created_at', { ascending: false });

      if (!vendorsError && vendors) {
        vendors.forEach(vendor => {
          allTransactions.push({
            id: vendor.id,
            type: 'Vendor Stand',
            amount: vendor.price,
            status: 'Completed',
            customer_email: vendor.email,
            created_at: vendor.created_at,
            description: `Stand #${vendor.stand_number} - ${vendor.business_name}`
          });
        });
      }

      // Fetch charity donations
      const { data: donations, error: donationsError } = await supabase
        .from('CharityDonations')
        .select('*')
        .order('created_at', { ascending: false });

      if (!donationsError && donations) {
        donations.forEach(donation => {
          allTransactions.push({
            id: donation.id,
            type: 'Charity Donation',
            amount: donation.amount,
            status: 'Completed',
            customer_email: donation.email || 'Anonymous',
            created_at: donation.created_at,
            description: `Donation to ${donation.charity_name}`
          });
        });
      }

      // Sort by date
      allTransactions.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      if (allTransactions.length === 0) {
        updateToastToError(toastId, 'No transactions found to export');
        return;
      }

      // Format data for export
      const formattedData = formatDataForExport(allTransactions, 'transactions');
      const filename = generateFilename('transactions', dateRange);

      // Export based on format
      switch (format) {
        case 'csv':
          await exportToCSV(formattedData, filename);
          break;
        case 'pdf':
          await exportToPDF(formattedData, filename, 'Transactions Report');
          break;
        case 'excel':
          await exportToExcel(formattedData, filename);
          break;
      }

      updateToastToSuccess(toastId, `Successfully exported ${allTransactions.length} transactions as ${format.toUpperCase()}`);

    } catch (error) {
      console.error('Export failed:', error);
      updateToastToError(toastId, `Export failed: ${(error as any)?.message || 'Please try again'}`);
    }
  };

  const toggleRow = (id: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
      setSelectedTransaction(null);
    } else {
      newExpanded.add(id);
      const transaction = transactions.find((t) => t.id === id);
      if (transaction) setSelectedTransaction(transaction);
    }
    setExpandedRows(newExpanded);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return format(date, 'PPpp');
    } catch (e) {
      return 'Invalid date';
    }
  };

  // Helper function to check if transaction was recently updated (within last 30 seconds)
  const isRecentlyUpdated = (transaction: any) => {
    if (!transaction.updated_at) return false;
    const updatedTime = new Date(transaction.updated_at);
    const now = new Date();
    const timeDiff = now.getTime() - updatedTime.getTime();
    return timeDiff < 30000; // 30 seconds
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IE', { // Changed to en-IE for Euro currency formatting
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
      case 'paid':
      case 'completed':
      case 'confirmed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
            <CheckCircle className="w-3 h-3 mr-1" />
            {status === 'paid' ? 'Paid' : status === 'completed' ? 'Completed' : status === 'confirmed' ? 'Confirmed' : 'Success'}
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900 text-red-300">
            <XCircle className="w-3 h-3 mr-1" />
            Failed
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900 text-yellow-300">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-900 text-gray-300">
            <XCircle className="w-3 h-3 mr-1" />
            Cancelled
          </span>
        );
      case 'refunded':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-900 text-purple-300">
            <CheckCircle className="w-3 h-3 mr-1" />
            Refunded
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
            Unknown
          </span>
        );
    }
  };

  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case 'ticket':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-300">
            <Ticket className="w-3 h-3 mr-1" />
            Ticket Sale
          </span>
        );
      case 'vendor_stand':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-900 text-purple-300">
            <CreditCard className="w-3 h-3 mr-1" />
            Vendor Stand
          </span>
        );
      case 'donation':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-300">
            <User className="w-3 h-3 mr-1" />
            Donation
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
            Unknown
          </span>
        );
    }
  };

  const formatTransactionItems = (transaction: Transaction) => {
    if (transaction.transaction_type === 'vendor_stand') {
      const item = transaction.items[0];
      return `${item.business_name || 'Vendor Stand'} - Stand #${item.stand_number || 'N/A'}`;
    } else if (transaction.transaction_type === 'donation') {
      const item = transaction.items[0];
      const campaign = item.campaign || transaction.donation_campaign || 'General Donation';
      const multiplier = item.multiplier || transaction.multiplier;
      return `${campaign}${multiplier ? ` (${multiplier}x multiplier)` : ''}`;
    } else {
      // Ticket sales
      return `${transaction.items.length} item${transaction.items.length !== 1 ? 's' : ''}`;
    }
  };

  return (
    <ProtectedRoute requiredRole={['super_admin', 'finance_admin', 'support_admin']}>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header with controls */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-amber-100">Transactions</h1>
              <p className="text-sm text-amber-100/70">View and manage all transactions</p>
            </div>

            <div className="flex flex-col items-center justify-center gap-2">
              {/* Controls 1st Layer */}
              <div className="w-full flex flex-wrap items-center gap-2">
                {/* Search */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-amber-100/70" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-amber-600/30 rounded-md bg-zinc-800/50 text-amber-100 placeholder-amber-100/50 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 sm:text-sm"
                    placeholder="Search transactions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Status filter */}
                <select
                  className="block pl-3 pr-10 py-2 text-base border border-amber-600/30 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md bg-zinc-800/50 text-amber-100"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as any)}
                >
                  <option value="all">All Statuses</option>
                  <option value="success">Successful</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                  <option value="cancelled">Cancelled</option>
                </select>

                {/* Transaction type filter */}
                <select
                  className="block pl-3 pr-10 py-2 text-base border border-amber-600/30 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 sm:text-sm rounded-md bg-zinc-800/50 text-amber-100"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as any)}
                >
                  <option value="all">All Types</option>
                  <option value="ticket">Ticket Sales</option>
                  <option value="vendor_stand">Vendor Stands</option>
                  <option value="donation">Donations</option>
                </select>
              </div>

              {/* Controls 2nd Layer */}
              <div className="w-full flex flex-wrap items-center gap-2">
                {/* Date range dropdown */}
                <div className="relative">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-amber-600/30 rounded-md shadow-sm text-sm font-medium text-amber-100 bg-zinc-800/50 hover:bg-amber-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                    onClick={() => setShowDateDropdown(!showDateDropdown)}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    {dateRange === '7days' ? 'Last 7 days' :
                      dateRange === '30days' ? 'Last 30 days' :
                        dateRange === '90days' ? 'Last 90 days' : 'Date range'}
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </button>

                  {showDateDropdown && (
                    <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-zinc-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <div className="py-1">
                        <button
                          onClick={() => handleDateRangeChange('7days')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Last 7 days
                        </button>
                        <button
                          onClick={() => handleDateRangeChange('30days')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Last 30 days
                        </button>
                        <button
                          onClick={() => handleDateRangeChange('90days')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Last 90 days
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Export dropdown */}
                <div className="relative">
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 border border-amber-600/30 rounded-md shadow-sm text-sm font-medium text-amber-100 bg-zinc-800/50 hover:bg-amber-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                    onClick={() => setShowExportDropdown(!showExportDropdown)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </button>

                  {showExportDropdown && (
                    <div className="origin-top-right absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-zinc-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                      <div className="py-1">
                        <button
                          onClick={() => handleExport('csv')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Export as CSV
                        </button>
                        <button
                          onClick={() => handleExport('pdf')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Export as PDF
                        </button>
                        <button
                          onClick={() => handleExport('excel')}
                          className="block w-full text-left px-4 py-2 text-sm text-amber-100 hover:bg-amber-900/30"
                        >
                          Export as Excel
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* Refresh button and status indicators */}
                <div className="flex items-center space-x-4">
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-2 border border-amber-600/30 rounded-md shadow-sm text-sm font-medium text-amber-100 bg-zinc-800/50 hover:bg-amber-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                    onClick={handleRefresh}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                  </button>

                  {/* Real-time connection status */}
                  <RealtimeConnectionStatus size="sm" variant="badge" />

                  <span className="text-xs text-amber-100/50">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Transactions table */}
          <div className="flex flex-col">
            <div className="overflow-hidden shadow border border-amber-600/30 rounded-lg">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-amber-600/20">
                  <thead className="bg-amber-900/20">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Customer
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Items
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Total
                      </th>
                      <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-zinc-800/50 divide-y divide-amber-600/10">
                    {loading ? (
                      <tr>
                        <td colSpan={8} className="px-6 py-4 whitespace-nowrap text-sm text-amber-100 text-center">
                          <div className="flex justify-center items-center space-x-2">
                            <Loader2 className="h-5 w-5 animate-spin text-amber-500" />
                            <span>Loading transactions...</span>
                          </div>
                        </td>
                      </tr>
                    ) : error ? (
                      <tr>
                        <td colSpan={8} className="px-6 py-4 whitespace-nowrap text-sm text-red-500 text-center">
                          Error loading transactions: {error}
                        </td>
                      </tr>
                    ) : transactions.length === 0 ? (
                      <tr>
                        <td colSpan={8} className="px-6 py-4 whitespace-nowrap text-sm text-amber-100/70 text-center">
                          No transactions found
                        </td>
                      </tr>
                    ) : (
                      transactions.map((transaction) => (
                        <>
                          <tr
                            key={transaction.id}
                            className={`${
                              expandedRows.has(transaction.id)
                                ? 'bg-amber-900/10'
                                : isRecentlyUpdated(transaction)
                                  ? 'bg-green-900/20 hover:bg-green-900/30 animate-pulse'
                                  : 'hover:bg-amber-900/5'
                            } cursor-pointer transition-colors duration-300`}
                            onClick={() => toggleRow(transaction.id)}
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-amber-100">
                              {transaction.id.substring(0, 8)}...
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-amber-100/70">
                              {formatDate(transaction.created_at)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm text-amber-100">{transaction.name}</div>
                              <div className="text-sm text-amber-100/70">{transaction.email}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              {getTransactionTypeBadge(transaction.transaction_type)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-amber-100/70">
                              {formatTransactionItems(transaction)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-amber-100">
                              {formatCurrency(transaction.total)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                              {getStatusBadge(transaction.status)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleRow(transaction.id);
                                }}
                                className="text-amber-400 hover:text-amber-300"
                              >
                                <Eye className="h-5 w-5" />
                              </button>
                            </td>
                          </tr>
                          {expandedRows.has(transaction.id) && selectedTransaction && (
                            <tr className="bg-amber-900/5">
                              <td colSpan={8} className="px-6 py-4">
                                <div className="bg-zinc-800/50 border border-amber-600/20 rounded-lg p-4">
                                  <div className="flex justify-between items-start mb-4">
                                    <div>
                                      <h3 className="text-lg font-medium text-amber-100">
                                        {transaction.transaction_type === 'vendor_stand' ? 'Vendor Stand Booking' :
                                          transaction.transaction_type === 'donation' ? 'Charity Donation' :
                                            'Order'} #{selectedTransaction.id}
                                      </h3>
                                      <p className="text-sm text-amber-100/70">
                                        {formatDate(selectedTransaction.created_at)}
                                      </p>
                                      <div className="mt-2">
                                        {getTransactionTypeBadge(selectedTransaction.transaction_type)}
                                      </div>
                                    </div>
                                    <div className="text-right">
                                      <p className="text-sm text-amber-100/70">Status</p>
                                      {getStatusBadge(selectedTransaction.status)}
                                    </div>
                                  </div>

                                  {/* Transaction-specific details */}
                                  {selectedTransaction.transaction_type === 'vendor_stand' && (
                                    <div className="mb-6 p-4 bg-zinc-800/30 rounded-lg border border-amber-600/20">
                                      <h4 className="text-sm font-medium text-amber-100/70 mb-3">
                                        Vendor Stand Details
                                      </h4>
                                      <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                          <span className="text-amber-100/70">Business:</span>
                                          <span className="text-amber-100 ml-2">{selectedTransaction.business_name}</span>
                                        </div>
                                        <div>
                                          <span className="text-amber-100/70">Stand Number:</span>
                                          <span className="text-amber-100 ml-2">#{selectedTransaction.stand_number}</span>
                                        </div>
                                        <div>
                                          <span className="text-amber-100/70">Contact:</span>
                                          <span className="text-amber-100 ml-2">{selectedTransaction.contact_name}</span>
                                        </div>
                                        <div>
                                          <span className="text-amber-100/70">Phone:</span>
                                          <span className="text-amber-100 ml-2">{selectedTransaction.phone}</span>
                                        </div>
                                        {selectedTransaction.discount_applied && (
                                          <div className="col-span-2">
                                            <span className="text-amber-100/70">Discount Applied:</span>
                                            <span className="text-green-400 ml-2">{selectedTransaction.discount_percentage}%</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  {selectedTransaction.transaction_type === 'donation' && (
                                    <div className="mb-6 p-4 bg-zinc-800/30 rounded-lg border border-amber-600/20">
                                      <h4 className="text-sm font-medium text-amber-100/70 mb-3">
                                        Donation Details
                                      </h4>
                                      <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                          <span className="text-amber-100/70">Campaign:</span>
                                          <span className="text-amber-100 ml-2">{selectedTransaction.donation_campaign}</span>
                                        </div>
                                        {selectedTransaction.multiplier && (
                                          <div>
                                            <span className="text-amber-100/70">Multiplier:</span>
                                            <span className="text-green-400 ml-2">{selectedTransaction.multiplier}x</span>
                                          </div>
                                        )}
                                        {selectedTransaction.original_amount && (
                                          <div>
                                            <span className="text-amber-100/70">Original Amount:</span>
                                            <span className="text-amber-100 ml-2">{formatCurrency(selectedTransaction.original_amount)}</span>
                                          </div>
                                        )}
                                        {selectedTransaction.is_recurring && (
                                          <div>
                                            <span className="text-amber-100/70">Type:</span>
                                            <span className="text-blue-400 ml-2">Recurring</span>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  <div className="mt-6">
                                    <h4 className="text-sm font-medium text-amber-100/70 mb-3">
                                      {selectedTransaction.transaction_type === 'vendor_stand' ? 'Stand Details' :
                                        selectedTransaction.transaction_type === 'donation' ? 'Donation Details' :
                                          'Order Items'}
                                    </h4>
                                    <div className="overflow-x-auto">
                                      <table className="min-w-full divide-y divide-amber-600/20">
                                        <thead>
                                          <tr>
                                            <th className="px-4 py-2 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                              {selectedTransaction.transaction_type === 'vendor_stand' ? 'Stand' :
                                                selectedTransaction.transaction_type === 'donation' ? 'Campaign' :
                                                  'Item'}
                                            </th>
                                            <th className="px-4 py-2 text-right text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                              Price
                                            </th>
                                            <th className="px-4 py-2 text-right text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                              Qty
                                            </th>
                                            <th className="px-4 py-2 text-right text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                              Total
                                            </th>
                                          </tr>
                                        </thead>
                                        <tbody className="divide-y divide-amber-600/10">
                                          {selectedTransaction.items.map((item, index) => (
                                            <tr key={index}>
                                              <td className="px-4 py-3">
                                                <div className="text-sm font-medium text-amber-100">
                                                  {selectedTransaction.transaction_type === 'vendor_stand' ? (
                                                    <>
                                                      Stand #{item.stand_number}
                                                      {item.business_name && (
                                                        <div className="text-xs text-amber-100/60 mt-1">
                                                          {item.business_name}
                                                        </div>
                                                      )}
                                                      {item.addons && item.addons.length > 0 && (
                                                        <div className="text-xs text-amber-100/50 mt-1">
                                                          Add-ons: {item.addons.map(addon => `${addon.name} (${addon.quantity})`).join(', ')}
                                                        </div>
                                                      )}
                                                    </>
                                                  ) : selectedTransaction.transaction_type === 'donation' ? (
                                                    <>
                                                      {item.campaign || 'General Donation'}
                                                      {item.multiplier && (
                                                        <div className="text-xs text-green-400 mt-1">
                                                          {item.multiplier}x multiplier applied
                                                        </div>
                                                      )}
                                                    </>
                                                  ) : (
                                                    <>
                                                      {item.ticket_name}
                                                      {item.ticket_description && (
                                                        <div className="text-xs text-amber-100/60 mt-1">
                                                          {item.ticket_description}
                                                        </div>
                                                      )}
                                                      {item.selected_day && (
                                                        <div className="text-xs text-amber-100/50 mt-1">
                                                          Day: {item.selected_day}
                                                        </div>
                                                      )}
                                                    </>
                                                  )}
                                                </div>
                                              </td>
                                              <td className="px-4 py-3 text-right text-sm text-amber-100/80">
                                                {formatCurrency(item.price)}
                                              </td>
                                              <td className="px-4 py-3 text-right text-sm text-amber-100/80">
                                                {item.quantity}
                                              </td>
                                              <td className="px-4 py-3 text-right text-sm font-medium text-amber-100">
                                                {formatCurrency(item.price * item.quantity)}
                                              </td>
                                            </tr>
                                          ))}
                                        </tbody>
                                        <tfoot>
                                          {selectedTransaction.transaction_type === 'vendor_stand' && selectedTransaction.discount_applied && (
                                            <>
                                              <tr className="border-t border-amber-600/20">
                                                <th colSpan={3} className="px-4 py-3 text-right text-sm font-medium text-amber-100/70">
                                                  Original Price
                                                </th>
                                                <td className="px-4 py-3 text-right text-sm font-medium text-amber-100">
                                                  {formatCurrency(selectedTransaction.original_price ?? selectedTransaction.total)}
                                                </td>
                                              </tr>
                                              <tr>
                                                <th colSpan={3} className="px-4 py-3 text-right text-sm font-medium text-amber-100/70">
                                                  Discount ({selectedTransaction.discount_percentage}%)
                                                </th>
                                                <td className="px-4 py-3 text-right text-sm font-medium text-green-400">
                                                  -{formatCurrency((selectedTransaction.original_price ?? selectedTransaction.total) - selectedTransaction.total)}
                                                </td>
                                              </tr>
                                            </>
                                          )}
                                          <tr className="border-t border-amber-600/20">
                                            <th colSpan={3} className="px-4 py-3 text-right text-sm font-bold text-amber-100">
                                              Total
                                            </th>
                                            <td className="px-4 py-3 text-right text-sm font-bold text-amber-300">
                                              {formatCurrency(selectedTransaction.total)}
                                            </td>
                                          </tr>
                                        </tfoot>
                                      </table>
                                    </div>
                                  </div>

                                  <div className="mt-6 pt-6 border-t border-amber-600/20 flex justify-end space-x-3">
                                    <button
                                      type="button"
                                      className="px-4 py-2 border border-amber-600/30 text-sm font-medium rounded-md text-amber-100 bg-zinc-800/50 hover:bg-amber-900/30 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                                      onClick={() => setSelectedTransaction(null)}
                                    >
                                      Close
                                    </button>
                                  </div>
                                </div>
                              </td>
                            </tr>
                          )}
                        </>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination - only show if there are results and more than one page */}
              {!loading && transactions.length > 0 && totalPages > 1 && (
                <div className="bg-zinc-800/50 px-4 py-3 flex items-center justify-between border-t border-amber-600/30 sm:px-6 rounded-b-lg">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-4 py-2 border border-amber-600/30 text-sm font-medium rounded-md ${currentPage === 1
                        ? 'bg-zinc-800 text-amber-100/50 cursor-not-allowed'
                        : 'bg-zinc-800/50 text-amber-100 hover:bg-amber-900/30'
                        }`}
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`ml-3 relative inline-flex items-center px-4 py-2 border border-amber-600/30 text-sm font-medium rounded-md ${currentPage === totalPages
                        ? 'bg-zinc-800 text-amber-100/50 cursor-not-allowed'
                        : 'bg-zinc-800/50 text-amber-100 hover:bg-amber-900/30'
                        }`}
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-amber-100/70">
                        Showing <span className="font-medium">{(currentPage - 1) * 10 + 1}</span> to{' '}
                        <span className="font-medium">
                          {Math.min(currentPage * 10, totalCount || 0)}
                        </span>{' '}
                        of <span className="font-medium">{totalCount || 0}</span> results
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-amber-600/30 text-sm font-medium ${currentPage === 1
                            ? 'bg-zinc-800 text-amber-100/50 cursor-not-allowed'
                            : 'bg-zinc-800/50 text-amber-100 hover:bg-amber-900/30'
                            }`}
                        >
                          <span className="sr-only">Previous</span>
                          <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                        </button>
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === pageNum
                                ? 'z-10 bg-amber-600/30 border-amber-500/50 text-amber-100'
                                : 'bg-zinc-800/50 border-amber-600/30 text-amber-100 hover:bg-amber-900/30'
                                }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                        <button
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-amber-600/30 text-sm font-medium ${currentPage === totalPages
                            ? 'bg-zinc-800 text-amber-100/50 cursor-not-allowed'
                            : 'bg-zinc-800/50 text-amber-100 hover:bg-amber-900/30'
                            }`}
                        >
                          <span className="sr-only">Next</span>
                          <ChevronRight className="h-5 w-5" aria-hidden="true" />
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </AdminLayout>

      {/* Real-time toast notifications */}
      <RealtimeErrorBoundary>
        <RealtimeToastContainer />
      </RealtimeErrorBoundary>
    </ProtectedRoute>
  );
};
export default TransactionsPage;