import { createClient } from '@supabase/supabase-js';
import { json, type ActionFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function action({ request }: ActionFunctionArgs) {
  try {
    const { email } = await request.json();

    if (!email) {
      return json(
        { message: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const { data: existingSubscriber } = await supabase
      .from('NewsletterSubscribers')
      .select('*')
      .eq('email', email.toLowerCase())
      .single();

    if (existingSubscriber) {
      return json(
        { message: 'You are already subscribed to our newsletter' },
        { status: 400 }
      );
    }

    // Add new subscriber
    const { error } = await supabase
      .from('NewsletterSubscribers')
      .insert([
        {
          email: email.toLowerCase(),
          subscribed_at: new Date().toISOString(),
        },
      ]);

    if (error) {
      throw error;
    }

    return json(
      { message: 'You have successfully subscribed to our newsletter' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return json(
      { message: 'Failed to subscribe to our newsletter' },
      { status: 500 }
    );
  }
}