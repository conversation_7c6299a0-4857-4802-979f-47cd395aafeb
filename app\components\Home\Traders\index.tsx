/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useState } from "react";
import { Marquee } from "@devnomic/marquee";
import { TradersData } from "@/data/data";
import "@/public/styles/marquee.css";

// Define type for trader data
interface TraderItem {
    link: string;
    imgSrc: string;
}

const Traders = () => {
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
        setMounted(true);
    }, []);

    if (!mounted) return null; // Or a fallback skeleton

    return (
        <section className='text-center py-11'>
            <div className="mx-auto px-4">
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-bold text-white mb-4 uppercase">Traders</h2>
                </div>
                <div>
                    <Marquee
                        className="overflow-hidden"
                        fade={true}
                        direction="left"
                        reverse={true}
                        pauseOnHover={false}
                    >
                        <div className="flex items-center">
                            {TradersData.map((item: TraderItem, i: number) => (
                                <div key={i} className="mx-8">
                                    <a
                                        href={item.link}
                                        target="_blank"
                                        rel="nofollow noreferrer noopener"
                                        className="inline-block"
                                    >
                                        <img
                                            src={item.imgSrc}
                                            alt={`Trader logo ${i + 1}`}
                                            width={176}
                                            height={176}
                                            className="mx-auto"
                                            loading="lazy"
                                        />
                                    </a>
                                </div>
                            ))}
                        </div>
                    </Marquee>
                </div>
            </div>
        </section>
    )
};
export default Traders;