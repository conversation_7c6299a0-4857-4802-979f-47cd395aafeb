import { createClient } from '@supabase/supabase-js';
import type { AdminRole } from '@/app/context/AdminAuthContext';
import { env } from '@/app/utils/env.server';

// Initialize Supabase client
const supabase = createClient(
  env.supabase.url,
  env.supabase.key,
);

/**
 * Check if a user has admin access
 * @param userId The user ID to check
 * @returns Object containing whether the user is an admin and their role
 */
export async function checkAdminAccess(userId: string) {
  try {
    const { data, error } = await supabase
      .from('AdminUsers')
      .select('role')
      .eq('user_id', userId)
      .single();

    if (error) throw error;

    return {
      isAdmin: !!data,
      role: data?.role as AdminRole | undefined
    };
  } catch (error) {
    console.error('Error checking admin access:', error);
    return {
      isAdmin: false,
      role: undefined
    };
  }
}

/**
 * Get all admin users
 * @returns Array of admin users with their roles
 */
export async function getAdminUsers() {
  try {
    // First get admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('AdminUsers')
      .select('id, user_id, role, created_at, updated_at')
      .order('created_at', { ascending: false });

    if (adminError) throw adminError;

    // Then get user emails
    const userIds = adminUsers.map(admin => admin.user_id);
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .in('id', userIds);

    if (usersError) throw usersError;

    // Combine the data
    return adminUsers.map(admin => {
      const user = users.find(u => u.id === admin.user_id);
      return {
        id: admin.id,
        userId: admin.user_id,
        role: admin.role,
        email: user?.email,
        createdAt: admin.created_at,
        updatedAt: admin.updated_at
      };
    });
  } catch (error) {
    console.error('Error getting admin users:', error);
    return [];
  }
}

/**
 * Add a new admin user
 * @param email The email of the user to add as admin
 * @param role The role to assign to the user
 * @returns Success status and message
 */
export async function addAdminUser(email: string, role: AdminRole) {
  try {
    // First, get the user ID from the email
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .limit(1);

    if (userError) throw userError;

    if (!users || users.length === 0) {
      return {
        success: false,
        message: 'User not found'
      };
    }

    const userId = users[0].id;

    // Check if user is already an admin
    const { data: existingAdmin } = await supabase
      .from('AdminUsers')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (existingAdmin) {
      return {
        success: false,
        message: 'User is already an admin'
      };
    }

    // Add user as admin
    const { error } = await supabase
      .from('AdminUsers')
      .insert({
        user_id: userId,
        role
      });

    if (error) throw error;

    return {
      success: true,
      message: `User ${email} added as ${role}`
    };
  } catch (error) {
    console.error('Error adding admin user:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update an admin user's role
 * @param userId The user ID to update
 * @param role The new role to assign
 * @returns Success status and message
 */
export async function updateAdminRole(userId: string, role: AdminRole) {
  try {
    const { error } = await supabase
      .from('AdminUsers')
      .update({ role, updated_at: new Date().toISOString() })
      .eq('user_id', userId);

    if (error) throw error;

    return {
      success: true,
      message: `User role updated to ${role}`
    };
  } catch (error) {
    console.error('Error updating admin role:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Remove an admin user
 * @param userId The user ID to remove from admin
 * @returns Success status and message
 */
export async function removeAdminUser(userId: string) {
  try {
    const { error } = await supabase
      .from('AdminUsers')
      .delete()
      .eq('user_id', userId);

    if (error) throw error;

    return {
      success: true,
      message: 'Admin user removed successfully'
    };
  } catch (error) {
    console.error('Error removing admin user:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Log admin action for audit purposes
 * @param userId The ID of the admin performing the action
 * @param action The action being performed
 * @param details Additional details about the action
 */
export async function logAdminAction(userId: string, action: string, details: Record<string, any>) {
  try {
    const { error } = await supabase
      .from('AdminAuditLogs')
      .insert({
        user_id: userId,
        action,
        details,
        created_at: new Date().toISOString()
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error logging admin action:', error);
  }
}