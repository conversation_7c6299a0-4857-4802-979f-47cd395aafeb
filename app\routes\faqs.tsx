import { Disclosure } from '@headlessui/react';
import { Icon } from "@iconify/react/dist/iconify.js";

const faqData = [
  {
    heading: "What is the Convention Before Christmas?",
    subheading: "The Convention Before Christmas is a magical Christmas experience for the whole family. Our event features Craft & Collectibles, Live Shows, Fun Fair, Toy Fair, Comic Con, Christmas Market, Toys and much more to celebrate the holiday season."
  },
  {
    heading: "When and where is the event?",
    subheading: "The event will be held at The National Show Center, Swords, Co. Dublin, on the ground floor. The event will take place on December 6th and 7th 2025 from 9:00am to 5:00pm."
  },

  {
    heading: "How do I book a ticket?",
    subheading: "You can book your tickets through our website. Simply click on the 'Book Tickets' button and follow the instructions provided."
  },
  {
    heading: "Do I need to print my tickets?",
    subheading: "No, you don't need to print your tickets. You can show your e-ticket on your smart phone or tablet. However, if you prefer a physical ticket, you're welcome to print it out."
  },
  {
    heading: "Is there parking available?",
    subheading: "Yes, there is free parking available at the venue. We recommend using public transport where possible as parking spaces are limited, especially during peak times."
  },
  {
    heading: "Is the venue wheelchair accessible?",
    subheading: "Yes, the venue is fully accessible for visitors with mobility requirements. The venue has wheelchair access, accessible toilets, and dedicated parking spaces. If you have specific requirements, please contact us before your visit."
  },
  {
    heading: "Are there ATMs on site?",
    subheading: "No, there are no ATMs available on site. However, most vendors will accept card payments. Cash is always an option."
  },
  {
    heading: "What's your refund policy?",
    subheading: "Tickets are non-refundable but can be transferred to another date subject to availability. Please contact our customer service team at least 48 hours before your scheduled visit if you need to make changes."
  },
  {
    heading: "Can I take photographs?",
    subheading: "Yes, personal photography is permitted and encouraged! Share your magical moments with us using #ConBeforeChristmas. Professional photography equipment (including tripods) requires prior permission."
  },
  {
    heading: "Is there a cloakroom/storage facility?",
    subheading: "No, there is no cloakroom or storage facility available on site."
  },
  {
    heading: "Warning!",
    subheading: "In regards to Comic Con/Cosplay, we would ask that no (real world replica weapons) or (real weapons) be brought to this convention, or they will be refused entry. For example, Airsoft guns, metal blades, metal hammers, knives, etc... The only exception for guns at this event are Nerf guns or 3D printed props. We would also ask that all cosplay be family friendly and suitable, as this is a family friendly event. Nudity or half nudity is forbidden.",
    warning: true
  },
];

export default function FAQsPage() {
  return (
    <main className="relative w-full h-full overflow-hidden">
      <div className="container mx-auto px-4 py-16 w-full h-full">
        <div className="w-full mb-8">
          <h1 className="text-4xl font-bold text-center mb-8">Frequently Asked Questions</h1>
          <p className="text-xl text-center max-w-2xl mx-auto">
            We&apos;ve compiled a list of frequently asked questions to help you plan your visit to the Convention Before Christmas. If you have any other questions, please don&apos;t hesitate to contact us. We&apos;re here to make your holiday experience magical!
          </p>
        </div>

        <div className="mx-auto max-w-7xl">
          <div className="grid lg:grid-cols-2 gap-8">
            <div>
              <div className="w-full">
                {faqData.slice(0, Math.ceil(faqData.length / 2)).map((item, i) => (
                  <div className="mx-auto w-full max-w-5xl rounded-2xl bg-[#1e1e1e] py-6 px-6 mb-5 border border-[#2a2a2a]" key={i}>
                    <Disclosure>
                      {({ open }) => (
                        <div>
                          <Disclosure.Button className="flex w-full justify-between rounded-lg text-[var(--darkGold)] sm:px-4 sm:py-2 text-left md:text-xl font-medium">
                            <span>{item.warning ? <span className="!text-red-500">Warning!</span> : item.heading}</span>
                            <Icon icon="tabler:chevron-up" className={`${open ? 'rotate-180 transform' : ''} text-2xl text-[var(--darkGold)]`} />
                          </Disclosure.Button>
                          <Disclosure.Panel className="px-4 pt-4 pb-2 md:text-lg text-white/60 font-normal">
                            {item.subheading}
                          </Disclosure.Panel>
                        </div>
                      )}
                    </Disclosure>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex flex-col">
              <div className="w-full">
                {faqData.slice(Math.ceil(faqData.length / 2)).map((item, i) => (
                  <div className="mx-auto w-full max-w-5xl rounded-2xl bg-[#1e1e1e] py-6 px-6 mb-5 border border-[#2a2a2a]" key={i}>
                    <Disclosure>
                      {({ open }) => (
                        <div>
                          <Disclosure.Button className="flex w-full justify-between rounded-lg text-[var(--darkGold)] sm:px-4 sm:py-2 text-left md:text-xl font-medium">
                            <span>{item.warning ? <span className="!text-red-500">Warning!</span> : item.heading}</span>
                            <Icon icon="tabler:chevron-up" className={`${open ? 'rotate-180 transform' : ''} text-2xl text-[var(--darkGold)]`} />
                          </Disclosure.Button>
                          <Disclosure.Panel className="px-4 pt-4 pb-2 md:text-lg text-white/60 font-normal">
                            {item.subheading}
                          </Disclosure.Panel>
                        </div>
                      )}
                    </Disclosure>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};