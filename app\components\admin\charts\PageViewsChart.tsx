import { useMemo } from 'react';
import { ResponsiveLine } from '@nivo/line';

interface PageViewData {
  date: string;
  views: number;
  uniqueVisitors?: number;
  bounceRate?: number;
}

interface PageViewsChartProps {
  data: PageViewData[];
  height?: number;
  lineColor?: string;
  areaColor?: string;
}

export function PageViewsChart({
  data,
  height = 300,
  lineColor = '#f59e0b', // amber-500
  areaColor = 'rgba(245, 158, 11, 0.1)'
}: PageViewsChartProps) {
  // Format data for Nivo Line chart
  const chartData = useMemo(() => {
    const sorted = [...data].sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    const baseData = {
      id: 'pageViews',
      color: lineColor,
      data: sorted.map(item => ({
        x: item.date,
        y: item.views,
        uniqueVisitors: item.uniqueVisitors,
        bounceRate: item.bounceRate
      }))
    };

    return [baseData];
  }, [data, lineColor]);

  // Custom tooltip
  const CustomTooltip = ({ point }: any) => {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-md p-3 text-sm shadow-lg">
        <div className="font-medium text-white">
          {new Date(point.data.x).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          })}
        </div>
        <div className="flex items-center mt-2">
          <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: point.serieColor }} />
          <span className="text-gray-300">{point.data.y.toLocaleString()} views</span>
        </div>
        {point.data.uniqueVisitors && (
          <div className="flex items-center mt-1">
            <div className="w-3 h-3 rounded-full mr-2 bg-blue-500" />
            <span className="text-gray-300">
              {point.data.uniqueVisitors.toLocaleString()} unique visitors
            </span>
          </div>
        )}
        {point.data.bounceRate && (
          <div className="flex items-center mt-1">
            <div className="w-3 h-3 rounded-full mr-2 bg-red-500" />
            <span className="text-gray-300">
              Bounce rate: {point.data.bounceRate}%
            </span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ height: `${height}px` }} className="w-full">
      <ResponsiveLine
        data={chartData}
        margin={{ top: 20, right: 20, bottom: 50, left: 60 }}
        xScale={{
          type: 'time',
          format: '%Y-%m-%d',
          useUTC: false,
          precision: 'day',
        }}
        xFormat="time:%Y-%m-%d"
        yScale={{
          type: 'linear',
          min: 0,
          max: 'auto',
          stacked: false,
          nice: true
        }}
        curve="monotoneX"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          format: '%b %d',
          legend: 'Date',
          legendOffset: 36,
          legendPosition: 'middle',
          tickValues: 'every 3 days'
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Page Views',
          legendOffset: -50,
          legendPosition: 'middle',
          format: (value) => {
            if (value >= 1000) {
              return `${(value / 1000).toFixed(1)}k`;
            }
            return value.toString();
          }
        }}
        colors={[lineColor]}
        lineWidth={2}
        pointSize={6}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={2}
        pointBorderColor={{ from: 'serieColor' }}
        pointLabelYOffset={-12}
        enableArea={true}
        areaOpacity={0.3}
        areaBaselineValue={0}
        enableGridX={false}
        enableGridY={true}
        enablePoints={data.length <= 30}
        useMesh={true}
        theme={{
          textColor: '#9ca3af',
          fontSize: 12,
          axis: {
            domain: {
              line: {
                stroke: '#374151',
                strokeWidth: 1,
              },
            },
            ticks: {
              line: {
                stroke: '#374151',
                strokeWidth: 1,
              },
            },
          },
          grid: {
            line: {
              stroke: '#374151',
              strokeWidth: 0.5,
              strokeDasharray: '2 2',
            },
          },
          tooltip: {
            container: {
              background: 'transparent',
              padding: 0,
              boxShadow: 'none',
            },
          },
        }}
        tooltip={CustomTooltip}
        legends={[]}
        motionConfig="gentle"
      />
    </div>
  );
};