/**
 * Server-side environment variables
 * This file provides type-safe access to environment variables
 */

function getRequiredEnvVar(key: string): string {
  const value = process.env[key];
  if (!value) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
  return value;
}

// Validate required environment variables at startup
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_KEY',
  'SESSION_SECRET',
  'NODE_ENV',
  'ADMIN_PASSWORD',
  'STRIPE_SECRET_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'RESEND_API_KEY',
  'EMAIL_FROM',
  'ADMIN_EMAIL',
  'SECONDARY_ADMIN_EMAIL',
];

// This will throw an error if any required env vars are missing
requiredEnvVars.forEach(varName => {
  if (!process.env[varName] && process.env.NODE_ENV !== 'test') {
    console.warn(`⚠️  Warning: Missing environment variable ${varName}`);
  }
});

export const env = {
  // Supabase
  supabase: {
    url: getRequiredEnvVar('SUPABASE_URL'),
    key: getRequiredEnvVar('SUPABASE_KEY'),
    serviceKey: process.env.SUPABASE_SERVICE_KEY || '',
  },

  // Session
  session: {
    secret: getRequiredEnvVar('SESSION_SECRET'),
    cookieName: 'admin_session',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  },

  // App
  nodeEnv: process.env.NODE_ENV || 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  isTest: process.env.NODE_ENV === 'test',

  // Default admin credentials (only used in development)
  admin: {
    email: process.env.ADMIN_EMAIL || '<EMAIL>',
    secondaryEmail: process.env.SECONDARY_ADMIN_EMAIL || '<EMAIL>',
    password: process.env.ADMIN_PASSWORD || 'admin123',
    name: process.env.ADMIN_NAME || 'Admin User',
    role: process.env.ADMIN_ROLE || 'super_admin',
  },

  // Other environment variables
  siteUrl: process.env.SITE_URL || 'http://localhost:3000',
  port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000,
  stripeSecretKey: process.env.STRIPE_SECRET_KEY || '',
  stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  resendApiKey: process.env.RESEND_API_KEY || '',
  emailFrom: process.env.EMAIL_FROM || '<EMAIL>',

  // Feature flags
  features: {
    enableEmailVerification: process.env.ENABLE_EMAIL_VERIFICATION === 'true',
    enablePasswordReset: process.env.ENABLE_PASSWORD_RESET === 'true',
    enable2FA: process.env.ENABLE_2FA === 'true',
  },
} as const;

// Type exports
type Env = typeof env;
type SupabaseConfig = Env['supabase'];
type SessionConfig = Env['session'];
type AdminConfig = Env['admin'];
type FeatureFlags = Env['features'];

export type { Env, SupabaseConfig, SessionConfig, AdminConfig, FeatureFlags };
