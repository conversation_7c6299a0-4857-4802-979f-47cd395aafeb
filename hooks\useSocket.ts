'use client';

import { useEffect, useRef } from 'react';
import io, { Socket } from 'socket.io-client';
import { getSocketUrl } from '@/utils/getSocketUrl';

export const useSocket = () => {
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    if (!socketRef.current) {
      const url = getSocketUrl();
      const isSecure = url.startsWith('https');

      socketRef.current = io(url, {
        path: '/api/socket',
        addTrailingSlash: false,
        transports: ['websocket', 'polling'], // Allow both transports from the start
        reconnection: true,
        reconnectionAttempts: Infinity, // Keep trying to reconnect
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        secure: isSecure,
        rejectUnauthorized: false,
        autoConnect: true,
        forceNew: true, // Force a new connection
        withCredentials: true,
      });

      socketRef?.current?.on('connect', () => {
        console.log('Socket connected:', socketRef.current?.id);

        // Set up a heartbeat
        const heartbeat = setInterval(() => {
          if (socketRef.current?.connected) {
            socketRef.current.emit('ping');
          }
        }, 25000);

        // Clean up heartbeat on disconnect
        socketRef?.current?.on('disconnect', () => {
          clearInterval(heartbeat);
        });
      });

      socketRef?.current?.on('connect_error', (err) => {
        console.error('Socket connection error:', err.message);
      });

      socketRef?.current?.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        if (reason === 'transport close' || reason === 'ping timeout') {
          // Attempt to reconnect
          socketRef.current?.connect();
        }
      });

      socketRef?.current?.on('pong', () => {
        console.debug('Received pong from server');
      });
    }

    return () => {
      if (socketRef.current) {
        socketRef.current.removeAllListeners();
        socketRef.current.close();
        socketRef.current = null;
      }
    };
  }, []);

  return socketRef.current;
};

