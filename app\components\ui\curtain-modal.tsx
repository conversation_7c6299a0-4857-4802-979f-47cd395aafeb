"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import styled from 'styled-components';
import Countdown from './countdown';
import { useAudio } from '@/app/context/AudioContext';

interface CurtainModalProps {
  onComplete?: () => void;
}

const CurtainContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

const CurtainWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
`;

const StageBottom = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30px;
  background-color: #5c3d1c;
  z-index: 1002;
`;

const RopeContainer = styled.div`
  position: absolute;
  top: -70px; /* Move it up so it starts off the page */
  right: 5%;
  z-index: 1003;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 260px; /* Increase height to accommodate the extended rope */
`;

const CountdownContainer = styled(motion.div)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1002;
  text-align: center;
  width: 80%;
  max-width: 800px;
`;

const CurtainModal: React.FC<CurtainModalProps> = ({ onComplete }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [showDarkCurtains, setShowDarkCurtains] = useState(false);
  const [countdownVisible, setCountdownVisible] = useState(true);
  const [canUnmount, setCanUnmount] = useState(false);
  const { playBackgroundMusic } = useAudio();
  const santaAudioRef = useRef<HTMLAudioElement | null>(null);
  const ropeRef = useRef<HTMLDivElement | null>(null);
  const numberRef = useRef<HTMLDivElement | null>(null);

  // Motion values for the rope pull effect
  const ropeY = useMotionValue(0);
  const ropeScale = useTransform(ropeY, [0, 100], [1, 0.9]);
  const isPulling = useRef(false);

  const handlePullComplete = () => {
    // Play santa sound when rope is pulled
    if (santaAudioRef.current) {
      santaAudioRef.current.currentTime = 0;
      santaAudioRef.current.play().catch(error => {
        console.error("Audio playback failed:", error);
      });
    }

    setCountdownVisible(false);

    setTimeout(() => {
      setIsOpen(true);

      setTimeout(() => {
        setShowDarkCurtains(true);
      }, 800);

      setTimeout(() => {
        playBackgroundMusic();
        setIsComplete(true);
        if (onComplete) onComplete();
      }, 2000);
    }, 1000);
  };

  // Only return null when both isComplete is true AND audio has finished
  if (isComplete && canUnmount) return null;

  // Set target date to Christmas 2025
  const targetDate = new Date('2025-12-06T09:00:00').toISOString() as any;

  return (
    <CurtainContainer style={{ opacity: isComplete ? 0 : 1 }}>
      {/* Add santa audio element */}
      <audio ref={santaAudioRef} preload="auto">
        <source src="/media/santa.mp3" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>

      <CurtainWrapper>
        {/* Left Curtain */}
        <motion.div
          initial={{ x: 0 }}
          animate={{ x: isOpen ? "-100%" : 0 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
          style={{
            position: "relative",
            width: "50%",
            height: "100%",
            zIndex: 1001,
            backgroundImage: `url('/images/${showDarkCurtains ? 'darkcurtain' : 'frontcurtain'}.jpg')`,
            backgroundSize: "cover",
            backgroundPosition: "right center",
            transition: "background-image 0.3s ease-in-out",
          }}
        />

        {/* Right Curtain */}
        <motion.div
          initial={{ x: 0 }}
          animate={{ x: isOpen ? "100%" : 0 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
          style={{
            position: "relative",
            width: "50%",
            height: "100%",
            zIndex: 1001,
            backgroundImage: `url('/images/${showDarkCurtains ? 'darkcurtain' : 'frontcurtain'}.jpg')`,
            backgroundSize: "cover",
            backgroundPosition: "left center",
            transition: "background-image 0.3s ease-in-out",
          }}
        />

        {/* Countdown in the middle */}
        <CountdownContainer
          initial={{ opacity: 1 }}
          animate={{ opacity: countdownVisible ? 1 : 0 }}
          transition={{ duration: 0.8 }}
          className="curtain-text-container"
        >
          <h2 className="curtain-text text-3xl md:text-4xl lg:text-5xl font-bold text-[var(--darkGold)] mb-8 text-shadow shadow-sm text-center">
            The most anticipated event of the year
          </h2>
          <Countdown
            targetDate={targetDate}
            className="text-shadow shadow-sm grid-cols-4 sm:grid-cols-2 event-countdown"
          />
        </CountdownContainer>

        {/* Rope with Pull Me tag - now draggable */}
        {!isOpen && (
          <RopeContainer className="pull-rope-container">
            <motion.div
              ref={ropeRef}
              drag="y"
              dragConstraints={{ top: 0, bottom: 60 }} /* Increased bottom constraint */
              dragElastic={0.2}
              style={{
                y: ropeY,
                scale: ropeScale,
                cursor: 'grab',
                touchAction: 'none'
              }}
              onDragStart={() => {
                isPulling.current = true;
              }}
              onDrag={(_, info) => {
                // If pulled down enough, trigger the curtain opening
                // Reduced threshold from 80 to 30 pixels for easier activation
                if (info.offset.y > 30 && isPulling.current) {
                  isPulling.current = false;
                  handlePullComplete();
                }
              }}
              whileDrag={{ cursor: 'grabbing' }}
              // Add click/tap handler as fallback
              onClick={() => handlePullComplete()}
            >
              <img
                src="/images/rope.png"
                alt="Pull rope"
                width={40}
                height={280}
                className="object-contain w-full h-64 pull-rope aspect-square"
                draggable="false"
              />
            </motion.div>
          </RopeContainer>
        )}
      </CurtainWrapper>
    </CurtainContainer>
  );
};
export default CurtainModal;