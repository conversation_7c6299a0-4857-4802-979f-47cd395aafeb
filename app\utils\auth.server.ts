import { createCookieSessionStorage, redirect } from '@remix-run/node';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import { env } from '@/app/utils/env.server';

// Initialize Supabase client
const supabaseUrl = env.supabase.url;
const supabaseKey = env.supabase.key;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('SUPABASE_URL and SUPABASE_KEY must be set in environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Admin user type
type AdminUser = {
  id: string;
  email: string;
  name: string;
  role: string;
  created_at: string;
  updated_at: string;
};

// Session configuration
const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error('SESSION_SECRET must be set');
}

const storage = createCookieSessionStorage({
  cookie: {
    name: 'admin_session',
    secure: process.env.NODE_ENV === 'production',
    secrets: [sessionSecret],
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 * 30, // 30 days
    httpOnly: true,
  },
});

export async function createAdminSession(adminId: string, redirectTo: string) {
  const session = await storage.getSession();
  session.set('adminId', adminId);
  return redirect(redirectTo, {
    headers: {
      'Set-Cookie': await storage.commitSession(session),
    },
  });
}

function getAdminSession(request: Request) {
  return storage.getSession(request.headers.get('Cookie'));
}

export async function getAdminId(request: Request) {
  const session = await getAdminSession(request);
  const adminId = session.get('adminId');
  if (!adminId || typeof adminId !== 'string') return null;
  return adminId;
}

export async function getAdmin(request: Request) {
  const adminId = await getAdminId(request);
  if (typeof adminId !== 'string') {
    return null;
  }

  try {
    const { data: admin, error } = await supabase
      .from('AdminUsers')
      .select('id, email, role, name')
      .eq('id', adminId)
      .single();

    if (error) throw error;
    return admin;
  } catch (error) {
    console.error('Error fetching admin user:', error);
    throw await logout(request);
  }
}

export async function requireAdmin(
  request: Request,
  roles: string[] = []
): Promise<AdminUser> {
  const admin = await getAdmin(request);

  if (!admin) {
    const searchParams = new URLSearchParams([['redirectTo', new URL(request.url).pathname]]);
    throw redirect(`/b7a6791b18efd0f58ca1fb26a0ef58dc/login?${searchParams}`);
  }

  if (roles.length > 0 && !roles.includes(admin.role)) {
    throw new Response('Unauthorized', { status: 403 });
  }

  return admin as AdminUser;
}

export async function login(email: string, password: string) {
  try {
    const { data: admin, error } = await supabase
      .from('AdminUsers')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !admin) return null;

    const isCorrectPassword = await bcrypt.compare(password, admin.password);
    if (!isCorrectPassword) return null;

    return {
      id: admin.id,
      email: admin.email,
      role: admin.role,
      name: admin.name
    };
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
}

export async function logout(request: Request) {
  const session = await getAdminSession(request);
  return redirect('/b7a6791b18efd0f58ca1fb26a0ef58dc/login', {
    headers: {
      'Set-Cookie': await storage.destroySession(session),
    },
  });
}

export async function createAdminUser(admin: {
  email: string;
  password: string;
  name: string;
  role: string;
}) {
  try {
    const hashedPassword = await bcrypt.hash(admin.password, 10);
    const { data, error } = await supabase
      .from('AdminUsers')
      .insert([
        {
          email: admin.email,
          password: hashedPassword,
          name: admin.name,
          role: admin.role,
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}