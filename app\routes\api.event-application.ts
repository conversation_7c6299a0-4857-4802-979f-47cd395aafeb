import { json, type ActionFunctionArgs } from "@remix-run/node";
import { validateEmail } from '@/utils/validateEmail';
import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

function formatEmailContent(data: any): string {
  // Create a nicely formatted HTML email
  const fields = Object.entries(data)
    .filter(([key, value]) => value && key !== 'typeLabel')
    .map(([key, value]) => {
      // Format the key for display (capitalize, replace underscores with spaces)
      const formattedKey = key
        .replace(/([A-Z])/g, ' $1')
        .replace(/_/g, ' ')
        .replace(/^\w/, c => c.toUpperCase());

      return `<tr>
        <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">${formattedKey}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${value}</td>
      </tr>`;
    })
    .join('');

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2>New Event Application</h2>
      <p>A new application has been submitted with the following details:</p>
      <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
        <tbody>
          ${fields}
        </tbody>
      </table>
    </div>
  `;
};

export async function action({ request }: ActionFunctionArgs) {
  try {
    const data = await request.json();

    // Validate required fields based on application type
    const requiredFields = ['type'];

    // Add type-specific required fields
    if (['artist', 'exhibitor', 'advertiser'].includes(data.type)) {
      requiredFields.push('businessName', 'vatNumber');
    }

    if (['artist', 'guest', 'exhibitor', 'content_creator', 'advertiser', 'performer', 'costume'].includes(data.type)) {
      requiredFields.push('applicantName', 'contactEmail', 'phoneNumber');
    }

    // Check for missing required fields
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return json(
        { message: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate email if provided
    if (data.contactEmail && !validateEmail(data.contactEmail)) {
      return json(
        { message: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Format the email content
    const emailContent = formatEmailContent(data);

    // Send email using Resend
    const { data: emailData, error: emailError } = await resend.emails.send({
      from: process.env.EMAIL_FROM!,
      to: process.env.ADMIN_EMAIL!,
      subject: `New Event Application: ${data.typeLabel || data.type}`,
      html: emailContent,
    });

    if (emailError) {
      throw emailError;
    }

    console.log("Application notification email sent successfully:", {
      to: process.env.ADMIN_EMAIL,
      messageId: emailData?.id
    });

    return json({ message: 'Application submitted successfully' });
  } catch (error: any) {
    console.error('Error processing application:', error);
    return json(
      { message: 'Failed to process application', error: error.message },
      { status: 500 }
    );
  }
};