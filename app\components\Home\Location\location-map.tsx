"use client";

import React, { useEffect, useRef, useState } from "react";

const MAPBOX_TOKEN = 'pk.eyJ1IjoiY2hpbGxlZGtvbmdzIiwiYSI6ImNtMHptbWtybDA3eGEyanNnejhmeG03NnUifQ.koQ7k5Km5ttOZP9gKN9uzA';

const LocationMap: React.FC = () => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const mapRef = useRef<any>(null);
  const [inView, setInView] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!mapContainer.current) return;
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );
    observer.observe(mapContainer.current);
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!inView || !mapContainer.current || mapRef.current) return;
    setLoading(true);
    import("mapbox-gl").then((mapboxgl) => {
      import("mapbox-gl/dist/mapbox-gl.css");
      mapboxgl.default.accessToken = MAPBOX_TOKEN;
      if (!mapboxgl.default.supported()) {
        setLoading(false);
        return;
      }
      mapRef.current = new mapboxgl.default.Map({
        container: mapContainer.current!,
        style: "mapbox://styles/mapbox/dark-v11",
        center: [-6.2251355, 53.4368267],
        zoom: 14,
      });
      const popup = new mapboxgl.default.Popup({
        closeOnClick: false,
        closeButton: false,
        offset: 25,
      })
        .setLngLat([-6.2251355, 53.4368267])
        .setHTML(
          '<h3 class="text-black font-bold">The National Show Centre</h3><p class="text-black">Stockhole Lane, Cloghran, Dublin</p>'
        )
        .addTo(mapRef.current);
      const marker = new mapboxgl.default.Marker()
        .setLngLat([-6.2251355, 53.4368267])
        .setPopup(popup)
        .addTo(mapRef.current);
      marker.togglePopup();
      setLoading(false);
    });
  }, [inView]);

  return (
    <div
      ref={mapContainer}
      className="w-full h-[300px] rounded-lg overflow-hidden mb-8 bg-gray-900"
    >
      {loading && (
        <div className="w-full h-full flex items-center justify-center text-white">Loading map...</div>
      )}
    </div>
  );
};

export default LocationMap;