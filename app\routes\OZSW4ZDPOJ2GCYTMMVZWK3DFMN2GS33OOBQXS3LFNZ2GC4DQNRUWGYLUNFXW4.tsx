import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { createClient } from '@supabase/supabase-js';
import VendorStandMap from '@/app/components/VendorMap/VendorStandMap';
import { standPositions } from '@/data/standPositions';

// Define types for booked stands
interface BookedStand {
  stand_number: number;
}

// Remix Loader function
export async function loader() {
  // Initialize Supabase client *inside* the loader (server-side)
  const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_KEY!
  );

  const { data: bookedStands, error } = await supabase
      .from('VendorStands')
      .select('stand_number');

  if (error) {
      console.error('Error fetching booked stands:', error);
      // In a real app, you might throw an error or return a specific status
      throw new Error('Failed to fetch booked stands.');
  }

  // bookedStands data is now guaranteed to be an array of objects like { stand_number: number }
  // We can cast it for better type safety after confirming the query and error handling
  return json({ bookedStands: bookedStands as BookedStand[] });
}

export default function VendorStandMapPage() {
    // Use useLoaderData to access data fetched by the loader
    const { bookedStands } = useLoaderData<typeof loader>();

    const initialStands = standPositions.map(stand => ({
        ...stand,
        isAvailable: !bookedStands.some((booked: BookedStand) =>
            booked.stand_number === stand.id
        )
    }));

    return (
        <div className="container mx-auto py-8">
            <h1 className="text-2xl font-bold mb-4">Vendor Stand Map</h1>

            {/* Pass the fetched data to the component */}
            <VendorStandMap
                standPositions={initialStands}
                bookedStands={bookedStands.map(bs => bs.stand_number)}
            />
        </div>
    );
}