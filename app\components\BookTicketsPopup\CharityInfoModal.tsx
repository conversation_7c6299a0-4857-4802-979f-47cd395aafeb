"use client";

import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';

interface CharityInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CharityInfoModal: React.FC<CharityInfoModalProps> = ({ isOpen, onClose }) => {
  const [totalDonations, setTotalDonations] = useState(0);
  const goalAmount = 70000;
  const progressPercentage = Math.min((totalDonations / goalAmount) * 100, 100);
  const modalRef = useRef<HTMLDivElement>(null);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, onClose);

  useEffect(() => {
    const fetchDonations = async () => {
      try {
        const response = await fetch('/api/charity/total-donations');
        if (response.ok) {
          const data = await response.json();
          setTotalDonations(data.totalAmount || 0);
        }
      } catch (error) {
        console.error('Error fetching donation data:', error);
      }
    };

    if (isOpen) {
      fetchDonations();
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70"
        >
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 max-w-md w-full mx-4"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 id="charity-modal-title" className="text-xl font-semibold text-white">Support LauraLynn Children&apos;s Hospice</h3>
            </div>

            <div className="text-white space-y-4 max-h-[300px] overflow-y-auto custom-scrollbar flex-grow">
              <p>
                LauraLynn is Ireland&apos;s only children&apos;s hospice providing specialized care to children with life-limiting conditions and support to their families.
              </p>

              <p>
                We&apos;re aiming to raise <span className="text-[var(--darkGold)] font-semibold">€70,000</span> to help LauraLynn continue their vital work. Your donation with each ticket purchase makes a real difference.
              </p>

              <div className="mt-4">
                <p className="mb-2 font-semibold">Fundraising Progress: €{totalDonations.toLocaleString()} of €{goalAmount.toLocaleString()}</p>
                <div className="w-full h-4 bg-gray-800 rounded-full">
                  <div
                    className="h-4 bg-[var(--darkGold)] rounded-full"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
                <p className="mt-2 text-sm text-right">{progressPercentage.toFixed(1)}% of goal</p>
              </div>

              <p>
                <span className="text-[var(--darkGold)] font-semibold">How Your Donation Helps:</span>
              </p>

              <ul className="list-disc pl-5 space-y-2">
                <li>Provides specialized care for children with life-limiting conditions</li>
                <li>Supports families through difficult times</li>
                <li>Helps fund essential medical equipment and services</li>
                <li>Contributes to making precious memories for families</li>
              </ul>
            </div>

            <div className="mt-6 text-center w-full">
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default CharityInfoModal;
