import { useMemo, useState } from 'react';
import { ResponsiveChoropleth } from '@nivo/geo';
import worldCountries from './world_countries.json';

interface RegionData {
  id: string;
  name: string;
  value: number;
  percentage?: number;
}

interface GeographicDistributionMapProps {
  data: RegionData[];
  title?: string;
  height?: number;
}

export function GeographicDistributionMap({
  data,
  title = 'User Geographic Distribution',
  height = 400
}: GeographicDistributionMapProps) {
  const [hoveredRegion, setHoveredRegion] = useState<RegionData | null>(null);

  // Calculate total for percentages
  const total = data.reduce((sum, region) => sum + region.value, 0);

  // Format data for Nivo Geo
  const formattedData = useMemo(() => {
    return data.map(region => ({
      id: region.id,
      value: region.value,
      name: region.name,
      percentage: total > 0 ? (region.value / total) * 100 : 0
    }));
  }, [data, total]);

  // Get color for a specific value
  const getColor = (value: number) => {
    const maxValue = Math.max(...data.map(r => r.value));
    const minValue = Math.min(...data.map(r => r.value));
    const range = maxValue - minValue;
    const normalizedValue = range > 0 ? (value - minValue) / range : 0.5;

    // Dark red to bright red gradient
    const r = Math.round(185 + (normalizedValue * 70));
    const g = Math.round(28 + (normalizedValue * 28));
    const b = Math.round(28 + (normalizedValue * 28));
    return `rgb(${r}, ${g}, ${b})`;
  };

  // Generate color scale for the map
  const colorScale = useMemo(() => {
    const values = data.map(d => d.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const range = max - min;

    return [
      { offset: 0, color: '#b91c1c' }, // dark red
      { offset: 50, color: '#dc2626' }, // red
      { offset: 100, color: '#ef4444' }  // light red
    ];
  }, [data]);

  // Find region by ID
  const findRegionById = (id: string) => {
    return data.find(region => region.id === id) || null;
  };

  return (
    <div className="bg-zinc-800 rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold text-gray-200 mb-4">{title}</h3>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Map */}
        <div className="w-full lg:w-2/3" style={{ height: `${height}px` }}>
          <ResponsiveChoropleth
            data={formattedData}
            features={worldCountries.features}
            margin={{ top: 0, right: 0, bottom: 0, left: 0 }}
            colors="reds"
            domain={[0, Math.max(...formattedData.map(d => d.value))]}
            unknownColor="#1f2937"
            label="properties.name"
            valueFormat=".0s"
            projectionType="mercator"
            projectionScale={100}
            projectionTranslation={[0.5, 0.5]}
            projectionRotation={[0, 0, 0]}
            borderWidth={0.5}
            borderColor="#1f2937"
            theme={{
              tooltip: {
                container: {
                  background: '#1f2937',
                  color: '#f3f4f6',
                  fontSize: '12px',
                  borderRadius: '4px',
                  boxShadow: '0 3px 9px rgba(0, 0, 0, 0.5)'
                }
              },
              labels: {
                text: {
                  fill: '#f3f4f6',
                  fontSize: '11px',
                  fontWeight: 500
                }
              }
            }}
            onMouseEnter={(feature: any) => {
              if (feature?.id) {
                const region = findRegionById(feature.id as string);
                if (region) setHoveredRegion(region);
              }
            }}
            onMouseLeave={() => setHoveredRegion(null)}
            tooltip={({ feature }: any) => {
              const region = findRegionById(feature.id as string);
              if (!region) return null;

              return (
                <div className="bg-zinc-800 p-2 rounded border border-zinc-700 shadow-lg">
                  <div className="font-semibold text-gray-200">{region.name}</div>
                  <div className="flex items-center mt-1">
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: getColor(region.value) }}
                    />
                    <span className="text-sm text-gray-300">
                      {region.value} users ({region.percentage?.toFixed(1)}%)
                    </span>
                  </div>
                </div>
              );
            }}
          />
        </div>

        {/* Legend */}
        <div className="w-full lg:w-1/3 overflow-y-auto" style={{ maxHeight: `${height}px` }}>
          <h4 className="text-sm font-medium text-gray-300 mb-3">Top Regions</h4>
          <div className="space-y-2 pr-2">
            {formattedData
              .sort((a, b) => b.value - a.value)
              .map((region) => (
                <div
                  key={region.id}
                  className="flex items-center p-2 rounded-md hover:bg-zinc-700/50 transition-colors"
                  onMouseEnter={() => setHoveredRegion(region)}
                  onMouseLeave={() => setHoveredRegion(null)}
                >
                  <div
                    className="w-4 h-4 rounded-sm mr-3 flex-shrink-0"
                    style={{ backgroundColor: getColor(region.value) }}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-baseline">
                      <span className="font-medium text-gray-200 truncate mr-2">
                        {region.name}
                      </span>
                      <span className="text-sm text-gray-400 flex-shrink-0">
                        {region.value}
                      </span>
                    </div>
                    <div className="w-full bg-zinc-700 rounded-full h-1.5 mt-1.5">
                      <div
                        className="h-1.5 rounded-full"
                        style={{
                          width: `${region.percentage}%`,
                          backgroundColor: getColor(region.value)
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
}