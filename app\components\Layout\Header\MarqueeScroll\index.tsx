"use client";

import React, { useEffect } from 'react';
import Marque<PERSON> from 'react-fast-marquee';

interface MarqueeScrollProps {
  phrases: string[];
  sticky: boolean;
}

const MarqueeScroll: React.FC<MarqueeScrollProps> = ({ phrases, sticky }) => {
  return (
    <div className={`bg-darkGold text-black py-1 w-full absolute ${sticky ? 'hidden' : '-bottom-5'}`}>
      <Marquee
        speed={50}
        gradient={false}
        className="overflow-hidden"
      >
        <div className="flex">
          {phrases.map((phrase, index) => (
            <div key={index} className="mx-8 text-sm text-[#151515] font-medium whitespace-nowrap">
              {phrase}
            </div>
          ))}
        </div>
      </Marquee>
    </div>
  );
};
export default MarqueeScroll;