"use client";

import { useState, useEffect } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';

interface PaymentFormProps {
  onSuccess: () => void;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  onReady?: () => void;
  clientSecret?: string | null | undefined;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  onSuccess,
  isProcessing,
  setIsProcessing,
  onReady,
  clientSecret
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');

    if (!stripe || !elements || !clientSecret) {
      setErrorMessage('Payment system not initialized');
      return;
    }

    try {
      setIsProcessing(true);

      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw submitError;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        confirmParams: {
          return_url: typeof window !== 'undefined' ? `${window.location.origin}/payment-confirmation` : '',
        },
        redirect: 'if_required',
      });

      if (error) {
        throw error;
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
      onSuccess();
      } else {
        throw new Error('Payment failed. Please try again.');
      }

    } catch (err: any) {
      console.error('Payment error:', err);
      setErrorMessage(err.message || 'Payment failed');
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <PaymentElement
        id="payment-element"
        options={{
          layout: 'accordion',
          paymentMethodOrder: ['card'],
          defaultValues: {
            billingDetails: {
              email: ''
            }
          }
        }}
        onReady={() => {
          console.log('Payment element is ready');
          setIsLoading(false);
        }}
        onChange={(data: any) => {
          console.log('Payment element changed:', data);
        }}
        onLoaderStart={() => console.log('Payment element loader started')}
      />

      {errorMessage && (
        <div className="text-red-500 text-sm">{errorMessage}</div>
      )}

      <button
        type="submit"
        disabled={isProcessing || isLoading || !stripe || !elements}
        className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg disabled:opacity-50"
      >
        {isProcessing ? 'Processing...' : (isLoading || !stripe || !elements) ? 'Loading...' : 'Pay Now'}
      </button>
    </form>
  );
};
export default PaymentForm;