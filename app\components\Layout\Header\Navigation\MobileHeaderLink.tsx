import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';
import { HeaderItem } from "@/types/menu";

interface MobileHeaderLinkProps {
  item: HeaderItem;
  onClick: () => void;
  isButton?: boolean;
  isBookTickets?: boolean;
  className?: string;
}

const MobileHeaderLink = ({
  item,
  onClick,
  isButton,
  isBookTickets = false,
  className = "",
}: MobileHeaderLinkProps) => {
  const [submenuOpen, setSubmenuOpen] = useState(false);
  const [isButtonHighlighted, setIsButtonHighlighted] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Close submenu when path changes
  useEffect(() => {
    setSubmenuOpen(false);
  }, [location.pathname]);

  const handleToggle = (e: React.MouseEvent) => {
    if (item.submenu) {
      setSubmenuOpen(!submenuOpen);
    }
  };

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    // 1) If this item has a submenu, just toggle it
    if (item.submenu) {
      handleToggle(e);
    }

    // 2) If it's the special "Book Tickets" audio case
    if (isBookTickets) {
      if (audioRef.current) {
        audioRef.current.currentTime = 0;
        audioRef.current.play().catch((error) => {
          console.error("Audio playback failed:", error);
        });
      }
      onClick();
      navigate('/book-tickets');
      return;
    }

    // 3) If it's a hash-link (contains "#")
    if (item.href.includes("#")) {
      const [path, hash] = item.href.split("#");
      const elementId = hash ? `#${hash}` : null;

      if (elementId) {
        // If user is already on the correct "page"
        const isSamePage = (
          location.pathname === path ||
          (location.pathname === '/' && path === '')
        );

        if (isSamePage) {
          onClick();
          setTimeout(() => {
            const target = document.querySelector(elementId);
            if (target) {
              target.scrollIntoView({ behavior: "smooth" });
            } else {
              console.warn(`Element ${elementId} not found in DOM`);
            }
          }, 50);

          return; // we're done
        }
      }

      // 4) If the element isn't on this page, navigate normally
      onClick();
      navigate(item.href);
      return;
    }

    // 5) For any "normal" link with no hash
    onClick();
    navigate(item.href);
  };

  useEffect(() => {
    if (item.button) {
      const interval = setInterval(() => {
        setIsButtonHighlighted(true);

        setTimeout(() => {
          setIsButtonHighlighted(false);
        }, 1000);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [item]);

  return (
    <>
      {isBookTickets && (
        <audio ref={audioRef} preload="auto">
          <source src="/media/santa.mp3" type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
      )}

      <button
        onClick={handleClick}
        className={`
          ${isButton ?
            `button-glare overflow-hidden bg-darkGold text-white dark:bg-darkGold dark:text-white hover:bg-[#A67C00] px-2 py-2 rounded-md ${isButtonHighlighted ? 'hover' : ''}`
            :
            'text-white hover:text-darkGold transition-colors duration-300 py-4 text-xl block w-full text-center'
          }
          ${className}
          ${item.href ? '' : 'cursor-default'}
        `}
        {...(item.href && { role: 'link', 'aria-label': item.label })}
        type="button"
      >
        {item.label}
      </button>

      {item.submenu && submenuOpen && (
        <div className="pl-4 mt-2 space-y-2">
          {item.submenu.map((subItem: any) => (
            <MobileHeaderLink
              key={subItem.id}
              item={subItem}
              onClick={onClick}
              className="text-sm text-gray-300 hover:text-white"
            />
          ))}
        </div>
      )}
    </>
  );
};
export default MobileHeaderLink;