import { ResponsiveLine } from '@nivo/line';
import { useMemo } from 'react';

export interface EventsByType {
  date: string;
  [eventType: string]: string | number;
}

interface EventsByTypeChartProps {
  data: EventsByType[];
  height?: number;
}

const EVENT_TYPE_COLORS: Record<string, string> = {
  page_view: '#f59e0b',
  first_visit: '#3b82f6',
  device_detected: '#10b981',
  user_demographics: '#6366f1',
  login_error: '#ef4444',
  login_attempt: '#f472b6',
  login_failed: '#b91c1c',
  form_interaction: '#a21caf',
  ui_interaction: '#0ea5e9',
  error_occurred: '#eab308',
  session_start: '#22d3ee',
  session_end: '#64748b',
};

export function EventsByTypeChart({ data, height = 350 }: EventsByTypeChartProps) {
  const eventTypes = useMemo(() => {
    if (!data.length) return [];
    return Object.keys(data[0]).filter((k) => k !== 'date');
  }, [data]);

  const chartData = useMemo(() => {
    return eventTypes.map((type) => ({
      id: type,
      color: EVENT_TYPE_COLORS[type] || '#888',
      data: data.map((row) => ({ x: row.date, y: Number(row[type] || 0) })),
    }));
  }, [data, eventTypes]);

  if (!data.length) {
    return <div className="flex items-center justify-center h-full"><span className="text-amber-100/50">No event data available</span></div>;
  }

  return (
    <div style={{ height }} className="w-full">
      <ResponsiveLine
        data={chartData}
        margin={{ top: 60, right: 20, bottom: 50, left: 60 }}
        xScale={{ type: 'point' }}
        yScale={{ type: 'linear', min: 0, max: 'auto', stacked: false, nice: true }}
        curve="monotoneX"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Date',
          legendOffset: 36,
          legendPosition: 'middle',
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Event Count',
          legendOffset: -50,
          legendPosition: 'middle',
          format: (value) => (value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value.toString()),
        }}
        colors={({ id }) => EVENT_TYPE_COLORS[id as string] || '#888'}
        lineWidth={2}
        pointSize={6}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={2}
        pointBorderColor={{ from: 'serieColor' }}
        pointLabelYOffset={-12}
        enableArea={true}
        areaOpacity={0.3}
        areaBaselineValue={0}
        enableGridX={false}
        enableGridY={true}
        enablePoints={data.length <= 30}
        useMesh={true}
        theme={{
          text: { fill: '#9ca3af', fontSize: 12 },
          axis: {
            domain: { line: { stroke: '#374151', strokeWidth: 1 } },
            ticks: { line: { stroke: '#374151', strokeWidth: 1 } },
          },
          grid: { line: { stroke: '#374151', strokeWidth: 0.5, strokeDasharray: '2 2' } },
          tooltip: { container: { background: 'transparent', padding: 0, boxShadow: 'none' } },
        }}
        tooltip={({ point }) => {
          const p = point as any;
          return (
            <div className="bg-zinc-800 border border-zinc-700 rounded-md p-2 text-sm min-w-28">
              <div className="font-medium text-white">{p.data.xFormatted}</div>
              <div className="flex items-center mt-1">
                <div
                  className="w-3 h-3 rounded-sm mr-2"
                  style={{ backgroundColor: p.serieColor, opacity: 0.8 }}
                />
                <span className="text-gray-300">
                  {p.serieId}: {p.data.yFormatted}
                </span>
              </div>
            </div>
          );
        }}
        motionConfig="gentle"
      />
      <div className="flex flex-wrap gap-4 mt-4 justify-center">
        {eventTypes.map((type) => (
          <div key={type} className="flex items-center gap-2">
            <span className="w-3 h-3 rounded-full" style={{ backgroundColor: EVENT_TYPE_COLORS[type] || '#888' }} />
            <span className="text-xs text-gray-300">{type.replace(/_/g, ' ')}</span>
          </div>
        ))}
      </div>
    </div>
  );
}