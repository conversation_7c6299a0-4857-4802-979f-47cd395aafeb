import { Link, useLocation } from '@remix-run/react';
import {
  Home,
  BarChart,
  Users,
  FileText,
  AlertTriangle,
  Settings,
  Shield,
  Menu,
  ChevronRight
} from 'lucide-react';
import { type AdminRole } from '@/app/context/AdminAuthContext';
import { cn } from '@/lib/utils';

interface AdminSidebarProps {
  collapsed: boolean;
  userRole?: AdminRole;
  toggleSidebar: () => void;
}

interface NavItem {
  label: string;
  icon: React.ReactNode;
  href: string;
  roles: AdminRole[];
  subItems?: NavItem[];
}

export function AdminSidebar({ collapsed, userRole, toggleSidebar }: AdminSidebarProps) {
  const location = useLocation();

  const navItems: NavItem[] = [
    {
      label: 'Dashboard',
      icon: <Home className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc',
      roles: ['super_admin', 'finance_admin', 'support_admin']
    },
    {
      label: 'Financial Metrics',
      icon: <BarChart className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/financial',
      roles: ['super_admin', 'finance_admin', 'support_admin'],
      subItems: [
        {
          label: 'Revenue Overview',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/financial/revenue',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        },
        {
          label: 'Ticket Sales',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/financial/tickets',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        },
        {
          label: 'Donations',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/financial/donations',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        },
        {
          label: 'Vendor Stalls',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/financial/stalls',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        }
      ]
    },
    {
      label: 'User Analytics',
      icon: <Users className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/users',
      roles: ['super_admin', 'finance_admin', 'support_admin'],
      subItems: [
        {
          label: 'Demographics',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/users/demographics',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        },
        {
          label: 'Behaviour',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/users/behaviour',
          roles: ['super_admin', 'finance_admin', 'support_admin']
        },
        {
          label: 'Management',
          icon: <ChevronRight className="w-4 h-4" />,
          href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/users/management',
          roles: ['super_admin']
        }
      ]
    },
    {
      label: 'Transactions',
      icon: <FileText className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/transactions',
      roles: ['super_admin', 'finance_admin', 'support_admin']
    },
    {
      label: 'System Performance',
      icon: <AlertTriangle className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/performance',
      roles: ['super_admin', 'finance_admin', 'support_admin']
    },
    {
      label: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/settings',
      roles: ['super_admin', 'finance_admin', 'support_admin']
    },
    {
      label: 'Admin Controls',
      icon: <Shield className="w-5 h-5" />,
      href: '/b7a6791b18efd0f58ca1fb26a0ef58dc/controls',
      roles: ['super_admin']
    }
  ];

  // Check if user has permission to view the item
  const hasPermission = (roles: AdminRole[]) => {
    if (!userRole) return false;
    return roles.includes(userRole);
  };

  return (
    <aside
      className={cn(
        'fixed md:relative z-10 h-screen transition-all duration-300 ease-in-out flex flex-col min-w-0',
        'bg-zinc-800 border-r border-amber-600/30',
        collapsed ? 'w-16' : 'w-64'
      )}
    >
      <div className="p-4 border-b border-amber-600/30 flex-shrink-0">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <h1 className="text-xl font-bold text-amber-200">Admin</h1>
          )}
          <button
            onClick={toggleSidebar}
            className="p-1 rounded-md text-amber-200 hover:bg-amber-900/50"
          >
            <Menu className="w-5 h-5" />
          </button>
        </div>
      </div>

      <nav className="flex-1 overflow-y-auto py-4 px-2">
        <div className="space-y-1">
          {navItems.map((item) => (
            <div key={item.href}>
              {hasPermission(item.roles) && (
                <>
                  <Link
                    to={item.href}
                    className={cn(
                      'flex items-center px-4 py-3 text-sm font-medium rounded-md',
                      location.pathname === item.href
                        ? 'bg-amber-900/50 text-amber-100 border-l-4 border-amber-400'
                        : 'text-gray-300 hover:bg-amber-900/30',
                      collapsed ? 'justify-center' : 'px-4'
                    )}
                  >
                    <span className="flex-shrink-0">{item.icon}</span>
                    {!collapsed && <span className="ml-3">{item.label}</span>}
                  </Link>

                  {!collapsed && item.subItems && (
                    <div className="ml-6 mt-1 space-y-1">
                      {item.subItems
                        .filter(subItem => userRole && subItem.roles.includes(userRole))
                        .map(subItem => (
                          <Link
                            key={subItem.href}
                            to={subItem.href}
                            className={cn(
                              'flex items-center px-3 py-1.5 rounded-md transition-colors text-sm',
                              location.pathname === subItem.href
                                ? 'bg-amber-900/50 text-amber-100'
                                : 'text-gray-300 hover:bg-amber-900/30'
                            )}
                          >
                            <span className="flex-shrink-0">{subItem.icon}</span>
                            <span className="ml-2">{subItem.label}</span>
                          </Link>
                        ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
      </nav>
    </aside>
  );
}