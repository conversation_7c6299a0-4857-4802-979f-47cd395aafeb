import React from 'react';
import { TicketType, CartItem } from '@/types/ticket';
import { Tab, Ta<PERSON>, Tab<PERSON>ist, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';
import { InfoIcon } from 'lucide-react';
import Tippy from '@tippyjs/react';
import 'tippy.js/dist/tippy.css';
import 'tippy.js/themes/light.css';

// Import Redux hooks and actions
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '@/store/store';
import {
  updateCartItemQuantity,
  clearCart,
  addToCart,
} from '@/store/slices/booking-slice';

interface TicketSelectStepProps {
  ticketTypes: TicketType[];
  loading: boolean;
  onContinue: () => void;
  onOpenFamilyInfo: () => void;
  onOpenSantaInfo: () => void;
  onOpenSantaSlot: (ticket: TicketType) => void;
  onOpenCharityInfo: () => void;
  hasRegularTickets: () => boolean;
  renderCharityMultipliers: (ticket: TicketType) => React.ReactNode;
  calculateTotal: () => number;
  updateQuantity: (ticketType: TicketType, newQuantity: number) => void;
  handlePlusClick: (ticketType: TicketType) => void;
}

const TicketSelectStep: React.FC<TicketSelectStepProps> = ({
  ticketTypes,
  loading,
  onContinue,
  onOpenFamilyInfo,
  onOpenSantaInfo,
  onOpenSantaSlot,
  onOpenCharityInfo,
  hasRegularTickets,
  renderCharityMultipliers,
  calculateTotal,
  updateQuantity,
  handlePlusClick,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const cart = useSelector((state: RootState) => state.booking.cart);

  const handleClearCart = () => {
    dispatch(clearCart());
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-white mb-4">Select Tickets</h3>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--darkGold)] mx-auto"></div>
          <p className="text-white mt-4">Loading tickets...</p>
        </div>
      ) : (
        <Tabs className="text-white">
          <TabList className="flex mb-4 border-b border-[var(--darkGold)]">
            <Tab className="px-4 py-2 cursor-pointer hover:bg-[var(--darkGold)]/20 focus:outline-none text-center sm:text-left w-1/2 sm:w-auto" selectedClassName="bg-[var(--darkGold)]/30 border-b-2 border-[var(--darkGold)]">1 Day Tickets</Tab>
            <Tab className="px-4 py-2 cursor-pointer hover:bg-[var(--darkGold)]/20 focus:outline-none text-center sm:text-left w-1/2 sm:w-auto" selectedClassName="bg-[var(--darkGold)]/30 border-b-2 border-[var(--darkGold)]">2 Day Tickets</Tab>
          </TabList>

          {/* 1 Day Tickets Tab */}
          <TabPanel>
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-[var(--darkGold)]">1 Day Tickets</h4>

              {ticketTypes.length === 0 ? (
                <p className="text-white w-full text-center">No tickets available</p>
              ) : (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {ticketTypes
                      .filter(ticket => ticket.name.includes('1 Day'))
                      .map((ticket: any) => {
                         const cartItem = cart.find(item => item.ticketType.id === ticket.id || (item.ticketType.originalId && item.ticketType.originalId === ticket.id));
                         const quantity = cartItem ? cartItem.quantity : 0;
                         const isFamilyPass = ticket.id.includes('family-1day'); // Identify family pass
                        return (
                          <div
                            key={ticket.id}
                            className={`flex flex-col justify-between p-3 border border-[var(--darkGold)] rounded-lg ${isFamilyPass ? 'sm:col-span-full' : ''}`} // Apply col-span-full for family pass
                          >
                            <div className="text-center">
                              <h4 className="text-white font-semibold text-sm flex items-center justify-center">
                                {ticket.name}
                                {ticket.id.includes('family') && (
                                  <Tippy content="Click for more info" placement="top" theme="light">
                                    <button
                                      onClick={e => {
                                        e.stopPropagation();
                                        onOpenFamilyInfo();
                                      }}
                                      className="ml-1 text-[var(--darkGold)] hover:text-[var(--lightGold)]"
                                    >
                                      <InfoIcon size={16} />
                                    </button>
                                  </Tippy>
                                )}
                              </h4>
                              <p className="text-gray-400 text-xs">{ticket.description}</p>
                              <p className="text-[var(--darkGold)] text-sm">€{ticket.price}</p>
                            </div>
                            <div className="flex items-center justify-center space-x-2 mt-2">
                               <button
                                onClick={() => updateQuantity(ticket, quantity - 1)}
                                className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                                disabled={quantity <= 0}
                              >
                                -
                              </button>
                              <span className="text-white text-sm">
                                {quantity}
                              </span>
                              <button
                                onClick={() => handlePlusClick(ticket)}
                                className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                              >
                                +
                              </button>
                            </div>
                          </div>
                        );
                      })}
                  </div>

                  {/* Addons section */}
                  <h4 className="text-lg font-medium text-[var(--darkGold)] mt-6">Addons</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {ticketTypes
                      .filter(ticket => ticket.type === 'option' || ticket.id === 'charity-donation')
                      .map((ticket: any) => {
                        const isDisabled = ticket.id === 'santa-option' && !hasRegularTickets();
                        const isDonationOption = ticket.id === 'charity-donation';
                        const cartItem = cart.find(item => item.ticketType.id === ticket.id);
                        const quantity = cartItem ? cartItem.quantity : 0;

                        return (
                          <div
                            key={ticket.id}
                            className={`flex flex-col justify-between p-3 border ${isDisabled ? 'border-gray-600 opacity-60' : 'border-[var(--darkGold)]'} rounded-lg`}
                          >
                            <div className="text-center">
                              <div className="flex items-center justify-center">
                                <h4 className="text-white font-semibold text-sm">{ticket.name}</h4>
                                {isDonationOption && (
                                  <Tippy content="Click for more info" placement="top" theme="light">
                                    <button
                                      onClick={onOpenCharityInfo}
                                      className="ml-2 text-[var(--darkGold)] hover:text-[var(--darkGold)]/80"
                                    >
                                      <InfoIcon size={16} />
                                    </button>
                                  </Tippy>
                                )}
                                {ticket.id.includes('santa-option') && (
                                  <Tippy content="Click for more info" placement="top" theme="light">
                                    <button
                                      onClick={() => onOpenSantaInfo()} // Use the prop function to open panel
                                      className="ml-2 text-[var(--darkGold)] hover:text-[var(--darkGold)]/80"
                                    >
                                      <InfoIcon size={16} />
                                    </button>
                                  </Tippy>
                                )}
                              </div>
                              <p className="text-gray-400 text-xs">
                                {isDisabled ? 'Add at least one ticket first' : ticket.description}
                              </p>
                              <p className="text-[var(--darkGold)] text-sm">€{ticket.price}</p>
                            </div>
                            <div className="flex items-center justify-center mt-2">
                              {ticket.id.includes('santa-option') ? (
                                <div className="w-full">
                                  <button
                                    onClick={() => onOpenSantaSlot(ticket)}
                                    className={`px-4 py-2 ${isDisabled ? 'bg-gray-600 cursor-not-allowed' : 'bg-[var(--darkGold)] hover:bg-[var(--darkGold)]/80'} rounded text-sm font-medium w-full`}
                                    disabled={isDisabled}
                                  >
                                    {cart.find(item => item.ticketType.id === ticket.id) ? 'Change Slot' : 'Add'}
                                  </button>
                                </div>
                              ) : (
                                <div className="flex items-center justify-center space-x-2 w-full">
                                  <button
                                    onClick={() => updateQuantity(ticket, quantity - 1)}
                                    className="w-8 h-8 flex items-center justify-center bg-gray-700 hover:bg-gray-600 rounded-full text-white" disabled={quantity <= 0}>
                                    -
                                  </button>
                                  <span className="text-white w-6 text-center">{quantity}</span>
                                  <button
                                    onClick={() => ticket.id === 'charity-donation' ? handlePlusClick(ticket) : updateQuantity(ticket, quantity + 1)}
                                    className="w-8 h-8 flex items-center justify-center bg-[var(--darkGold)] hover:bg-[var(--darkGold)]/80 rounded-full text-white"
                                  >
                                    +
                                  </button>
                                </div>
                              )}
                            </div>
                            {ticket.id === 'charity-donation' && renderCharityMultipliers(ticket)}
                          </div>
                        );
                      })}
                  </div>
                </>
              )}
            </div>
          </TabPanel>

          {/* 2 Day Tickets Tab */}
          <TabPanel>
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-[var(--darkGold)]">2 Day Tickets</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {ticketTypes
                  .filter(ticket => ticket.name.includes('2 Day'))
                  .map(ticket => {
                    const cartItem = cart.find(item => item.ticketType.id === ticket.id);
                    const quantity = cartItem ? cartItem.quantity : 0;
                    return (
                      <div
                        key={ticket.id}
                        className="flex flex-col justify-between p-3 border border-[var(--darkGold)] rounded-lg"
                      >
                        <div className="text-center">
                          <h4 className="text-white font-semibold text-sm">{ticket.name}</h4>
                          <p className="text-gray-400 text-xs">{ticket.description}</p>
                          <p className="text-[var(--darkGold)] text-sm">€{ticket.price}</p>
                        </div>
                        <div className="flex items-center justify-center space-x-2 mt-2">
                          <button
                            onClick={() => updateQuantity(ticket, quantity - 1)}
                            className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                            disabled={quantity <= 0}
                          >
                            -
                          </button>
                          <span className="text-white text-sm">
                            {quantity}
                          </span>
                          <button
                            onClick={() => handlePlusClick(ticket)}
                            className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                          >
                            +
                          </button>
                        </div>
                      </div>
                    );
                  })}
              </div>
              {/* Addons section */}
              <h4 className="text-lg font-medium text-[var(--darkGold)] mt-6">Addons</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {ticketTypes
                  .filter(ticket => ticket.type === 'option' || ticket.id === 'charity-donation')
                  .map(ticket => {
                    const isDisabled = ticket.id === 'santa-option' && !hasRegularTickets();
                    const isDonationOption = ticket.id === 'charity-donation';
                    const cartItem = cart.find(item => item.ticketType.id === ticket.id);
                    const quantity = cartItem ? cartItem.quantity : 0;
                    return (
                      <div
                        key={ticket.id}
                        className={`flex flex-col justify-between p-3 border ${isDisabled ? 'border-gray-600 opacity-60' : 'border-[var(--darkGold)]'} rounded-lg`}
                      >
                        <div className="text-center">
                          <div className="flex items-center justify-center">
                            <h4 className="text-white font-semibold text-sm">{ticket.name}</h4>
                            {isDonationOption && (
                              <Tippy content="Click for more info" placement="top" theme="light">
                                <button
                                  onClick={onOpenCharityInfo}
                                  className="ml-2 text-[var(--darkGold)] hover:text-[var(--darkGold)]/80"
                                >
                                  <InfoIcon size={16} />
                                </button>
                              </Tippy>
                            )}
                            {ticket.id.includes('santa-option') && (
                              <Tippy content="Click for more info" placement="top" theme="light">
                                <button
                                  onClick={() => onOpenSantaInfo()} // Use the prop function to open panel
                                  className="ml-2 text-[var(--darkGold)] hover:text-[var(--darkGold)]/80"
                                >
                                  <InfoIcon size={16} />
                                </button>
                              </Tippy>
                            )}
                          </div>
                          <p className="text-gray-400 text-xs">
                            {isDisabled ? 'Add at least one ticket first' : ticket.description}
                          </p>
                          <p className="text-[var(--darkGold)] text-sm">€{ticket.price}</p>
                        </div>
                        <div className="flex items-center justify-center mt-2">
                          {ticket.id.includes('santa-option') ? (
                            <div className="w-full">
                              <button
                                onClick={() => onOpenSantaSlot(ticket)}
                                className={`px-4 py-2 ${isDisabled ? 'bg-gray-600 cursor-not-allowed' : 'bg-[var(--darkGold)] hover:bg-[var(--darkGold)]/80'} rounded text-sm font-medium w-full`}
                                disabled={isDisabled}
                              >
                                {cart.find(item => item.ticketType.id === ticket.id) ? 'Change Slot' : 'Add'}
                              </button>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center space-x-2 w-full">
                              <button
                                onClick={() => updateQuantity(ticket, quantity - 1)}
                                className="w-8 h-8 flex items-center justify-center bg-gray-700 hover:bg-gray-600 rounded-full text-white" disabled={quantity <= 0}>
                                -
                              </button>
                              <span className="text-white w-6 text-center">{quantity}</span>
                              <button
                                onClick={() => ticket.id === 'charity-donation' ? handlePlusClick(ticket) : updateQuantity(ticket, quantity + 1)}
                                className="w-8 h-8 flex items-center justify-center bg-[var(--darkGold)] hover:bg-[var(--darkGold)]/80 rounded-full text-white"
                              >
                                +
                              </button>
                            </div>
                          )}
                        </div>
                        {ticket.id === 'charity-donation' && renderCharityMultipliers(ticket)}
                      </div>
                    );
                  })}
              </div>
            </div>
          </TabPanel>
        </Tabs>
      )}

      {cart.length > 0 && (
        <div className="mt-6 border-t border-[var(--darkGold)] pt-4">
          <div className="flex justify-between items-center">
            <h4 className="text-lg font-medium text-white">Cart Summary</h4>
            <button
              onClick={handleClearCart} // Call the internal clear cart handler
              className="text-sm text-red-500 hover:text-red-800 transition-colors"
            >
              Clear Cart
            </button>
          </div>
          <ul className="space-y-2 mt-2">
            {cart
              .filter(item => item.quantity > 0) // Only display items with quantity > 0
              .map(item => {
              const total = item.ticketType.price * item.quantity;
              return (
                <li key={item.ticketType.id} className="flex justify-between text-sm items-center">
                  <span className="text-gray-300 flex items-center gap-2">
                    <button
                      onClick={() => updateQuantity(item.ticketType, item.quantity - 1)} // Use internal update quantity handler
                      className="w-6 h-6 flex items-center justify-center bg-gray-700 hover:bg-gray-600 rounded-full text-white text-xs" disabled={item.quantity <= 0}>
                      -
                    </button>
                    {item.quantity} x {item.ticketType.name}
                    {(item.selectedDay || item.ticketType.selectedDay) && (
                      <span className="ml-1 text-xs text-[var(--darkGold)]">
                        ({item.selectedDay || item.ticketType.selectedDay})
                      </span>
                    )}
                    {item.ticketType.id.includes('santa-option') && item.santaSlot && (
                      <div className="text-xs text-[var(--darkGold)]">
                        <div>{item.santaSlot.day}, {item.santaSlot.timeRange}</div>
                        {item.santaSlot.numChildren && item.santaSlot.numChildren > 1 && (
                          <div>{item.santaSlot.numChildren} children (€{(item.santaSlot.numChildren - 1) * 6} extra)</div>
                        )}
                      </div>
                    )}
                  </span>
                  <span className="text-[var(--darkGold)]">€{total.toFixed(2)}</span>
                </li>
              );
            })}
            <li className="flex justify-between font-bold border-t border-gray-700 pt-2 mt-2">
              <span className="text-white">Total</span>
              <span className="text-[var(--darkGold)]">
                €{calculateTotal().toFixed(2)} <span className="text-xs font-normal">(incl. 13.5% VAT)</span>
              </span>
            </li>
          </ul>
          <button
            onClick={onContinue}
            className="w-full mt-4 py-2 bg-[var(--darkGold)] text-white font-semibold rounded hover:bg-[var(--darkGold)]/80 transition-colors"
          >
            Continue to Details
          </button>
        </div>
      )}
    </div>
  );
};
export default TicketSelectStep;