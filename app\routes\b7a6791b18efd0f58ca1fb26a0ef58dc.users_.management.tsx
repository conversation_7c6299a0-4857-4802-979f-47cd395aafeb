import { useState, useRef } from 'react';
import { UserList, UserListRef } from '@/app/components/admin/users/UserList';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { Users, UserPlus } from 'lucide-react';
import { CreateUserModal } from '@/app/components/admin/users/CreateUserModal';

export default function UserManagement() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const userListRef = useRef<UserListRef>(null);

  const handleUserCreated = () => {
    // Trigger refresh of user list
    if (userListRef.current) {
      userListRef.current.refreshUsers();
    }
  };

  return (
    <ProtectedRoute requiredRole="super_admin">
      <AdminLayout>
        <div className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-amber-500" />
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">User Management</h1>
              </div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-amber-900 bg-amber-400 hover:bg-amber-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-colors"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Create New User
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Manage admin users and their permissions
            </p>
          </div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-4">
            <div className="bg-white dark:bg-zinc-800 shadow overflow-hidden sm:rounded-lg p-6">
              <UserList ref={userListRef} />
            </div>
          </div>
        </div>

        {/* Create User Modal */}
        {showCreateModal &&
          <CreateUserModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onUserCreated={handleUserCreated}
          />
        }
      </AdminLayout>
    </ProtectedRoute>
  );
};