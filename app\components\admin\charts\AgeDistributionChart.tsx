import { ResponsiveLine } from '@nivo/line';
import { useMemo } from 'react';
import { Box } from '@chakra-ui/react';

interface DataPoint {
  x: string;
  y: number;
}

interface AgeData {
  id: string;
  data: DataPoint[];
  color?: string;
}

interface AgeDistributionChartProps {
  data: Array<{
    age: string;
    count: number;
  }>;
  height?: number;
  colors?: string[];
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

export function AgeDistributionChart({ 
  data, 
  height = 400,
  colors = ['#f59e0b', '#3b82f6', '#10b981', '#8b5cf6'],
  margin = { top: 20, right: 30, bottom: 80, left: 60 }
}: AgeDistributionChartProps) {
  // Theme configuration
  const theme = {
    textColor: '#9CA3AF',
    axis: {
      domain: {
        line: {
          stroke: '#374151',
          strokeWidth: 1
        }
      },
      ticks: {
        line: {
          stroke: '#374151',
          strokeWidth: 1
        },
        text: {
          fill: '#9CA3AF',
          fontSize: 11
        }
      },
      legend: {
        text: {
          fill: '#9CA3AF',
          fontSize: 12,
          fontWeight: 500
        }
      }
    },
    grid: {
      line: {
        stroke: '#374151',
        strokeWidth: 1,
        strokeDasharray: '2 4'
      }
    },
    tooltip: {
      container: {
        background: '#1F2937',
        color: '#F3F4F6',
        border: '1px solid #4B5563',
        borderRadius: '6px',
        padding: '8px 12px',
        fontSize: '13px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
      }
    },
    crosshair: {
      line: {
        stroke: '#F59E0B',
        strokeWidth: 1,
        strokeOpacity: 0.5
      }
    }
  };

  // Process data for the area chart
  const chartData = useMemo<AgeData[]>(() => {
    if (!data || data.length === 0) return [];

    // Sort data by age range
    const sortedData = [...data].sort((a, b) => {
      const ageA = parseInt(a.age.split('-')[0] || a.age);
      const ageB = parseInt(b.age.split('-')[0] || b.age);
      return ageA - ageB;
    });

    // Format data for area chart
    return [{
      id: 'Users',
      color: colors[0],
      data: sortedData.map(item => ({
        x: item.age,
        y: item.count
      }))
    }];
  }, [data, colors]);

  if (!data || data.length === 0) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height="100%">
        <Box color="gray.400" fontSize="sm">No age data available</Box>
      </Box>
    );
  }

  // Format count for tooltip
  const formatCount = (count: number) => {
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k`;
    return count.toString();
  };

  // Find max value for y-scale
  const maxValue = Math.max(...data.map(item => item.count));
  const yMax = Math.ceil(maxValue * 1.2); // Add 20% padding

  return (
    <Box width="100%" height={height} position="relative">
      <ResponsiveLine
        data={chartData}
        margin={margin}
        xScale={{ type: 'point' }}
        yScale={{
          type: 'linear',
          min: 0,
          max: yMax,
          stacked: false,
          reverse: false,
        }}
        curve="monotoneX"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Age Group',
          legendOffset: 36,
          legendPosition: 'middle',
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          tickValues: 5,
          format: (value) => formatCount(Number(value)),
          legend: 'Number of Users',
          legendOffset: -50,
          legendPosition: 'middle',
        }}
        enableGridX={false}
        enableGridY={true}
        colors={({ color }) => color || colors[0]}
        lineWidth={2}
        pointSize={6}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={2}
        pointBorderColor={{ from: 'serieColor' }}
        pointLabelYOffset={-12}
        enableArea={true}
        areaOpacity={0.25}
        useMesh={true}
        theme={theme}
        tooltip={({ point }) => {
          return (
            <Box 
              bg="gray.800"
              border="1px"
              borderColor="gray.600"
              borderRadius="md"
              p={2}
              boxShadow="lg"
            >
              <Box fontWeight="medium" color={colors[0]} mb={1}>
                {point.data.xFormatted}
              </Box>
              <Box fontSize="sm" display="flex" alignItems="center">
                <Box 
                  w={3} 
                  h={3} 
                  bg={colors[0]} 
                  borderRadius="sm" 
                  mr={2}
                />
                <Box as="span" mr={2}>
                  {formatCount(Number(point.data.y))} users
                </Box>
              </Box>
            </Box>
          );
        }}
        animate={true}
        motionConfig="gentle"
      />
    </Box>
  );
}
