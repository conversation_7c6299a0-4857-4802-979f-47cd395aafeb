import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';
import { render } from '@react-email/render';
import { VendorStandConfirmationEmail } from '@/app/components/Email/VendorStandConfirmationEmail';
import { AdminStandNotificationEmail } from '@/app/components/Email/AdminStandNotificationEmail';
import { standPositions } from '@/data/standPositions';
import { env } from '@/app/utils/env.server';

// Initialize Supabase client
const supabase = createClient(
  env.supabase.url,
  env.supabase.key
);

// Initialize Resend client
const resend = new Resend(env.resendApiKey);

// Update the request body interface to include discount info
interface RequestBody {
  standNumber: number;
  businessName: string;
  contactName: string;
  email: string;
  phone: string;
  addons: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  totalPrice: number;
  discountApplied?: boolean;
  discountPercentage?: number;
  originalPrice?: number;
  price?: number;
}

export async function action({ request, params }: ActionFunctionArgs) {
  try {
    const data = await request.json();
    const {
      standNumber,
      businessName,
      contactName,
      email,
      phone,
      addons,
      totalPrice,
      discountApplied = false,
      discountPercentage = 0,
      originalPrice = 0,
      price = 0
    } = data as RequestBody;

    // Validate required fields
    if (!standNumber || !businessName || !contactName || !email || !phone) {
      return json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if stand is already booked
    const { data: existingBooking, error: bookingCheckError } = await supabase
      .from('VendorStands')
      .select('*')
      .eq('stand_number', standNumber)
      .single();

    if (bookingCheckError && bookingCheckError.code !== 'PGRST116') {
      throw bookingCheckError;
    }

    if (existingBooking) {
      return json(
        { error: 'Stand is already booked' },
        { status: 400 }
      );
    }

    // Update or create booking record with confirmed status
    const { error: upsertError } = await supabase
      .from('VendorStands')
      .upsert({
        stand_number: standNumber,
        business_name: businessName,
        contact_name: contactName,
        email: email,
        phone: phone,
        price: totalPrice,
        addons: addons || [],
        discount_applied: discountApplied,
        discount_percentage: discountApplied ? discountPercentage : null,
        original_price: discountApplied ? originalPrice : null,
        status: 'confirmed',
        payment_status: 'paid' // Set to paid since this is called after successful payment
      }, {
        onConflict: 'stand_number'
      });

    if (upsertError) throw upsertError;

    // Add vendor to newsletter subscribers if not already subscribed
    const { data: existingSubscriber } = await supabase
      .from('NewsletterSubscribers')
      .select('*')
      .eq('email', email.toLowerCase())
      .single();

    if (!existingSubscriber) {
      // Add new subscriber
      await supabase
        .from('NewsletterSubscribers')
        .insert([
          {
            email: email.toLowerCase(),
            subscribed_at: new Date().toISOString(),
            is_subscribed: true
          },
        ]);
    }

    // Find the stand in standPositions to get isArtist value and price
    const standData = standPositions.find(stand => stand.id === standNumber);
    const isArtist = standData?.isArtist || false;
    const standPrice = standData?.price || 0;

    // Format vendor confirmation email
    const vendorEmailHtml = await render(
      VendorStandConfirmationEmail({
        businessName,
        contactName,
        email,
        phone,
        standNumber,
        totalPrice,
        addons,
        isArtist,
        price: standPrice,
        discountApplied,
        discountPercentage,
        originalPrice
      })
    ) as string;

    // Format admin notification email
    const adminEmailHtml = await render(
      AdminStandNotificationEmail({
        businessName,
        contactName,
        email,
        phone,
        standNumber,
        totalPrice,
        addons
      })
    ) as string;

    // Send vendor confirmation email
    const { data: vendorEmailData, error: vendorEmailError } = await resend.emails.send({
      from: env.emailFrom,
      to: email,
      subject: "The Convention Before Christmas - Stand Booking Confirmation",
      html: vendorEmailHtml,
    });

    if (vendorEmailError) {
      throw vendorEmailError;
    }

    console.log("Vendor confirmation email sent successfully:", {
      to: email,
      messageId: vendorEmailData?.id
    });

    // Send admin notification email to multiple recipients
    const adminRecipients = [env.admin.email, env.admin.secondaryEmail].filter(Boolean);

    const { data: adminEmailData, error: adminEmailError } = await resend.emails.send({
      from: env.emailFrom,
      to: adminRecipients,
      subject: `New Vendor Stand Booking - Stand #${standNumber}`,
      html: adminEmailHtml,
    });

    if (adminEmailError) {
      console.error("Error sending admin notification email:", adminEmailError);
      // Don't throw here to ensure vendor gets success response
    } else {
      console.log("Admin notification email sent successfully:", {
        to: adminRecipients,
        messageId: adminEmailData?.id
      });
    }

    return json({
      success: true,
      message: 'Stand booked successfully',
      standNumber
    });

  } catch (error) {
    console.error('Error processing vendor stand booking:', error);
    return json(
      { error: 'Failed to process booking', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
};

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { data: bookedStands, error } = await supabase
      .from('VendorStands')
      .select('stand_number');

    if (error) throw error;

    return json(bookedStands);
  } catch (error) {
    console.error('Error fetching booked stands:', error);
    return json(
      { error: 'Failed to fetch booked stands' },
      { status: 500 }
    );
  }
};