import { useAppSelector, useAppDispatch } from '@/app/store/hooks';
import { selectConnectionStatus, selectIsConnected } from '@/app/store/slices/realtimeSlice';
import { realtimeManager } from '@/app/utils/realtimeManager';
import { Wifi, WifiOff, AlertCircle, RefreshCw } from 'lucide-react';
import { useState } from 'react';

interface RealtimeConnectionStatusProps {
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'minimal' | 'detailed' | 'badge';
}

export const RealtimeConnectionStatus = ({
  showLabel = true,
  size = 'md',
  variant = 'minimal'
}: RealtimeConnectionStatusProps) => {
  const connectionStatus = useAppSelector(selectConnectionStatus);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const handleReconnect = async () => {
    setIsReconnecting(true);
    try {
      await realtimeManager.reconnect();
    } catch (error) {
      console.error('Manual reconnection failed:', error);
    } finally {
      setIsReconnecting(false);
    }
  };

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connected':
        return {
          icon: Wifi,
          color: 'text-green-400',
          bgColor: 'bg-green-400/10',
          borderColor: 'border-green-400/30',
          label: 'Live Updates',
          description: 'Real-time updates are active'
        };
      case 'connecting':
        return {
          icon: RefreshCw,
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-400/10',
          borderColor: 'border-yellow-400/30',
          label: 'Connecting...',
          description: 'Establishing real-time connection'
        };
      case 'error':
        return {
          icon: AlertCircle,
          color: 'text-red-400',
          bgColor: 'bg-red-400/10',
          borderColor: 'border-red-400/30',
          label: 'Connection Error',
          description: 'Real-time updates unavailable'
        };
      default:
        return {
          icon: WifiOff,
          color: 'text-gray-400',
          bgColor: 'bg-gray-400/10',
          borderColor: 'border-gray-400/30',
          label: 'Offline',
          description: 'Real-time updates disabled'
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          icon: 'w-3 h-3',
          text: 'text-xs',
          padding: 'px-2 py-1'
        };
      case 'lg':
        return {
          icon: 'w-5 h-5',
          text: 'text-sm',
          padding: 'px-3 py-2'
        };
      default:
        return {
          icon: 'w-4 h-4',
          text: 'text-xs',
          padding: 'px-2 py-1'
        };
    }
  };

  const config = getStatusConfig();
  const sizeClasses = getSizeClasses();
  const IconComponent = config.icon;

  if (variant === 'minimal') {
    return (
      <div className="flex items-center space-x-2">
        <IconComponent
          className={`${sizeClasses.icon} ${config.color} ${
            connectionStatus === 'connecting' || isReconnecting ? 'animate-spin' : ''
          }`}
        />
        {showLabel && (
          <span className={`${sizeClasses.text} font-medium ${config.color}`}>
            {config.label}
          </span>
        )}
      </div>
    );
  }

  if (variant === 'badge') {
    return (
      <div className={`
        inline-flex items-center space-x-2 rounded-full border
        ${config.bgColor} ${config.borderColor} ${sizeClasses.padding}
      `}>
        <IconComponent
          className={`${sizeClasses.icon} ${config.color} ${
            connectionStatus === 'connecting' || isReconnecting ? 'animate-spin' : ''
          }`}
        />
        {showLabel && (
          <span className={`${sizeClasses.text} font-medium ${config.color}`}>
            {config.label}
          </span>
        )}
      </div>
    );
  }

  // Detailed variant
  return (
    <div className={`
      flex items-center justify-between p-3 rounded-lg border
      ${config.bgColor} ${config.borderColor}
    `}>
      <div className="flex items-center space-x-3">
        <IconComponent
          className={`w-5 h-5 ${config.color} ${
            connectionStatus === 'connecting' || isReconnecting ? 'animate-spin' : ''
          }`}
        />
        <div>
          <div className={`text-sm font-medium ${config.color}`}>
            {config.label}
          </div>
          <div className="text-xs text-gray-400">
            {config.description}
          </div>
        </div>
      </div>

      {(connectionStatus === 'error' || connectionStatus === 'disconnected') && (
        <button
          onClick={handleReconnect}
          disabled={isReconnecting}
          className={`
            px-3 py-1 text-xs font-medium rounded-md transition-colors
            ${config.color} hover:bg-white/10 disabled:opacity-50
          `}
        >
          {isReconnecting ? 'Reconnecting...' : 'Reconnect'}
        </button>
      )}
    </div>
  );
};
export default RealtimeConnectionStatus;