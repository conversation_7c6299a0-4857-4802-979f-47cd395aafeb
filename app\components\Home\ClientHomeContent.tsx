"use client";

import React, { useState, useEffect } from "react";
//import Snowfall from "react-snowfall";
import Hero from "./Hero";
import Sponsors from "./Sponsors";
import Traders from "./Traders";
import Welcome from "./Welcome";
import Celebs from "./Celebs";
import Location from "./Location";
import WhatsOn from "./WhatsOn";
import Newsletter from "../Newsletter";
//import CurtainModal from "../ui/curtain-modal";
import { useLocation } from '@remix-run/react';

export default function ClientHomeContent() {
  const [showCurtain, setShowCurtain] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const location = useLocation();

  // Handle curtain animation complete
  const handleCurtainComplete = () => {
    // Save the current time to localStorage
    localStorage.setItem('curtainLastShown', Date.now().toString());

    setShowCurtain(false);
    setContentVisible(true);
  };

  useEffect(() => {
    /* // Only show curtain on home page
    if (pathname === '/') {
      // Check if we should show the curtain (only on client-side)
      const shouldShowCurtain = () => {
        // Get the last time the curtain was shown
        const lastShown = localStorage.getItem('curtainLastShown');

        if (!lastShown) {
          // First visit, show the curtain
          return true;
        }

        const lastShownTime = parseInt(lastShown, 10);
        const currentTime = Date.now();

        // Check if 15 minutes (900000 ms) have passed
        return currentTime - lastShownTime > 900000;
      };

      const showCurtainValue = shouldShowCurtain();
      setShowCurtain(showCurtainValue);

      // If we're not showing the curtain, make content visible immediately
      if (!showCurtainValue) {
        setContentVisible(true);
      }
    } else { */
      // Not on home page, don't show curtain and make content visible
      setShowCurtain(false);
      setContentVisible(true);
    //}
  }, [location.hash]);

  // Effect to scroll to hash on page load or hash change
  useEffect(() => {
    if (location.hash) {
      const element = document.querySelector(location.hash);
      if (element) {
        // Use requestAnimationFrame to wait for potential rendering updates
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            element.scrollIntoView({ behavior: "smooth" });
          });
        });
      }
    } else {
        // If no hash and not the very first load, maybe scroll to top?
        // This part is optional, depending on desired behavior when arriving without a hash.
        // For now, let's just ensure no erroneous scrolling.
    }
  }, [location.hash]); // Rerun effect when location hash changes

  return (
    <div className="relative w-full h-full">
      {/* showCurtain && <CurtainModal onComplete={handleCurtainComplete} /> */}

      <div className="fixed inset-0 z-50 pointer-events-none">
        {/* <Snowfall
              snowflakeCount={200}
              color="#fff"
            /> */}
      </div>

      {/* <div style={{ opacity: contentVisible ? 1 : 0, transition: 'opacity 1s ease-in-out' }}> */}
      <div>
        <Hero />
        <Welcome />
        <Sponsors />
        <div className="bg-[#1c1c1c]">
          <WhatsOn />
        </div>
        <Traders />
        <div className="bg-[#1c1c1c]">
          <Celebs />
        </div>
        <Newsletter />
        <div className="bg-[#1c1c1c]">
          <Location />
        </div>
      </div>
    </div>
  );
};