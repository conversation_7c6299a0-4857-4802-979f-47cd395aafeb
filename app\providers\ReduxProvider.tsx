'use client';

import { useMemo } from 'react';
import { Provider } from 'react-redux';
import { createStore, type RootState } from '@/app/store';

interface ReduxProviderProps {
  children: React.ReactNode;
  initialState?: Partial<RootState>;
}

export function ReduxProvider({ children, initialState }: ReduxProviderProps) {
  // Create the store instance on the client side only
  const store = useMemo(
    () => createStore(initialState || {}),
    [initialState]
  );

  return <Provider store={store}>{children}</Provider>;
}
