/**
 * Client-side environment variables
 * Only expose variables that are safe to be exposed to the client
 */

// Safely access window.ENV with fallbacks
declare const window: Window & { ENV: Record<string, string | undefined> };

// Helper function to safely get environment variables with defaults
const getEnv = (key: string, defaultValue: string = ''): string => {
  if (typeof window === 'undefined') return defaultValue;
  return window.ENV?.[key] ?? defaultValue;
};

export const env = {
  // Supabase
  supabase: {
    url: getEnv('SUPABASE_URL', ''),
    key: getEnv('SUPABASE_KEY', ''),
  },

  // App
  nodeEnv: getEnv('NODE_ENV', 'development') as 'development' | 'production' | 'test',
  siteUrl: getEnv('SITE_URL', ''),
  stripePublishableKey: getEnv('STRIPE_PUBLISHABLE_KEY', ''),

  // Convenience flags
  get isProduction() {
    return this.nodeEnv === 'production';
  },
  get isDevelopment() {
    return this.nodeEnv === 'development';
  },
  get isTest() {
    return this.nodeEnv === 'test';
  },
} as const;

// Type exports
type Env = typeof env;
type SupabaseConfig = Env['supabase'];

export type { Env, SupabaseConfig };

// Validate required environment variables in development
if (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined') {
  const requiredVars = ['SUPABASE_URL', 'SUPABASE_KEY'];
  const missingVars = requiredVars.filter(varName => !window.ENV?.[varName]);

  if (missingVars.length > 0) {
    console.warn(
      'Missing required environment variables:\n  - ' +
      missingVars.join('\n  - ') +
      '\n\nPlease check your environment configuration.'
    );
  }
}