import React, { useEffect, useState, useRef } from 'react';
import Tippy from '@tippyjs/react';
import styles from './StandIndicators.module.css';

interface StandPosition {
  id: number;
  x: number;
  y: number;
  price: number;
  isArtist: boolean;
  isAvailable: boolean;
  tableSize: string;
  reserved: boolean;
}

interface StandIndicatorProps {
  onStandClick: (standNumber: number) => void;
  stands: StandPosition[];
}

export default function StandIndicators({ onStandClick, stands }: StandIndicatorProps) {
  const [mounted, setMounted] = useState(false);
  const [scale, setScale] = useState({ scale: 1, offsetX: 0, offsetY: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // Natural dimensions of the image
  const naturalWidth = 4963;
  const naturalHeight = 3505;

  const getVisiblePosition = (x: number, y: number) => {
    const { scale: s, offsetX, offsetY } = scale;
    return {
      x: (x * s) - offsetX,
      y: (y * s) - offsetY,
    };
  };

  useEffect(() => {
    // This effect runs only on the client after initial render
    setMounted(true);

    const updateScale = () => {
      if (containerRef.current && imageRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const containerHeight = containerRef.current.clientHeight;

        // Calculate scale factor using object-cover logic:
        const scaleFactor = Math.max(containerWidth / naturalWidth, containerHeight / naturalHeight);

        // Rendered image dimensions
        const displayedWidth = naturalWidth * scaleFactor;
        const displayedHeight = naturalHeight * scaleFactor;

        // Calculate cropping offsets (if image overflows container)
        const offsetX = (displayedWidth - containerWidth) / 2;
        const offsetY = (displayedHeight - containerHeight) / 2;

        setScale({ scale: scaleFactor, offsetX, offsetY });
      }
    };

    updateScale();

    const resizeObserver = new ResizeObserver(updateScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    window.addEventListener('resize', updateScale);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', updateScale);
    };
  }, []);

  return (
    <div ref={containerRef} className="relative w-full h-full">
      <img
        ref={imageRef}
        src="/images/floor-plan.webp"
        alt="Floor Plan"
        className="w-full h-full object-cover" // full coverage of the container
      />

      <div className="absolute top-0 left-0 w-full h-full">
        {/* Render Tippy only on the client */}
        {mounted && stands.map((stand) => {
          const { x, y } = getVisiblePosition(stand.x, stand.y);
          return (
            <Tippy
              key={stand.id}
              content={`${(!stand.reserved && stand.isAvailable) ? `Book ${stand.isArtist ? 'Artist' : 'Stand'} #${stand.id}` : 'Unavailable'}`}
              placement="top"
              theme="dark"
            >
              <span
                className={`${styles.pulsingIndicator} ${(!stand.reserved && stand.isAvailable) ? styles.available : styles.unavailable}`}
                style={{
                  position: 'absolute',
                  left: `${x}px`,
                  top: `${y}px`,
                  transform: 'translate(-50%, -50%)',
                  cursor: (!stand.reserved && stand.isAvailable) ? 'pointer' : 'not-allowed',
                }}
                onClick={() => (!stand.reserved && stand.isAvailable) && onStandClick(stand.id)}
                role="button"
                aria-label={`Select stand ${stand.id}`}
              />
            </Tippy>
          );
        })}
      </div>
    </div>
  );
};