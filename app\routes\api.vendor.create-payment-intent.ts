import Stripe from 'stripe';
import { json, type ActionFunctionArgs } from "@remix-run/node";
import { createClient } from '@supabase/supabase-js';
import { env } from '@/app/utils/env.server';

const stripe = new Stripe(env.stripeSecretKey, {
  apiVersion: '2025-05-28.basil',
});

const supabase = createClient(
  env.supabase.url,
  env.supabase.key
);

export async function action({ request }: ActionFunctionArgs) {
  try {
    const data = await request.json();
    const { amount, standNumber, businessName, contactName, email, addons } = data;

    if (!amount || !standNumber || !businessName || !contactName || !email) {
      return json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create metadata object with basic info
    const metadata: Record<string, string> = {
      type: 'vendor_stand',
      standNumber: standNumber.toString(),
      businessName,
      contactName,
      email,
    };

    // Add addons to metadata if present
    if (addons && addons.length > 0) {
      metadata.hasAddons = 'true';
      // Add up to 10 addons (Stripe metadata limit)
      addons.slice(0, 10).forEach((addon: any, index: number) => {
        metadata[`addon_${index}_id`] = addon.id;
        metadata[`addon_${index}_name`] = addon.name;
        metadata[`addon_${index}_quantity`] = addon.quantity.toString();
        metadata[`addon_${index}_price`] = addon.price.toString();
      });
    }

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents and ensure it's an integer
      currency: 'eur',
      automatic_payment_methods: {
        enabled: true,
      },
      metadata,
    });

    if (!paymentIntent.client_secret) {
      throw new Error('Failed to create payment intent');
    }

    // Create vendor stand record with pending status
    const { error: vendorError } = await supabase
      .from('VendorStands')
      .insert({
        stand_number: standNumber,
        business_name: businessName,
        contact_name: contactName,
        email: email,
        price: amount,
        addons: addons || [],
        payment_intent_id: paymentIntent.id,
        payment_status: 'pending'
      });

    if (vendorError) {
      console.error('Error creating vendor stand record:', vendorError);
      // Don't fail the payment intent creation, but log the error
    }

    return json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
}