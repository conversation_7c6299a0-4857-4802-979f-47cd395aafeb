import React, { useEffect, useRef } from 'react';

declare global {
  interface Window {
    run: () => void;
  }
}

const Loader: React.FC<{ onLoadComplete?: () => void }> = ({ onLoadComplete }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    // Initialize the loader when the component mounts
    const script = document.createElement('script');
    script.src = '/tree-loader.js';
    script.async = true;
    script.onload = () => {
      if (typeof window.run === 'function' && typeof window !== undefined) {
        window.run();
      }
      // Call onLoadComplete after a delay to allow the animation to play
      setTimeout(() => {
        onLoadComplete?.();
      }, 1000); // Adjust timing as needed
    };
    document.body.appendChild(script);

    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [onLoadComplete]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#151515]">
      <canvas
        ref={canvasRef}
        id="scene"
        width="500"
        height="500"
        className="bg-[#151515]"
      />
    </div>
  );
};
export default Loader;