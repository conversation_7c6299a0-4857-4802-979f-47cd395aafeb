'use client'
import { useEffect, ReactNode, useState } from "react";
import dynamic from 'next/dynamic';

// Import styles only when AOS is used
import 'aos/dist/aos.css';

interface AosComponentProps {
  children: ReactNode;
}

const Aoscompo = ({ children }: AosComponentProps) => {
  const [aosLoaded, setAosLoaded] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const loadAOS = async () => {
      try {
        const AOS = (await import('aos')).default;
        AOS.init({
          duration: 600, // Reduced from 800
          once: true,
          disable: window.innerWidth < 1024, // Disable on more devices
          startEvent: 'DOMContentLoaded', // Start earlier
        });
        setAosLoaded(true);
      } catch (error) {
        console.error("Failed to load AOS:", error);
      }
    };

    // Delay AOS initialization to prioritize critical content
    const timer = setTimeout(loadAOS, 1000);
    return () => clearTimeout(timer);
  }, []);

  return <>{children}</>;
}

export default Aoscompo;
