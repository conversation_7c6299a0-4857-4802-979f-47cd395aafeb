import React from 'react';

interface ImageCarouselProps {
  images: string[];
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images }) => {
  // Duplicate images for continuous loop effect
  const duplicatedImages = [...images, ...images];

  return (
    <>
      <style>
        {`
          @keyframes scroll-left {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%); /* Scrolls exactly one full set of original images */
            }
          }

          .animate-scroll {
            animation: scroll-left 30s linear infinite; /* Adjust duration as needed */
          }
        `}
      </style>
      <div className="overflow-hidden w-full">
        <div className="flex animate-scroll" style={{ width: `${duplicatedImages.length * 16}rem` }}> {/* Assuming each image is w-64 (16rem) + mr-4 (1rem). Adjust as needed. This width is a rough estimate and might need fine-tuning. */}
          {duplicatedImages.map((image, index) => (
            <img
              key={index}
              src={image}
              alt={`Carousel Image ${index + 1}`}
              className="inline-block flex-shrink-0 w-64 h-56 object-cover rounded-lg shadow-lg mr-4"
            />
          ))}
        </div>
      </div>
    </>
  );
};
export default ImageCarousel;