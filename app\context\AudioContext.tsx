"use client";

import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
  ReactNode,
} from "react";

interface AudioContextType {
  isMuted: boolean;
  toggleMute: () => void;
  playBackgroundMusic: () => void;
  audioRef: React.RefObject<HTMLAudioElement>;
  hasPlayedBefore: boolean;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

export function AudioProvider({ children }: { children: ReactNode }) {
  // ─────────────────────────────────────────────────────────────────────────
  // 1) State & refs
  // ─────────────────────────────────────────────────────────────────────────
  const [isMuted, setIsMuted] = useState(false);
  const [hasPlayedBefore, setHasPlayedBefore] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // ─────────────────────────────────────────────────────────────────────────
  // 2) Keep the <audio> element in sync with isMuted
  // ─────────────────────────────────────────────────────────────────────────
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.muted = isMuted;
      // Explicitly set volume to 0 if muted, otherwise restore to a base volume (e.g., 0.6)
      audioRef.current.volume = isMuted ? 0 : 0.6;
    }
  }, [isMuted]);

  // ─────────────────────────────────────────────────────────────────────────
  // 3) "Play on first click" but skip clicks on anything marked data-skip-play
  // ─────────────────────────────────────────────────────────────────────────
  useEffect(() => {
    const handleFirstUserClick = (e: MouseEvent) => {
      // If we've already played once, do nothing.
      if (hasPlayedBefore) return;

      // If the click came from an element or inside something with data-skip-play, skip auto-play.
      const clickedElement = e.target as HTMLElement;
      if (clickedElement.closest("[data-skip-play]")) {
        return;
      }

      // Otherwise, start the music.
      if (audioRef.current) {
        const audioEl = audioRef.current;
        // Set volume based on muted flag
        audioEl.volume = isMuted ? 0 : 0.6;
        audioEl
          .play()
          .then(() => {
            setHasPlayedBefore(true);
          })
          .catch((err) => {
            console.error("Audio playback failed:", err);
          });

        // If this click wasn't on a native form element or link, prevent default to avoid page jump/scroll
        const tagName = clickedElement.tagName.toLowerCase();
        const interactive = ["input", "textarea", "select", "button", "a"].includes(tagName);
        if (!interactive && !clickedElement.closest("a") && !clickedElement.closest("button")) {
          e.preventDefault();
        }
      }
    };

    document.addEventListener("mousedown", handleFirstUserClick, { passive: false });
    return () => {
      document.removeEventListener("mousedown", handleFirstUserClick);
    };
  }, [hasPlayedBefore, isMuted]);

  // ─────────────────────────────────────────────────────────────────────────
  // 4) toggleMute flips React state; the useEffect above keeps audioRef.current.muted in sync.
  // ─────────────────────────────────────────────────────────────────────────
  const toggleMute = () => {
    setIsMuted((prev) => !prev);
  };

  // ─────────────────────────────────────────────────────────────────────────
  // 5) Expose playBackgroundMusic if you ever want to play manually:
  // ─────────────────────────────────────────────────────────────────────────
  const playBackgroundMusic = () => {
    if (audioRef.current && audioRef.current.paused) {
      const audioEl = audioRef.current;
      audioEl.volume = isMuted ? 0 : 0.6;
      audioEl
        .play()
        .then(() => {
          setHasPlayedBefore(true);
        })
        .catch((err) => {
          console.error("Audio playback failed:", err);
        });
    }
  };

  return (
    <AudioContext.Provider
      value={{
        isMuted,
        toggleMute,
        playBackgroundMusic,
        audioRef: audioRef as React.RefObject<HTMLAudioElement>,
        hasPlayedBefore,
      }}
    >
      {/* One and only one <audio> in the entire app */}
      <audio ref={audioRef} loop preload="metadata" src="/media/background.mp3" />
      {children}
    </AudioContext.Provider>
  );
}

export function useAudio() {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error("useAudio must be used within an AudioProvider");
  }
  return context;
}