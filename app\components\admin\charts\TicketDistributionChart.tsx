import { useMemo, useState } from 'react';
import { ResponsivePie } from '@nivo/pie';

interface TicketType {
  type: string;
  count: number;
  label?: string;
}

interface TicketDistributionChartProps {
  data: TicketType[];
  title?: string;
  height?: number;
}

export function TicketDistributionChart({
  data,
  title = 'Ticket Sales Distribution',
  height = 360,
}: TicketDistributionChartProps) {
  const [activeId, setActiveId] = useState<string | null>(null);

  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + item.count, 0);

  // Color scheme matching the original
  const colors = [
    '#b91c1c', // red
    '#1d4ed8', // blue
    '#047857', // green
    '#b45309', // amber
    '#6d28d9', // purple
    '#be185d', // pink
    '#0e7490', // cyan
    '#4d7c0f', // lime
  ];

  // Format data for Nivo Pie
  const chartData = useMemo(() => {
    return data.map((item, index) => ({
      id: item.type,
      label: item.label || item.type,
      value: item.count,
      color: colors[index % colors.length],
      count: item.count,
      percentage: total > 0 ? (item.count / total) * 100 : 0,
    }));
  }, [data, total]);

  // Sort segments by count (descending) for the legend
  const sortedData = useMemo(() =>
    [...chartData].sort((a, b) => b.value - a.value)
  , [chartData]);

  // Format friendly ticket type names
  const getTicketLabel = (type: string) => {
    const item = data.find((d) => d.type === type);
    if (item?.label) return item.label;

    return type
      .replace(/-/g, ' ')
      .replace(/(\d+)day/g, '$1-day')
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="w-full h-full flex flex-col">
      {title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-white">{title}</h3>
        </div>
      )}

      <div className="flex flex-col md:flex-row gap-4 h-full">
        {/* Pie Chart */}
        <div className="relative flex-1" style={{ height: `${height}px` }}>
          <ResponsivePie
            data={chartData}
            margin={{ top: 10, right: 10, bottom: 10, left: 10 }}
            innerRadius={0.5}
            padAngle={0.7}
            cornerRadius={3}
            activeOuterRadiusOffset={8}
            colors={({ data }) => data.color}
            borderWidth={1}
            borderColor={{
              from: 'color',
              modifiers: [['darker', 0.2]]
            }}
            enableArcLinkLabels={false}
            arcLinkLabelsSkipAngle={10}
            arcLinkLabelsTextColor="#9ca3af"
            arcLinkLabelsThickness={2}
            arcLinkLabelsColor={{ from: 'color' }}
            arcLabelsSkipAngle={10}
            arcLabelsTextColor="#ffffff"
            arcLabel={d => `${d.data.percentage.toFixed(1)}%`}
            animate={true}
            motionConfig="gentle"
            onMouseEnter={(datum) => setActiveId(datum.id as string)}
            onMouseLeave={() => setActiveId(null)}
            tooltip={({ datum }) => (
              <div className="bg-zinc-800 p-2 rounded border border-zinc-700 shadow-lg">
                <div className="font-semibold text-gray-200">
                  {getTicketLabel(datum.id as string)}
                </div>
                <div className="flex items-center mt-1">
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: datum.color }}
                  />
                  <span className="text-sm text-gray-300">
                    {datum.value} tickets ({datum.data.percentage.toFixed(1)}%)
                  </span>
                </div>
              </div>
            )}
            theme={{
              tooltip: {
                container: {
                  background: '#1f2937',
                  color: '#f3f4f6',
                  fontSize: '12px',
                  borderRadius: '4px',
                  boxShadow: '0 3px 9px rgba(0, 0, 0, 0.5)'
                }
              },
              labels: {
                text: {
                  fill: '#f3f4f6',
                  fontSize: '11px',
                  fontWeight: 500
                }
              }
            }}
          />
        </div>

        {/* Legend */}
        <div className="w-full md:w-64 overflow-y-auto pr-2" style={{ maxHeight: `${height}px` }}>
          {sortedData.map((item) => (
            <div
              key={item.id}
              className={`flex items-center p-2 rounded-md mb-1 transition-colors ${
                activeId === item.id ? 'bg-zinc-700/50' : 'hover:bg-zinc-700/30'
              }`}
              onMouseEnter={() => setActiveId(item.id as string)}
              onMouseLeave={() => setActiveId(null)}
              style={{ cursor: 'pointer' }}
            >
              <div
                className="w-4 h-4 rounded-full mr-3 flex-shrink-0"
                style={{ backgroundColor: item.color }}
              />
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-baseline">
                  <span className="text-sm font-medium text-gray-200 truncate">
                    {getTicketLabel(item.id as string)}
                  </span>
                  <span className="text-xs text-gray-400 ml-2 whitespace-nowrap">
                    {item.count}
                  </span>
                </div>
                <div className="w-full bg-zinc-700 rounded-full h-1.5 mt-1">
                  <div
                    className="h-1.5 rounded-full"
                    style={{
                      width: `${item?.percentage}%`,
                      backgroundColor: item.color
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};