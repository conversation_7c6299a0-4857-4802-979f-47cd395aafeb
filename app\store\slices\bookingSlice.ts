import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

// Define the cart item type
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  // Add other cart item properties as needed
}

// Define the order details type
export interface OrderDetails {
  id?: string;
  status?: string;
  // Add other order details properties as needed
}

// Define the booking state interface
export interface BookingState {
  cart: CartItem[];
  orderDetails: OrderDetails | null;
  loading: boolean;
  error: string | null;
}

// Initial state
export const initialState: BookingState = {
  cart: [],
  orderDetails: null,
  loading: false,
  error: null,
};

// Create the slice
const bookingSlice = createSlice({
  name: 'booking',
  initialState,
  reducers: {
    // Add item to cart
    addToCart(state, action: PayloadAction<Omit<CartItem, 'quantity'>>) {
      const existingItem = state.cart.find(item => item.id === action.payload.id);
      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        state.cart.push({ ...action.payload, quantity: 1 });
      }
    },

    // Update cart item quantity
    updateCartItemQuantity(
      state,
      action: PayloadAction<{ id: string; quantity: number }>
    ) {
      const { id, quantity } = action.payload;
      const item = state.cart.find(item => item.id === id);
      if (item) {
        if (quantity <= 0) {
          state.cart = state.cart.filter(item => item.id !== id);
        } else {
          item.quantity = quantity;
        }
      }
    },

    // Remove item from cart
    removeFromCart(state, action: PayloadAction<string>) {
      state.cart = state.cart.filter(item => item.id !== action.payload);
    },

    // Clear cart
    clearCart(state) {
      state.cart = [];
    },

    // Set order details
    setOrderDetails(state, action: PayloadAction<OrderDetails>) {
      state.orderDetails = action.payload;
    },

    // Set loading state
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },

    // Set error
    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },
  },
});

// Export actions
export const {
  addToCart,
  updateCartItemQuantity,
  removeFromCart,
  clearCart,
  setOrderDetails,
  setLoading,
  setError,
} = bookingSlice.actions;

// Selectors
export const selectCart = (state: RootState) => state.booking.cart;
export const selectOrderDetails = (state: RootState) => state.booking.orderDetails;
export const selectIsLoading = (state: RootState) => state.booking.loading;
export const selectError = (state: RootState) => state.booking.error;

export default bookingSlice.reducer;
