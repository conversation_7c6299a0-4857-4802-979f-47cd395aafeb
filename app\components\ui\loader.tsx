'use client';

import React, { useEffect, useRef, useState } from 'react';

interface LoaderProps {
  onLoadComplete?: () => void;
}

const Loader: React.FC<LoaderProps> = ({ onLoadComplete }) => {
  const [loadingProgress, setLoadingProgress] = useState(0);
  const hasCalledComplete = useRef(false);

  useEffect(() => {
    if (hasCalledComplete.current) return;

    const checkPageLoad = async () => {
      // Check if document is fully loaded
      if (document.readyState === 'complete') {
        // Check if all images are loaded
        const images = Array.from(document.getElementsByTagName('img'));
        const allImagesLoaded = images.every(img => img.complete && img.naturalWidth !== 0);

        // Check if fonts are loaded
        const fontsLoaded = await document.fonts?.ready?.catch(() => true) ?? true;

        if (allImagesLoaded && fontsLoaded) {
          setLoadingProgress(100);
          hasCalledComplete.current = true;
          onLoadComplete?.();
        }
      }
    };

    // Initial check
    checkPageLoad();

    // Listen for load event
    window.addEventListener('load', checkPageLoad);

    // Monitor DOM changes for dynamic content
    const observer = new MutationObserver(checkPageLoad);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
    });

    // Fallback timeout
    const timeoutId = setTimeout(() => {
      console.warn('Loader timeout: Forcing load complete after 5 seconds');
      setLoadingProgress(100);
      hasCalledComplete.current = true;
      onLoadComplete?.();
    }, 5000);

    return () => {
      window.removeEventListener('load', checkPageLoad);
      observer.disconnect();
      clearTimeout(timeoutId);
    };
  }, [onLoadComplete]);

  return (
    <div className="loader-container">
      <div className="loader-wrapper">
        <div className="loader" style={{ width: '150px', height: '150px' }}>
          <svg viewBox="0 0 100 100">
            <defs>
              <filter id="shadow">
                <feDropShadow floodColor="#000" stdDeviation="1.5" dy={0} dx={0} />
              </filter>
            </defs>
            <circle
              r={45}
              cy={50}
              cx={50}
              style={{
                fill: "transparent",
                stroke: "var(--darkGold)",
                strokeWidth: 7,
                strokeLinecap: "round",
                filter: "url(#shadow)"
              }}
              id="spinner"
            />
          </svg>
        </div>

        <div className="loading-progress-container">
          <div
            className="loading-progress-bar"
            style={{ width: `${loadingProgress}%` }}
          />
          <div className="loading-percentage">
            {Math.floor(loadingProgress)}%
          </div>
        </div>
      </div>
    </div>
  );
};

export default Loader;