import React, { useRef, useEffect, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { WhatsOnData } from "@/data/data";

// CAROUSEL SETTINGS
const WhatsOn: React.FC = () => {
    const settings = {
        dots: false,
        infinite: true,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: false,
        autoplay: true,
        cssEase: "linear",
        vertical: false,
        verticalSwiping: true,
        autoplaySpeed: 3000,
    };

    // Add ref for text content
    const textContentRef = useRef<HTMLDivElement>(null);
    const [textHeight, setTextHeight] = useState(600);
    const [isMounted, setIsMounted] = useState(false);

    // Measure text height on mount and resize
    useEffect(() => {
        setIsMounted(true);

        const updateHeight = () => {
            if (textContentRef.current) {
                setTextHeight(textContentRef.current.offsetHeight);
            }
        };

        updateHeight();
        window.addEventListener('resize', updateHeight);
        return () => window.removeEventListener('resize', updateHeight);
    }, []);

    // Determine image height based on screen size
    const getImageHeight = () => {
        if (!isMounted) return '500px'; // Default height during SSR
        return window.innerWidth >= 768 ? `${textHeight}px` : '500px';
    };

    return (
        <section className='text-center py-16' id="whats-on-section">
            <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4">
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-bold text-white mb-4 uppercase">What&apos;s On</h2>
                </div>

                <div className="flex flex-col lg:flex-row items-center gap-8 overflow-hidden mb-16">
                    {/* Left side - Mini slider */}
                    <div className="w-full lg:w-1/2 h-full overflow-hidden">
                        <Slider {...settings}>
                            {WhatsOnData.map((item, i) =>
                                <div key={i} className="p-2">
                                    <img
                                        src={item.imgSrc}
                                        alt={item.imgSrc}
                                        className="w-full object-cover object-center rounded-lg"
                                        style={{ height: getImageHeight() }}
                                    />
                                </div>
                            )}
                        </Slider>
                    </div>

                    {/* Right side - Text content */}
                    <div ref={textContentRef} className="w-full lg:w-1/2 text-left">
                        <h3 className="text-2xl font-bold text-[var(--darkGold)] mb-4">A Festive Adventure For The Whole Family!</h3>
                        <div className="space-y-4 text-white">
                            <p>Step into a magical Christmas village where holiday dreams come to life! The Convention Before Christmas combines the joy of a traditional Christmas market with the excitement of pop culture and fandom in one unforgettable event.</p>

                            <h3 className="mt-4 text-xl">What To Expect:</h3>
                            <ul className="list-disc pl-5 space-y-2">
                                <li>Visit Santa Clause&apos;s grotto and immerse yourself in Who-ville, the enchanted Christmas village</li>
                                <li>Vibrant festive food market with seasonal treats</li>
                                <li>Unique handcrafted gifts from local artisans</li>
                                <li>Exciting fun fair with rides and attractions</li>
                                <li>Interactive holiday-themed entertainment, including live theatrical shows and Cosplay masquerade</li>
                                <li>Geeky collectibles for the pop culture fans</li>
                                <li>Festive photo opportunities throughout the venue</li>
                                <li>CosBomb competitions, along with special prizes to be won, courtesy of our sponsors</li>
                            </ul>
                            <p>Find all your Christmas gifts under one roof, while creating magical memories that will last a lifetime!</p>
                            <p>Whether you&apos;re shopping for that perfect present, enjoying festive foods, meeting Santa, or embracing your geeky side with holiday cosplay, there&apos;s something for everyone at The Convention Before Christmas. Don&apos;t miss Dublin&apos;s most spectacular holiday event of the season!</p>

                            <p>Keep an eye on our website for more updates.</p>

                            <p>The North Pole is crafting holiday magic for this special event.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}
export default WhatsOn;