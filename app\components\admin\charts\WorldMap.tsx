'use client';

import { ResponsiveChoropleth } from '@nivo/geo';
import worldFeatures from './world_countries.json';
import React, { useMemo } from 'react';
import { Globe } from 'lucide-react';

interface CountryDatum {
  id: string;
  label: string;
  count: number;
  percent?: number;
}

interface Props {
  data: CountryDatum[];
  height?: number;
  className?: string;
}

export const WorldMap: React.FC<Props> = ({ data = [], height = 400, className = "" }) => {
  // Transform and validate data
  const mapData = useMemo(() => {
    if (!Array.isArray(data)) return [];
    return data
      .filter((c) => c && c.id && typeof c.count === 'number' && c.count > 0)
      .map((c) => ({
        id: c.id.toUpperCase(),
        value: c.count,
        label: c.label || c.id.toUpperCase(),
        percent: c.percent ?? 0,
      }));
  }, [data]);

  const hasData = mapData.length > 0;

  if (!hasData) {
    return (
      <div
        className="w-full flex items-center justify-center text-amber-100/50 border border-amber-600/30 rounded-lg"
        style={{ height }}
      >
        <div className="text-center p-4">
          <Globe className="h-8 w-8 mx-auto mb-2 text-amber-400/30" />
          <p>No location data available</p>
          <p className="text-xs text-amber-100/50 mt-1">User location data will appear here</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...mapData.map((d) => d.value), 1);

  return (
    <div
      className={`relative w-full h-full ${className}`}
      style={{ height, minHeight: height }}
    >
      <ResponsiveChoropleth
        data={mapData}
        features={worldFeatures.features}
        margin={{ top: 0, right: 0, bottom: 0, left: 0 }}
        colors="YlOrRd"
        domain={[0, maxValue]}
        unknownColor="#444444"
        label="properties.name"
        valueFormat=".2s"
        projectionScale={150}
        projectionTranslation={[0.5, 0.5]}
        projectionRotation={[0, 0, 0]}
        borderWidth={0.5}
        borderColor="#2D3748"
        enableGraticule={false}
        graticuleLineColor="transparent"
        isInteractive={true}
        theme={{
          tooltip: {
            container: {
              background: '#1F2937',
              color: '#F9FAFB',
              fontSize: '12px',
              borderRadius: '4px',
              boxShadow: '0 3px 9px rgba(0, 0, 0, 0.5)',
              padding: '8px 12px',
            },
          },
        }}
        tooltip={({ feature }) => {
          const country = mapData.find((d) => d.id === feature.id);
          const count = country?.value || 0;
          const percent = country?.percent || 0;
          const name = feature.properties?.name || 'Unknown';
          return (
            <div className="bg-zinc-900 p-2 border border-amber-600/30 rounded text-xs">
              <div className="font-semibold">{name}</div>
              <div className="text-amber-400">{count} users</div>
              {percent > 0 && <div className="text-amber-100/70">{percent.toFixed(1)}%</div>}
            </div>
          );
        }}
      />
    </div>
  );
};