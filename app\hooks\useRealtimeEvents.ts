/**
 * useRealtimeEvents Hook
 *
 * This hook listens to RealtimeManager events and dispatches them to Redux
 * This ensures we use the same Redux store instance as the React components
 */

import { useEffect } from 'react';
import { useAppDispatch } from '@/app/store/hooks';
import { addNotification } from '@/app/store/slices/realtimeSlice';

export const useRealtimeEvents = () => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    let realtimeManager: any = null;

    const initializeEventListener = async () => {
      try {
        const { realtimeManager: manager } = await import('@/app/utils/realtimeManager');
        realtimeManager = manager;

        // Define the notification handler with debouncing
        let debounceTimeout: NodeJS.Timeout | null = null;

        const handleNotification = (notification: any) => {
          console.log('[RealtimeEvents] Received notification from RealtimeManager:', notification);

          // Clear any existing timeout
          if (debounceTimeout) {
            clearTimeout(debounceTimeout);
          }

          // Debounce the dispatch to prevent rapid-fire updates
          debounceTimeout = setTimeout(() => {
            dispatch(addNotification(notification));
            console.log('[RealtimeEvents] Notification dispatched to Redux');
          }, 100); // 100ms debounce
        };

        // Add event listener
        realtimeManager.addEventListener('notification', handleNotification);
        console.log('[RealtimeEvents] Event listener added');

        // Cleanup function
        return () => {
          if (realtimeManager) {
            realtimeManager.removeEventListener('notification', handleNotification);
            console.log('[RealtimeEvents] Event listener removed');
          }
        };
      } catch (error) {
        console.error('[RealtimeEvents] Failed to initialize event listener:', error);
      }
    };

    let cleanup: (() => void) | undefined;

    initializeEventListener().then((cleanupFn) => {
      cleanup = cleanupFn;
    });

    // Return cleanup function
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, [dispatch]);
};