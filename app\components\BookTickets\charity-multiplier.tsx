interface CharityMultiplierProps {
  multiplier: number | null;
  onMultiplierChange: (multiplier: number | null) => void;
}

export function CharityMultiplier({ multiplier, onMultiplierChange }: CharityMultiplierProps) {
  const multipliers = [10, 100, 1000];

  return (
    <div className="space-y-4">
      <div className="flex gap-2 items-center justify-between">
        {multipliers.map((value) => (
          <button
            key={value}
            onClick={() => onMultiplierChange(multiplier === value ? null : value)}
            className={`px-3 py-1 text-sm rounded transition-colors ${
              multiplier === value
                ? 'bg-[var(--darkGold)] text-white'
                : 'bg-gray-700 text-white hover:bg-gray-600'
            }`}
          >
            {value}x
          </button>
        ))}
      </div>

      {multiplier && (
        <div className="text-white text-sm">
          <p>Your donation will be multiplied by {multiplier}x</p>
          <p className="text-[var(--darkGold)] font-semibold mt-1">
            Total donation: €{(10 * multiplier).toFixed(2)}
          </p>
        </div>
      )}
    </div>
  );
}