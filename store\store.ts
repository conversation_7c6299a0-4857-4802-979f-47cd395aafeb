import { configureStore } from '@reduxjs/toolkit';
import infoPanelsReducer from './slices/info-panels-slice';
import bookingReducer from './slices/booking-slice';
import adminReducer from '../app/store/slices/adminSlice';
import realtimeReducer from '../app/store/slices/realtimeSlice';

export const store = configureStore({
  reducer: {
    infoPanels: infoPanelsReducer,
    booking: bookingReducer,
    admin: adminReducer,
    realtime: realtimeReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;