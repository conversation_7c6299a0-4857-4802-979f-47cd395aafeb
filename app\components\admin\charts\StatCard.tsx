import React, { ReactNode } from 'react';
import { ArrowUp, ArrowDown, Minus } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string | number;
  icon: ReactNode;
  color?: 'amber' | 'blue' | 'green' | 'red' | 'purple' | 'indigo' | 'pink' | 'emerald' | 'cyan' | 'violet';
  description?: string;
  loading?: boolean;
  isSmall?: boolean;
  tooltip: string;
}

const colorMap = {
  amber: {
    bg: 'bg-amber-500/10',
    text: 'text-amber-400',
    border: 'border-amber-500/30',
  },
  blue: {
    bg: 'bg-blue-500/10',
    text: 'text-blue-400',
    border: 'border-blue-500/30',
  },
  green: {
    bg: 'bg-green-500/10',
    text: 'text-green-400',
    border: 'border-green-500/30',
  },
  red: {
    bg: 'bg-red-500/10',
    text: 'text-red-400',
    border: 'border-red-500/30',
  },
  purple: {
    bg: 'bg-purple-500/10',
    text: 'text-purple-400',
    border: 'border-purple-500/30',
  },
  indigo: {
    bg: 'bg-indigo-500/10',
    text: 'text-indigo-400',
    border: 'border-indigo-500/30',
  },
  pink: {
    bg: 'bg-pink-500/10',
    text: 'text-pink-400',
    border: 'border-pink-500/30',
  },
  emerald: {
    bg: 'bg-emerald-500/10',
    text: 'text-emerald-400',
    border: 'border-emerald-500/30',
  },
  cyan: {
    bg: 'bg-cyan-500/10',
    text: 'text-cyan-400',
    border: 'border-cyan-500/30',
  },
  violet: {
    bg: 'bg-violet-500/10',
    text: 'text-violet-400',
    border: 'border-violet-500/30',
  },
};

export function StatCard({
  title,
  value,
  change,
  icon,
  color = 'amber',
  description,
  loading = false,
  isSmall = false,
  tooltip = ""
}: StatCardProps) {

  if (loading) {
    return (
      <div className="bg-zinc-800/50 border border-amber-600/30 rounded-lg p-4 h-full">
        <div className="flex justify-between items-start">
          <div className="space-y-2 w-full">
            <div className="h-4 bg-zinc-700 rounded w-3/4"></div>
            <div className="h-6 bg-zinc-700 rounded w-1/2"></div>
            {!isSmall && <div className="h-3 bg-zinc-700 rounded w-1/3"></div>}
          </div>
          <div className={`h-${isSmall ? '6' : '8'} w-${isSmall ? '6' : '8'} rounded-full bg-zinc-700`}></div>
        </div>
      </div>
    );
  }

  const cardClasses = `p-${isSmall ? '2' : '4'} rounded-lg border ${colorMap[color].border} ${colorMap[color].bg} h-full`;
  const titleClasses = `text-${isSmall ? '2xs' : 'xs'} font-medium text-zinc-400`;
  const valueClasses = `text-${isSmall ? 'lg' : '2xl'} font-semibold ${colorMap[color].text}`;
  const iconSize = isSmall ? 'h-4 w-4' : 'h-6 w-6';
  const iconContainerClasses = `p-${isSmall ? '1.5' : '2'} rounded-full ${colorMap[color].bg} bg-opacity-50`;

  return (
    <div className={cardClasses}>
      <div className="flex items-center justify-between">
        <div className={isSmall ? 'space-y-0.5' : 'space-y-1'}>
          <p className={titleClasses}>{title}</p>
          <div className="flex items-baseline">
            <p className={valueClasses}>
              {value}
            </p>
            {change !== undefined && (
              <span
                className={`ml-2 inline-flex items-baseline px-1.5 py-0.5 rounded-full text-2xs font-medium ${typeof change === 'string' && (change.startsWith('+') || !isNaN(Number(change)) && Number(change) > 0)
                  ? 'bg-green-900 text-green-200'
                  : typeof change === 'string' && change.startsWith('-')
                    ? 'bg-red-900 text-red-200'
                    : 'bg-gray-700 text-gray-300'
                  }`}
              >
                {typeof change === 'string' && (change.startsWith('+') || change.startsWith('-')) ? (
                  change.startsWith('+') ? (
                    <ArrowUp className="-ml-0.5 h-2.5 w-2.5 flex-shrink-0 self-center text-green-500" aria-hidden="true" />
                  ) : (
                    <ArrowDown className="-ml-0.5 h-2.5 w-2.5 flex-shrink-0 self-center text-red-500" aria-hidden="true" />
                  )
                ) : (
                  <Minus className="-ml-0.5 h-2.5 w-2.5 flex-shrink-0 self-center text-gray-500" aria-hidden="true" />
                )}
                <span className="ml-0.5">
                  {typeof change === 'string' && (change.startsWith('+') || change.startsWith('-')) ? change.substring(1) : change}
                </span>
              </span>
            )}
          </div>
          {description && (
            <p className={`text-2xs text-zinc-500 ${isSmall ? 'hidden' : ''}`}>{description}</p>
          )}
        </div>
        <div className={iconContainerClasses}>
          {React.cloneElement(icon as React.ReactElement, {
            className: `${iconSize} ${colorMap[color].text}`
          })}
        </div>
      </div>
    </div>
  );
}

interface StatCardGridProps {
  children: ReactNode;
  cols?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function StatCardGrid({
  children,
  cols = 4,
  gap = 'md',
  className = ''
}: StatCardGridProps) {
  const gapMap = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  const colMap = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-5',
    6: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-6',
  };

  return (
    <div className={`grid ${colMap[cols]} ${gapMap[gap]} ${className}`}>
      {children}
    </div>
  );
}