'use client';

import { useState } from 'react';
import { OrderDetails } from '@/types/ticket';
import { useDispatch, useSelector } from 'react-redux';
import { setOrderDetails } from '@/store/slices/booking-slice';
import { RootState } from '@/store/store';

interface TicketDetailsStepProps {
  onContinue: () => void;
}

export function TicketDetailsStep({ onContinue }: TicketDetailsStepProps) {
  const dispatch = useDispatch();
  const orderDetails = useSelector((state: RootState) => state.booking.orderDetails);
  const [isSending, setIsSending] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSending(true);
    setErrors({});

    // Validate form
    const newErrors: Record<string, string> = {};
    if (!orderDetails.email) newErrors.email = 'Email is required';
    if (!orderDetails.name) newErrors.name = 'Name is required';
    if (!orderDetails.phoneNumber) newErrors.phoneNumber = 'Phone number is required';

    // Validate email format
    if (orderDetails.email && !/\S+@\S+\.\S+/.test(orderDetails.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsSending(false);
      return;
    }

    try {
      // Here you would typically make an API call to validate the details
      // For now, we'll just proceed to the next step
      onContinue();
    } catch (error) {
      console.error('Error submitting details:', error);
      setErrors({ submit: 'Failed to submit details. Please try again.' });
    } finally {
      setIsSending(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    dispatch(setOrderDetails({
      ...orderDetails,
      [name]: value
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-white">
          Full Name
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={orderDetails.name}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md bg-gray-700 border ${
            errors.name ? 'border-red-500' : 'border-gray-600'
          } text-white px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]`}
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-500">{errors.name}</p>
        )}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-white">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={orderDetails.email}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md bg-gray-700 border ${
            errors.email ? 'border-red-500' : 'border-gray-600'
          } text-white px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]`}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-500">{errors.email}</p>
        )}
      </div>

      <div>
        <label htmlFor="phoneNumber" className="block text-sm font-medium text-white">
          Phone Number
        </label>
        <input
          type="tel"
          id="phoneNumber"
          name="phoneNumber"
          value={orderDetails.phoneNumber}
          onChange={handleChange}
          className={`mt-1 block w-full rounded-md bg-gray-700 border ${
            errors.phoneNumber ? 'border-red-500' : 'border-gray-600'
          } text-white px-3 py-2 focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]`}
        />
        {errors.phoneNumber && (
          <p className="mt-1 text-sm text-red-500">{errors.phoneNumber}</p>
        )}
      </div>

      {errors.submit && (
        <div className="text-red-500 text-sm">{errors.submit}</div>
      )}

      <button
        type="submit"
        disabled={isSending}
        className={`w-full py-2 px-4 rounded-lg text-white font-semibold transition-colors ${
          isSending
            ? 'bg-gray-600 cursor-not-allowed'
            : 'bg-[var(--darkGold)] hover:bg-[#95784a]'
        }`}
      >
        {isSending ? 'Processing...' : 'Continue to Payment'}
      </button>
    </form>
  );
};