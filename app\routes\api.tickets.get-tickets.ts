import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

// Move static data outside the function
const ticketTypes = [
  {
    id: "adult-1day",
    name: "1 Day Ticket Adult",
    description: "Single day entry for adults",
    price: 30,
    maxQuantity: 10,
    type: "ticket"
  },
  {
    id: "child-1day",
    name: "1 Day Ticket Child",
    description: "Single day entry for children",
    price: 20,
    maxQuantity: 10,
    type: "ticket"
  },
  {
    id: "family-1day",
    name: "1 Day Family Pass",
    description: "Single day entry for up to 5 family members",
    price: 100,
    maxQuantity: 5,
    type: "ticket"
  },
  // 2-day tickets
  {
    id: "adult-2day",
    name: "2 Day Ticket Adult",
    description: "Weekend pass for adults (Saturday & Sunday)",
    price: 55,
    maxQuantity: 10,
    type: "ticket"
  },
  {
    id: "child-2day",
    name: "2 Day Ticket Child",
    description: "Weekend pass for children (Saturday & Sunday)",
    price: 35,
    maxQuantity: 10,
    type: "ticket"
  },
  // Options
  {
    id: "santa-option",
    name: "Meet Santa",
    description: "10-minute slot with Santa (includes 1 child).",
    price: 16,
    maxQuantity: 5,
    type: "option"
  },
  {
    id: "charity-donation",
    name: "Charity Donation",
    description: "Support our chosen charity.",
    price: 3,
    maxQuantity: 0,
    type: "donation"
  }
];

const initialAvailability = {
  "adult-1day-saturday": 1900,
  "adult-1day-sunday": 1900,
  "child-1day-saturday": 1300,
  "child-1day-sunday": 1300,
  "family-1day-saturday": 350,
  "family-1day-sunday": 350,
  "adult-2day": 3000,
  "child-2day": 2500,
  "santa-option-saturday": 162,
  "santa-option-sunday": 162
};

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Use database aggregation instead of fetching all sales
    const { data: salesAggregation, error: salesError } = await supabase
      .from('TicketSales')
      .select('ticket_id, quantity');

    if (salesError) throw salesError;

    // Calculate remaining availability more efficiently
    const remainingAvailability = { ...initialAvailability } as any;

    if (salesAggregation && Array.isArray(salesAggregation)) {
      salesAggregation.forEach((sale: any) => {
        if (remainingAvailability[sale.ticket_id]) {
          remainingAvailability[sale.ticket_id] -= sale.sum;
        }
      });
    }

    // Calculate combined availability
    const combinedAvailability = {
      "adult-1day": remainingAvailability["adult-1day-saturday"] + remainingAvailability["adult-1day-sunday"],
      "child-1day": remainingAvailability["child-1day-saturday"] + remainingAvailability["child-1day-sunday"],
      "family-1day": remainingAvailability["family-1day-saturday"] + remainingAvailability["family-1day-sunday"],
      "santa-option": remainingAvailability["santa-option-saturday"] + remainingAvailability["santa-option-sunday"]
    };

    // Merge availabilities
    const finalAvailability = { ...remainingAvailability, ...combinedAvailability };

    return new Response(JSON.stringify({
      tickets: ticketTypes,
      availability: finalAvailability
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=60' // Add 1-minute cache
      }
    });
  } catch (error: any) {
    console.error('Error fetching tickets:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};