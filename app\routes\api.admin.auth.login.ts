import { json, type ActionFunctionArgs } from "@remix-run/node";
import { createClient } from '@supabase/supabase-js';
import { z } from 'zod';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

// Validation schema
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters")
});

export async function action({ request }: ActionFunctionArgs) {
  try {
    const formData = await request.json();

    // Validate input
    const validatedData = loginSchema.parse(formData);

    // Authenticate with Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email: validatedData.email,
      password: validatedData.password
    });

    if (error) throw error;

    // Get user role from AdminUsers table
    const { data: adminData, error: adminError } = await supabase
      .from('AdminUsers')
      .select('role')
      .eq('user_id', data.user.id)
      .single();

    if (adminError) throw adminError;

    // If user is not in AdminUsers table, they are not an admin
    if (!adminData) {
      return json(
        { error: "Unauthorized access" },
        { status: 403 }
      );
    }

    return json({
      user: data.user,
      role: adminData.role,
      session: data.session
    });
  } catch (error) {
    console.error('Login error:', error);
    return json(
      { error: error instanceof Error ? error.message : "Authentication failed" },
      { status: 400 }
    );
  }
}