/* eslint-disable @typescript-eslint/no-unused-vars */
import {
    <PERSON>,
    But<PERSON>,
    Column,
    Con<PERSON>er,
    Head,
    Heading,
    Html,
    Img,
    Preview,
    Row,
    Section,
    Text,
} from '@react-email/components';
import * as React from 'react';

interface TicketConfirmationEmailProps {
    name: string;
    email: string;
    phoneNumber: string;
    tickets: Array<{
        ticketType: {
            name: string;
            price: number;
        };
        quantity: number;
        selectedDay?: 'Saturday' | 'Sunday';
        santaSlot?: {
            id: number;
            day: string;
            timeRange: string;
            numChildren?: number;
        };
    }>;
    total: number;
    orderId: string;
    qrCodeDataUrl: string;
}

// Update baseUrl to use absolute URL for production
const baseUrl = process.env.SITE_URL || 'https://yourwebsite.com';

// Updated styles with black and gold gradient theme
const main = {
    backgroundColor: '#121212',
    color: '#ffffff',
    fontFamily: 'Arkhip, Arial, sans-serif',
};

const logo = {
    padding: '30px 0',
    textAlign: 'center' as const,
    backgroundColor: '#121212',
};

const content = {
    backgroundColor: '#1e1e1e',
    padding: '20px',
    borderRadius: '8px',
    border: '1px solid #ab8e56',
};

const paragraph = {
    fontSize: '16px',
    lineHeight: '24px',
    color: '#e0e0e0',
};

const heading = {
    fontSize: '32px',
    fontWeight: 'bold',
    textAlign: 'center' as const,
    color: '#ab8e56',
    margin: '30px 0',
};

const button = {
    backgroundColor: '#ab8e56',
    color: '#ffffff',
    padding: '12px 20px',
    borderRadius: '4px',
    textDecoration: 'none',
    textAlign: 'center' as const,
    display: 'inline-block',
    fontWeight: 'bold',
};

const footer = {
    backgroundColor: 'linear-gradient(to right, #121212, #ab8e56, #121212)',
    padding: '20px',
    textAlign: 'center' as const,
    color: '#ab8e56',
    fontSize: '14px',
};

const ticketList = {
    borderTop: '1px solid #e0e0e0',
    borderBottom: '1px solid #e0e0e0',
    padding: '15px 0',
    margin: '15px 0',
};

const ticketItem = {
    padding: '8px 0',
};

const ticketText = {
    fontSize: 16,
    margin: 0,
    color: '#333',
};

const ticketDetailText = {
    fontSize: 12,
    margin: '2px 0 0 0',
    color: '#ab8e56',
    display: 'block',
};

const totalRow = {
    borderTop: '1px solid #e0e0e0',
    marginTop: 10,
    paddingTop: 10,
};

const totalText = {
    fontSize: 18,
    fontWeight: 'bold',
    margin: 0,
    color: '#333',
};

export const TicketConfirmationEmail = ({
    name,
    email,
    phoneNumber,
    tickets,
    total,
    orderId,
    qrCodeDataUrl,
}: TicketConfirmationEmailProps) => {
    return (
        <Html>
            <Head />
            <Body style={main}>
                <Preview>Your Con Before Christmas Ticket Confirmation</Preview>
                <Container>
                    <Section style={logo}>
                        {/* Use absolute URL for logo */}
                        <Img src={`${baseUrl}/images/logo.png`} alt="Con Before Christmas" width={200} height={80} />
                    </Section>

                    <Section style={content}>
                        <Heading style={heading}>
                            Thank you for your purchase!
                        </Heading>

                        <Text style={paragraph}>
                            Dear {name},
                        </Text>

                        <Text style={paragraph}>
                            Your tickets:
                        </Text>

                        <Section style={ticketList}>
                            {tickets.map((item, index) => (
                                <Row key={index} style={ticketItem}>
                                    <Column>
                                        <Text style={ticketText}>
                                            {item.quantity}x {item.ticketType.name}
                                            {item.selectedDay && (
                                                <Text style={ticketDetailText}>({item.selectedDay})</Text>
                                            )}
                                            {/* Handle originalId for day-specific tickets */}
                                            {!item.selectedDay && item.ticketType.originalId && item.ticketType.id.includes('-saturday') && (
                                                <Text style={ticketDetailText}>(Saturday)</Text>
                                            )}
                                            {!item.selectedDay && item.ticketType.originalId && item.ticketType.id.includes('-sunday') && (
                                                <Text style={ticketDetailText}>(Sunday)</Text>
                                            )}
                                            {item.santaSlot && (
                                                <Text style={ticketDetailText}>
                                                    {item.santaSlot.day}, {item.santaSlot.timeRange}
                                                    {item.santaSlot.numChildren && item.santaSlot.numChildren > 1 && (
                                                        <Text style={ticketDetailText}>{item.santaSlot.numChildren} children</Text>
                                                    )}
                                                </Text>
                                            )}
                                        </Text>
                                    </Column>
                                    <Column style={{ textAlign: 'right' }}>
                                        <Text style={ticketText}>
                                            €{(item.ticketType.price * item.quantity).toFixed(2)}
                                        </Text>
                                    </Column>
                                </Row>
                            ))}

                            <Row style={totalRow}>
                                <Column>
                                    <Text style={totalText}>Total:</Text>
                                </Column>
                                <Column style={{ textAlign: 'right' }}>
                                    <Text style={totalText}>€{total.toFixed(2)}</Text>
                                </Column>
                            </Row>
                        </Section>

                        <Text style={paragraph}>
                            <b>Order ID:</b> {orderId}
                        </Text>

                        <Text style={paragraph}>
                            Phone: {phoneNumber}
                        </Text>

                        <Text style={paragraph}>
                            Please present this QR code at the entrance:
                        </Text>

                        <Section style={{ textAlign: 'center', margin: '20px 0' }}>
                            <Img src={qrCodeDataUrl} alt="Ticket QR Code" width={200} height={200} />
                        </Section>

                        <Text style={paragraph}>
                            We look forward to seeing you at the event!
                        </Text>

                        <Section style={{ textAlign: 'center', margin: '30px 0' }}>
                            <Button style={button} href={`${baseUrl}/tickets?orderId=${orderId}`}>
                                View Ticket Details
                            </Button>
                        </Section>
                    </Section>

                    <Section style={footer}>
                        <Text>
                            Con Before Christmas © {new Date().getFullYear()}
                        </Text>
                    </Section>
                </Container>
            </Body>
        </Html>
    );
};
export default TicketConfirmationEmail;