import { useState } from 'react';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { Shield, Database, RefreshCw, AlertTriangle, CheckCircle, XCircle, Trash2, FileText } from 'lucide-react';
import { useSupabase } from '@/app/context/AdminAuthContext';

type ActionCardProps = {
  title: string;
  description: string;
  buttonText: string;
  icon: React.ReactNode;
  action: () => Promise<void>;
  isDangerous?: boolean;
};

const ActionCard = ({
  title,
  description,
  buttonText,
  icon,
  action,
  isDangerous = false,
}: ActionCardProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const handleAction = async () => {
    if (window.confirm(`Are you sure you want to ${buttonText.toLowerCase()}?`)) {
      setIsLoading(true);
      setStatus(null);

      try {
        await action();
        setStatus({
          type: 'success',
          message: `${title} completed successfully`,
        });
      } catch (error) {
        setStatus({
          type: 'error',
          message: `Failed to ${buttonText.toLowerCase()}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="bg-white bg-zinc-800 rounded-lg shadow p-6">
      <div className="flex items-start">
        <div className="flex-shrink-0 p-3 rounded-full bg-zinc-100 bg-zinc-700">
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <h3 className="text-lg font-medium text-gray-900 text-white">{title}</h3>
          <p className="mt-1 text-sm text-gray-500 text-gray-400">{description}</p>

          {status && (
            <div className={`mt-3 p-2 rounded-md text-sm ${
              status.type === 'success'
                ? 'bg-green-50 text-green-800 bg-green-900/20 text-green-200'
                : 'bg-red-50 text-red-800 bg-red-900/20 text-red-200'
            }`}>
              <div className="flex">
                <div className="flex-shrink-0">
                  {status.type === 'success' ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <XCircle className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-2">
                  <p>{status.message}</p>
                </div>
              </div>
            </div>
          )}

          <div className="mt-4">
            <button
              type="button"
              onClick={handleAction}
              disabled={isLoading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
                isDangerous
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  Processing...
                </>
              ) : (
                buttonText
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function AdminControls() {
  const supabase = useSupabase();

  const clearCache = async () => {
    try {
      // Clear browser cache programmatically
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }

      // Clear localStorage and sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // Force reload to clear any in-memory caches
      window.location.reload();
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw error;
    }
  };

  const rebuildSearchIndex = async () => {
    try {
      // This would typically call a backend API to rebuild search indices
      // For now, we'll refresh the analytics events table which acts as our search data
      const { error } = await supabase
        .from('AnalyticsEvents')
        .select('count(*)')
        .limit(1);

      if (error) throw error;

      // In a real implementation, you might call a search service API
      console.log('Search index rebuild completed');
    } catch (error) {
      console.error('Error rebuilding search index:', error);
      throw error;
    }
  };

  const runDatabaseBackup = async () => {
    try {
      // Get database statistics
      const tables = ['Orders', 'VendorStands', 'CharityDonations', 'AnalyticsEvents', 'NewsletterSubscribers', 'SantaBookings'];
      const backupInfo = [];

      for (const table of tables) {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });

        if (!error) {
          backupInfo.push({ table, count });
        }
      }

      // In a real implementation, this would trigger a database backup
      console.log('Database backup completed. Tables backed up:', backupInfo);

      // Create a backup report
      const backupReport = {
        timestamp: new Date().toISOString(),
        tables: backupInfo,
        status: 'completed'
      };

      // Store backup info in localStorage for demo purposes
      localStorage.setItem('lastBackup', JSON.stringify(backupReport));

    } catch (error) {
      console.error('Error running database backup:', error);
      throw error;
    }
  };

  const clearTempFiles = async () => {
    try {
      // Clear temporary data from localStorage/sessionStorage
      const keysToRemove = [];

      // Find temporary keys
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith('temp_') || key.startsWith('cache_') || key.includes('_temp'))) {
          keysToRemove.push(key);
        }
      }

      // Remove temporary keys
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Clear old analytics events (older than 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { error } = await supabase
        .from('AnalyticsEvents')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString());

      if (error && error.code !== 'PGRST116') { // Ignore "no rows deleted" error
        throw error;
      }

      console.log(`Cleared ${keysToRemove.length} temporary files and old analytics data`);

    } catch (error) {
      console.error('Error clearing temporary files:', error);
      throw error;
    }
  };

  const runSystemDiagnostics = async () => {
    try {
      const diagnostics = {
        timestamp: new Date().toISOString(),
        browser: {
          userAgent: navigator.userAgent,
          language: navigator.language,
          cookieEnabled: navigator.cookieEnabled,
          onLine: navigator.onLine
        },
        performance: {
          memory: (performance as any).memory ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
          } : null,
          timing: performance.timing ? {
            loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
            domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
          } : null
        },
        database: {},
        errors: []
      };

      // Test database connectivity
      const tables = ['Orders', 'VendorStands', 'CharityDonations', 'AnalyticsEvents'] as any;
      for (const table of tables) {
        try {
          const { count, error } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });

          if (error) {
            diagnostics.errors.push(`Database error for ${table}: ${(error as any).message}`);
          } else {
            diagnostics.database[table] = { status: 'ok', count };
          }
        } catch (err: any) {
          diagnostics.errors.push(`Connection error for ${table}: ${err.message}`);
        }
      }

      // Store diagnostics report
      localStorage.setItem('lastDiagnostics', JSON.stringify(diagnostics));
      console.log('System diagnostics completed:', diagnostics);

    } catch (error) {
      console.error('Error running system diagnostics:', error);
      throw error;
    }
  };

  return (
    <ProtectedRoute requiredRole={['super_admin']}>
      <AdminLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 text-white">Admin Controls</h1>
            <p className="mt-1 text-sm text-gray-500 text-gray-400">
              Advanced system controls and maintenance actions
            </p>
          </div>

          <div className="bg-yellow-50 bg-yellow-900/20 border-l-4 border-yellow-400 border-yellow-600 p-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800 text-yellow-200">
                  Administrator Access Required
                </h3>
                <div className="mt-2 text-sm text-yellow-700 text-yellow-400">
                  <p>
                    These are advanced system controls. Please use with caution as some actions may affect system
                    performance or result in data loss.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <ActionCard
              title="Clear Application Cache"
              description="Clears browser cache, localStorage, and forces a page reload to ensure fresh data."
              buttonText="Clear Cache"
              icon={<RefreshCw className="h-6 w-6 text-blue-500" />}
              action={clearCache}
            />

            <ActionCard
              title="Rebuild Search Index"
              description="Refreshes search data and validates database connectivity for search functionality."
              buttonText="Rebuild Index"
              icon={<Database className="h-6 w-6 text-purple-500" />}
              action={rebuildSearchIndex}
            />

            <ActionCard
              title="Run Database Backup"
              description="Creates a backup report with table statistics and stores backup information."
              buttonText="Backup Database"
              icon={<Database className="h-6 w-6 text-green-500" />}
              action={runDatabaseBackup}
            />

            <ActionCard
              title="Clear Temporary Files"
              description="Removes temporary cache files and old analytics data (older than 30 days)."
              buttonText="Clear Temp Files"
              icon={<Trash2 className="h-6 w-6 text-yellow-500" />}
              action={clearTempFiles}
            />

            <ActionCard
              title="Run System Diagnostics"
              description="Performs comprehensive system checks including database connectivity and performance metrics."
              buttonText="Run Diagnostics"
              icon={<FileText className="h-6 w-6 text-indigo-500" />}
              action={runSystemDiagnostics}
            />

            <ActionCard
              title="Emergency Maintenance Mode"
              description="Puts the site into maintenance mode. Only administrators will be able to access it."
              buttonText="Enable Maintenance"
              icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
              action={async () => {
                // Toggle maintenance mode
                await new Promise(resolve => setTimeout(resolve, 1000));
              }}
              isDangerous
            />
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};