import { useEffect, useRef, useState } from "react";
import { Link, useLocation } from "@remix-run/react";
import { headerData } from "../Header/Navigation/menuData";
import Logo from "./Logo";
import HeaderLink from "../Header/Navigation/HeaderLink";
import MobileHeaderLink from "../Header/Navigation/MobileHeaderLink";
import { VolumeX, Volume2 } from "lucide-react";
import { useAudio } from "@/app/context/AudioContext";

const Header: React.FC = () => {
  const [navbarOpen, setNavbarOpen] = useState(false);
  const [sticky, setSticky] = useState(false);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const location = useLocation();
  const [MarqueeScroll, setMarqueeScroll] = useState<React.ComponentType<{ phrases: string[], sticky: boolean, key: string }> | null>(null);
  const { isMuted, toggleMute } = useAudio();
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Store scroll position when opening mobile menu
  const scrollPositionRef = useRef<number>(0);

  useEffect(() => {
    setNavbarOpen(false);
  }, [location.pathname]);

  const marqueeItems = [
    "Trade hall/vendor Applications are still live, please visit the event applications page to submit your request and get Santa's approval in the gifting of presents this year.",
    "Event tickets are now on sale, simply click the Book Tickets button and follow the steps to purchase your tickets.",
    "Don't forget, you can donate to the Laura Lynn Children's Hospice to help them continue their vital work."
  ];

  const handleScroll = () => {
    setSticky(window.scrollY >= 10);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node) && navbarOpen) {
      setNavbarOpen(false);
    }
  };

  const handleOpenTicketModal = () => {
    setIsTicketModalOpen(true);
    setNavbarOpen(false);
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [navbarOpen]);

  useEffect(() => {
    if (typeof window === "undefined") return;

    if (navbarOpen) {
      // Store current scroll position
      scrollPositionRef.current = window.scrollY;
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
      // Restore scroll position
      window.scrollTo(0, scrollPositionRef.current);
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [navbarOpen]);

  const bookTicketsItem = headerData.find((item) => item.button);

  const regularMenuItems = headerData.filter((item) => !item.button);

  useEffect(() => {
    // Reset the MarqueeScroll component when location changes
    setMarqueeScroll(null);

    import("./MarqueeScroll")
      .then((module) => {
        setMarqueeScroll(() => module.default);
      })
      .catch((error) => {
        console.error("Failed to load MarqueeScroll component:", error);
      });
  }, [location.pathname]);

  return (
    <>
      <header
        className={`top-0 z-40 w-full transition-all duration-300 bg-transparent backdrop-blur-md border border-darkGold border-solid border-b border-l-0 border-r-0 border-t-0 ${sticky ? "fixed" : "relative py-4"
          }`}
      >
        <div className="lg:py-0 py-2">
          <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md flex flex-col sm:flex-row items-center px-4">
            <div className="w-full flex items-center justify-between mb-3 sm:mb-0 gap-2">
              <Logo />

              {/* Hamburger Menu Button - Only visible on mobile */}
              <button
                onClick={() => setNavbarOpen(!navbarOpen)}
                className="p-2 rounded-lg sm:hidden shadow-none"
                aria-label="Toggle mobile menu"
                type="button"
              >
                <span className="block w-6 h-0.5 bg-white"></span>
                <span className="block w-6 h-0.5 bg-white mt-1.5"></span>
                <span className="block w-6 h-0.5 bg-white mt-1.5"></span>
              </button>

              <button
                onClick={toggleMute}
                className="sm:hidden p-2 rounded-full hover:bg-white/10 transition-colors border border-solid border-white/40 hover:border-white"
                aria-label={isMuted ? "Unmute background music" : "Mute background music"}
                type="button"
                data-skip-play="true"
              >
                {isMuted ? <VolumeX className="w-6 h-6 text-white" /> : <Volume2 className="w-6 h-6 text-white" />}
              </button>
            </div>

            <div className="flex items-center justify-center sm:justify-end gap-4 z-10 w-full md:w-auto">
              {bookTicketsItem && (
                <div className="flex items-center w-full z-20">
                  <HeaderLink
                    item={bookTicketsItem}
                    onClick={() => setNavbarOpen(false)}
                    isBookTickets={true}
                    isButton={true}
                    className="inline-block w-full text-center md:w-auto min-w-40"
                  />
                </div>
              )}

              {/* Hamburger Menu Button - Only visible on desktop */}
              <button
                onClick={() => setNavbarOpen(!navbarOpen)}
                className="p-2 hidden sm:block"
                aria-label="Toggle mobile menu"
                type="button"
              >
                <span className="block w-6 h-0.5 bg-white shadow-[0_1px_2px_rgba(0,0,0,0.8)]"></span>
                <span className="block w-6 h-0.5 bg-white mt-1.5 shadow-[0_1px_2px_rgba(0,0,0,0.8)]"></span>
                <span className="block w-6 h-0.5 bg-white mt-1.5 shadow-[0_1px_2px_rgba(0,0,0,0.8)]"></span>
              </button>

              <button
                onClick={toggleMute}
                className="hidden sm:block p-2 rounded-full hover:bg-white/10 transition-colors border border-solid border-white/40 hover:border-white"
                aria-label={isMuted ? "Unmute background music" : "Mute background music"}
                type="button"
              >
                {isMuted ? (
                  <VolumeX className="w-6 h-6 text-white [filter:drop-shadow(0_1px_1px_#000)]" />
                ) : (
                  <Volume2 className="w-6 h-6 text-white [filter:drop-shadow(0_1px_1px_#000)]" />
                )}
              </button>
            </div>
          </div>
        </div>
        {/* Only show MarqueeScroll on the home page */}
        {location.pathname === '/' && MarqueeScroll && (
          <MarqueeScroll
            key={`marquee-${location.key}`}
            phrases={marqueeItems}
            sticky={sticky}
          />
        )}
      </header>

      {/* Slide Down Mobile Menu - Full overlay */}
      <div
        ref={mobileMenuRef}
        className={`fixed top-0 left-0 w-full h-full bg-[#151515] shadow-lg transform transition-transform duration-300 z-50 ${navbarOpen ? "translate-y-0" : "-translate-y-full"
          } flex flex-col overflow-hidden`}
      >
        {navbarOpen && (
          <>
            <div className="flex items-center justify-between p-4 flex-shrink-0">
              <h2 className="text-lg font-bold text-white">
                <Logo />
              </h2>

              {/* Close button */}
              <button
                onClick={() => setNavbarOpen(false)}
                className="w-5 h-5 mr-2"
                aria-label="Close menu Modal"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" className="w-5 h-5 fill-white">
                  <path d="M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z" />
                </svg>
              </button>
            </div>

            <div className="flex-grow overflow-y-auto">
              <nav className="flex flex-col items-center justify-start p-4 space-y-4 mt-8 pb-20">
                {regularMenuItems.map((item, index) => (
                  <MobileHeaderLink
                    key={index}
                    item={item}
                    onClick={() => setNavbarOpen(false)}
                    isButton={false}
                  />
                ))}

                {bookTicketsItem && (
                  <div className="w-full mt-6 text-center">
                    <MobileHeaderLink
                      item={bookTicketsItem}
                      onClick={() => handleOpenTicketModal()}
                      isBookTickets={true}
                      isButton={true}
                      className="inline-block"
                    />
                  </div>
                )}
              </nav>
            </div>
          </>
        )}
      </div>

      {/* Add spacer div that appears only when header is fixed */}
      {sticky && <div className="h-24" />}
    </>
  );
};
export default Header;
