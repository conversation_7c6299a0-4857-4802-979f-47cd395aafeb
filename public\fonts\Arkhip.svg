<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Thu Nov 13 18:00:52 2014
 By 
Copyright (c) 2014 by <PERSON> for Studio Design Klimov. All rights reserved.
</metadata>
<defs>
<font id="Arkhip" horiz-adv-x="219" >
  <font-face 
    font-family="Arkhip"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="0 0 0 0 0 0 0 0 0 0"
    ascent="751"
    descent="-249"
    x-height="518"
    cap-height="746"
    bbox="0 -249 1477 946"
    underline-thickness="50"
    underline-position="-99"
    unicode-range="U+0020-2122"
  />
<missing-glyph horiz-adv-x="540" 
 />
    <glyph glyph-name=".notdef" horiz-adv-x="540" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="271" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="339" 
d="M136 196q-2 139 -6 275t-9 275h105q-5 -139 -9 -275t-6 -275h-75zM113 59q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="400" 
d="M151 518h-69l-22 241h114zM316 518h-69l-21 241h114z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="821" 
d="M350 459l-24 -152h175l25 152h-176zM791 459h-165l-24 -152h164l-12 -88h-164l-35 -219h-101l35 219h-174l-37 -219h-100l35 219h-163l11 88h163l26 152h-162l10 90h163l32 202h100l-31 -202h175l31 202h101l-31 -202h163z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="740" 
d="M203 535q0 -16 6 -31t21 -26.5t41 -19t66 -9.5v207q-28 -3 -52.5 -14t-42.5 -27.5t-28.5 -37t-10.5 -42.5zM578 227q0 33 -11.5 53.5t-31.5 33t-46 18t-56 7.5v-239q30 6 56.5 16.5t46 26t31 36.5t11.5 48zM337 832h96v-70q63 -9 117.5 -35t106.5 -77l-68 -80
q-43 35 -78.5 53.5t-77.5 26.5v-203q76 -5 125.5 -22t78.5 -45.5t41 -67t12 -85.5q0 -48 -19 -88.5t-53 -71t-81.5 -50.5t-103.5 -27v-70h-96v65q-81 5 -157 43.5t-130 107.5l78 73q13 -17 32.5 -36t45.5 -36t59 -29t72 -15v248q-126 4 -190.5 52t-64.5 141q0 43 18.5 84
t52.5 73t81 52.5t103 23.5v65z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1029" 
d="M890 159q0 29 -11.5 50t-29.5 34.5t-41.5 20t-47.5 6.5q-25 0 -48.5 -7t-42 -21t-29.5 -35.5t-11 -49.5q0 -29 11.5 -50t30 -35t42 -20.5t47.5 -6.5t47.5 7.5t42 22.5t29.5 36t11 48zM979 160q0 -47 -18.5 -84.5t-49.5 -63t-71 -39t-82 -13.5t-81.5 13.5t-70 39t-48.5 62
t-18 83.5q0 48 18.5 84.5t49.5 62t70.5 38.5t81.5 13t81 -12t70 -36.5t49.5 -61.5t18.5 -86zM400 514q0 29 -11.5 50t-29.5 34.5t-41.5 20t-47.5 6.5q-25 0 -48.5 -7t-42 -21t-29.5 -35.5t-11 -49.5q0 -29 11.5 -50t30 -35t42 -20.5t47.5 -6.5t47.5 7.5t42 22.5t29.5 36
t11 48zM489 515q0 -47 -18.5 -84.5t-49.5 -63.5t-71 -39.5t-82 -13.5t-81.5 13.5t-70 39t-48.5 62.5t-18 84q0 48 18.5 84.5t49.5 62t70.5 38.5t81.5 13t81 -12t70 -36.5t49.5 -61.5t18.5 -86zM304 -39h-116l541 757h117z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="870" 
d="M567 574q-18 24 -40.5 40t-46.5 26t-48 14t-45 4q-25 0 -53 -6t-52 -20.5t-40 -38.5t-16 -60q0 -45 31.5 -69.5t79.5 -24.5h110l-8 -109h-104q-26 0 -52 -6.5t-48 -21.5t-35.5 -38.5t-13.5 -56.5q0 -22 7 -42.5t25.5 -36.5t50 -25.5t81.5 -9.5q33 0 67 9t65.5 24.5
t59.5 36t50 43.5l9 124h-77l9 109h257l-9 -109h-73l-24 -330h-106l6 93q-56 -50 -119.5 -76.5t-114.5 -26.5q-71 0 -122.5 16t-84.5 45t-49 68t-16 86q0 39 9.5 70t24.5 55.5t34 43t37 31.5q-32 22 -49.5 49t-17.5 69q0 33 11.5 75t42.5 80t83.5 64t135.5 26q45 0 82 -9.5
t67.5 -25.5t54.5 -36.5t42 -42.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="234" 
d="M151 518h-69l-22 241h114z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="426" 
d="M388 766q-32 -23 -68.5 -63.5t-67.5 -92.5t-51 -113t-20 -126q0 -61 19.5 -121.5t49.5 -113t65.5 -94.5t67.5 -66l-66 -59q-54 43 -101 95t-81 110.5t-53.5 122t-19.5 129.5t20 131.5t56.5 124.5t87 110t111.5 89z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="426" 
d="M388 371q0 -66 -20 -131.5t-56 -124.5t-86.5 -110t-111.5 -88l-52 63q33 22 69 62t67 92.5t51 114t20 125.5q0 61 -19.5 121.5t-49.5 113t-65.5 94.5t-66.5 66l66 60q54 -44 100.5 -96.5t80.5 -111t53.5 -122t19.5 -128.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="437" 
d="M310 583l66 -53l-30 -55l-81 30l-13 -84h-63l-12 84l-85 -32l-32 56l69 54l-67 53l31 55l84 -30l12 86h63l13 -86l80 31l32 -56z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="595" 
d="M344 321v-188h-93v188h-191v93h191v188h93v-188h191v-93h-191z" />
    <glyph glyph-name="comma" unicode="," 
d="M50 62q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -45.5t-23 -48t-36 -44.5t-47 -34l-23 30q35 27 49.5 48.5t14.5 45.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="595" 
d="M60 321v93h475v-93h-475z" />
    <glyph glyph-name="period" unicode="." 
d="M50 62q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="630" 
d="M161 -28h-122l416 773h124z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="768" 
d="M384 -17q-58 0 -114.5 21t-100.5 67.5t-71.5 120t-27.5 178.5t27.5 180.5t71.5 123.5t100.5 71t114.5 23t114.5 -23t100.5 -71t71.5 -123.5t27.5 -180.5t-27.5 -178.5t-71.5 -120t-100.5 -67.5t-114.5 -21zM591 370q0 73 -17 127t-45.5 90t-66 53.5t-78.5 17.5
t-78.5 -17.5t-66 -53.5t-45.5 -90t-17 -127t17 -125.5t45.5 -86.5t66 -50t78.5 -16t78.5 16t66 50t45.5 86.5t17 125.5z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="370" 
d="M153 0v611l-103 -85v137l103 89h107v-752h-107z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="699" 
d="M93 650q24 23 55 44t66.5 37.5t72.5 26.5t73 10q61 0 103.5 -14.5t70.5 -36.5t43.5 -49t23.5 -53t9.5 -47t1.5 -31q0 -15 -4.5 -43t-23 -64t-57 -77t-106.5 -81q-46 -27 -80.5 -46t-61 -36.5t-46 -36t-34.5 -44.5h420v-109h-539v109q19 49 44 82t59 59.5t78.5 52.5
t103.5 61q50 29 78 56.5t41.5 50.5t16.5 40t3 26q0 8 -3 28t-16.5 40.5t-43 36.5t-81.5 16q-24 0 -51 -6.5t-53 -18.5t-50 -28.5t-43 -36.5z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="748" 
d="M154 746h467v-110l-185 -177q106 -11 169 -67.5t63 -152.5q0 -57 -23.5 -104.5t-64 -81t-94 -52t-113.5 -18.5q-35 0 -76 9.5t-81.5 27t-76 42.5t-59.5 56l67 93q51 -64 113 -91.5t113 -27.5q38 0 72 10.5t59.5 29.5t40.5 46.5t15 60.5q0 60 -47 87.5t-125 27.5h-88v108
l179 174h-325v110z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="760" 
d="M459 627l-257 -327h257v327zM569 191v-191h-110v191h-394v109l369 452h135v-452h111l-41 -109h-70z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="743" 
d="M368 -17q-35 0 -76 9.5t-81.5 27t-76 42.5t-59.5 56l67 91q25 -32 54 -54t59 -36t59 -20.5t54 -6.5q38 0 72 10.5t59.5 29.5t40.5 46.5t15 60.5q0 30 -12.5 56t-35 45t-54 30t-70.5 11q-35 0 -62.5 -4.5t-49 -12t-38 -16.5t-28.5 -18l-92 15l75 401h410v-110h-322
l-34 -181q21 15 60 24.5t81 9.5q63 0 114.5 -19.5t88.5 -53t57 -79.5t20 -98q0 -57 -23.5 -104.5t-64 -81t-94 -52t-113.5 -18.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="715" 
d="M536 236q0 69 -44.5 113t-131.5 44q-42 0 -73.5 -12t-53 -33.5t-32.5 -50t-11 -61.5q0 -31 13.5 -57.5t37 -45.5t54 -30t65.5 -11q36 0 68 11t56 30.5t38 46t14 56.5zM549 616q-33 19 -72 30.5t-81 11.5q-91 0 -148 -61.5t-67 -172.5q33 32 77.5 54t101.5 22
q64 0 116.5 -19.5t90 -54t58 -83.5t20.5 -107q0 -53 -22.5 -99t-61 -80.5t-90.5 -54t-111 -19.5q-45 0 -84 11.5t-71.5 32t-57.5 47.5t-42 58q-13 24 -20 51.5t-10.5 56t-4 57t-0.5 54.5q0 84 18 159.5t57.5 133t101.5 91t149 33.5q57 0 109.5 -15t98.5 -42z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="663" 
d="M276 0h-130l333 636h-419v110h543v-108z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="719" 
d="M117 561q0 43 19 80.5t52 65.5t77 44.5t94 16.5t94 -16.5t77 -44.5t52 -65.5t19 -80.5q0 -45 -20 -81.5t-52 -64.5q54 -32 87 -83t33 -111q0 -50 -23 -93t-62.5 -75.5t-92.5 -51t-112 -18.5q-60 0 -112.5 18.5t-91.5 51t-62 75.5t-23 93q0 60 32.5 111t86.5 83
q-33 28 -52.5 64.5t-19.5 81.5zM180 221q0 -27 14 -51t38 -41t56.5 -27t70.5 -10q37 0 70 10t57.5 27t38.5 41t14 51q0 28 -14 52.5t-38.5 43t-57.5 29t-70 10.5q-38 0 -70.5 -10.5t-56.5 -29t-38 -43t-14 -52.5zM226 561q0 -21 10.5 -39t28.5 -31.5t42 -21.5t52 -8t52.5 8
t42.5 22t28 32t10 38q0 21 -10.5 38.5t-28.5 30.5t-42.5 20.5t-51.5 7.5q-28 0 -52 -7.5t-42 -20.5t-28.5 -30.5t-10.5 -38.5z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="715" 
d="M179 514q0 -69 44.5 -113t131.5 -44q42 0 73.5 12t53 33.5t32.5 50t11 61.5q0 31 -13.5 57.5t-37 45.5t-54.5 30t-65 11q-36 0 -68 -11t-56 -30.5t-38 -46t-14 -56.5zM166 134q33 -19 72 -30.5t81 -11.5q91 0 148 61.5t67 172.5q-33 -32 -77.5 -54t-101.5 -22
q-64 0 -116.5 19.5t-90 54t-58 83t-20.5 107.5q0 52 22.5 98.5t61 81t90.5 54.5t111 20q45 0 84 -12t71 -32t57.5 -47.5t42.5 -58.5q13 -24 20 -51.5t10.5 -56t4 -57t0.5 -54.5q0 -84 -18 -159.5t-57.5 -132.5t-101.5 -90.5t-149 -33.5q-57 0 -109.5 14.5t-98.5 41.5z" />
    <glyph glyph-name="colon" unicode=":" 
d="M50 457q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM50 62q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="semicolon" unicode=";" 
d="M50 457q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM50 62q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -45.5t-23 -48t-36 -44.5t-47 -34l-23 30q35 27 49.5 48.5t14.5 45.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="509" 
d="M70 224v98l389 254v-130l-277 -173l277 -173v-130z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="595" 
d="M60 231v93h475v-93h-475zM60 411v93h475v-93h-475z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="509" 
d="M50 -30v130l277 173l-277 173v130l389 -254v-98z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="609" 
d="M221 42q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -41.5t-42.5 -16.5t-42 16.5t-17 41.5zM50 650q21 24 46 45t56.5 37.5t69.5 26t85 9.5q74 0 123 -22t77.5 -54t40 -68.5t11.5 -66.5q0 -45 -9.5 -78t-30.5 -60.5t-55.5 -52.5t-85.5 -53q-15 -9 -27 -16.5
t-20.5 -19.5t-13 -31t-4.5 -50h-70q-2 19 -4 38.5t-2 38.5t3.5 37.5t13 36t26 32t42.5 26.5q30 13 54 27t40.5 31.5t25.5 40t9 53.5q0 13 -6 30t-22.5 33t-44.5 27t-71 11q-34 0 -60.5 -6.5t-48 -18.5t-40.5 -28.5t-38 -36.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1020" 
d="M511 350q-51 0 -75 -6t-24 -35q0 -21 23 -31t77 -10q11 0 29 2.5t34.5 13t28.5 30.5t12 56q-20 -14 -44 -17t-43 -3h-18zM342 499q19 23 39.5 36.5t42 21t43.5 9.5t45 2q51 0 84.5 -18t54 -44t28.5 -56.5t8 -55.5v-59q0 -30 17 -49t55 -19q21 0 39.5 12t32.5 32.5t22 48
t8 59.5q0 58 -28.5 106.5t-76.5 83t-111.5 53.5t-133.5 19q-66 0 -128.5 -21t-111.5 -60t-79 -95t-30 -125q0 -74 29 -131.5t77 -97.5t110.5 -61t130.5 -21q61 0 99.5 13t58.5 29l57 -75q-28 -18 -79.5 -35.5t-134.5 -17.5t-162.5 26t-141 76t-99 121.5t-37.5 163.5
q0 98 34.5 172.5t94.5 124.5t140 75.5t172 25.5q91 0 171 -21.5t139.5 -65t94 -109.5t34.5 -154q0 -50 -17 -92.5t-44.5 -73.5t-61.5 -48.5t-68 -17.5q-57 0 -87.5 14.5t-47.5 32.5q-12 -22 -45 -34t-68 -12q-91 0 -140.5 29t-49.5 95q0 36 13.5 59t36.5 36.5t55 18.5t70 5
q13 0 26 -1t26 -1q44 0 47 17q0 15 -20.5 32t-62.5 17q-32 0 -54.5 -7.5t-39.5 -27.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="811" 
d="M302 759h120l359 -759h-122l-49 109h-227q-51 0 -90.5 -7t-67.5 -18t-45 -24t-23 -25l-19 -35h-118l323 667zM561 219l-163 315l-165 -357q20 17 66 29.5t124 12.5h138z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="814" 
d="M218 408q34 20 69.5 25t72.5 5h128q51 0 81 25.5t30 77.5q0 53 -31 74t-80 21h-270v-228zM218 109h281q22 0 46.5 6.5t44.5 20t33 34t13 48.5q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-128q-36 0 -72 -6t-70 -24v-188zM744 204q0 -56 -22.5 -94.5t-59 -63t-82.5 -35.5
t-92 -11h-378v746h378q47 0 87.5 -13.5t70 -39.5t46 -64t16.5 -87q0 -48 -17 -87t-50 -60q19 -9 38 -25.5t33 -40.5t23 -55t9 -70z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="816" 
d="M679 568q-42 42 -98 66t-120 24q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q73 0 134 31.5t103 85.5l67 -91q-57 -63 -135.5 -99t-169.5 -36q-85 0 -158 31t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5t88 122
t128.5 80.5t153 29q85 0 158.5 -31.5t128.5 -86.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="837" 
d="M259 109h169q54 0 98.5 21t75.5 56.5t48 82.5t17 99q0 54 -16 102.5t-47 85.5t-75.5 58.5t-102.5 21.5h-167v-527zM45 746h380q76 0 140.5 -29.5t111.5 -80t73.5 -117.5t26.5 -143t-29.5 -149t-81.5 -128t-108 -77t-130 -22h-276v636h-59z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="744" 
d="M219 109h460v-109h-569v746h525v-110h-416v-226q34 20 69 24.5t72 4.5h163v-111h-163q-36 0 -71.5 -5.5t-69.5 -23.5v-190z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="695" 
d="M219 0h-109v746h525v-110h-416v-226q34 20 69 24.5t72 4.5h163v-111h-163q-36 0 -71.5 -5.5t-69.5 -23.5v-299z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="841" 
d="M679 568q-42 42 -98 66t-120 24q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q57 0 107 19t89 53v136h-96l-47 108h251v-290q-57 -63 -135.5 -99t-169.5 -36q-85 0 -158 31t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5
t88 122t128.5 80.5t153 29q85 0 158.5 -31.5t128.5 -86.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="876" 
d="M218 0h-108v746h108v-306q23 11 47 15.5t48 4.5q16 0 46.5 -3.5t66 -7.5t70.5 -7.5t60 -3.5q23 0 50 4t51 15v289h109v-746h-109v347q-24 -11 -51 -15t-50 -4q-25 0 -60 3.5t-70.5 7.5t-66 7.5t-46.5 3.5q-24 0 -48 -4.5t-47 -15.5v-330z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="327" 
d="M110 0v746h107v-746h-107z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="675" 
d="M565 295q0 -86 -27.5 -145.5t-70.5 -96.5t-94 -53.5t-99 -16.5q-33 0 -68 6t-68.5 17.5t-63.5 27.5t-54 37l55 96q11 -12 28 -25.5t41.5 -24.5t56.5 -18.5t74 -7.5q53 0 88 20t55.5 51t29 67.5t9.5 68.5v338h-159l-48 110h315v-451z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="825" 
d="M219 0h-109v746h109v-557l379 557h131l-245 -351l134 -201q35 -52 63.5 -69t52.5 -15l31 1v-111h-39q-26 0 -49.5 3t-46.5 14.5t-47 35t-50 65.5l-113 181z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="653" 
d="M110 0v746h109v-637h414v-109h-523z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="986" 
d="M492 208l-253 305v-513h-107v641l-87 105h141l305 -366l309 366h141l-87 -105v-641h-107v513z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="885" 
d="M45 746h141l479 -570v570h110v-746h-108l-428 513v-513h-107v641z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="923" 
d="M755 378q0 61 -23.5 112.5t-64 89t-93.5 58t-113 20.5q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q60 0 113.5 22t93 60.5t63 90.5t23.5 113zM863 372q0 -83 -32.5 -154t-87.5 -123t-128 -82t-154 -30q-85 0 -158 31
t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5t88 122t128.5 80.5t153 29q83 0 156 -30.5t127.5 -84t86 -125.5t31.5 -156z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="761" 
d="M493 427q110 0 110 106q0 57 -28 80.5t-82 23.5h-274v-240q34 20 69.5 25t72.5 5h132zM110 0v746h383q49 0 89.5 -14.5t69 -42t44 -67t15.5 -89.5t-15.5 -89.5t-44 -68t-69 -43.5t-89.5 -15h-132q-36 0 -72 -6t-70 -24v-287h-109z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="939" 
d="M490 282q24 -2 43 -7t39 -16.5t45 -29.5t61 -45q35 38 56 87.5t21 106.5q0 61 -23.5 112.5t-64 89t-93.5 58t-113 20.5q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q62 0 122 26q-24 18 -42 30.5t-35 20.5t-34 12.5
t-38 5.5zM863 372q0 -73 -25.5 -137t-70.5 -115q22 -14 51 -27t61 -15l-55 -95q-43 5 -76.5 22.5t-66.5 40.5q-98 -63 -220 -63q-85 0 -158 31t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5t88 122t128.5 80.5t153 29q83 0 156 -30.5t127.5 -84t86 -125.5t31.5 -156z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="840" 
d="M219 389q34 20 69 24.5t72 4.5h142q57 0 92 27t35 87q0 29 -10 49t-27 32.5t-40 18t-50 5.5h-283v-248zM447 309h-87q-36 0 -71.5 -5.5t-69.5 -23.5v-280h-109v746h392q51 0 94 -14.5t74.5 -42.5t49 -69t17.5 -94q0 -85 -45.5 -139t-126.5 -74l58 -119q18 -26 31 -42
t25 -25.5t25 -13t31 -3.5l35 1v-111q-32 0 -64.5 2t-62.5 13t-57 35t-48 68z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="740" 
d="M690 227q0 -56 -25.5 -101.5t-70 -77t-105.5 -48.5t-132 -17q-43 0 -85.5 10.5t-82.5 30t-75.5 48t-63.5 64.5l78 73q15 -18 36 -38.5t49.5 -38t64 -29t79.5 -11.5q41 0 81 8t71 24.5t50 42t19 60.5q0 43 -19.5 66t-50.5 33.5t-71 12.5t-80 2q-135 0 -205 48t-70 145
q0 45 20.5 87.5t57 75t87 52t110.5 19.5q85 0 159 -26t141 -92l-68 -80q-30 24 -56.5 41t-53.5 27.5t-56.5 15t-65.5 4.5q-32 0 -60 -10t-49 -27t-33 -39.5t-12 -46.5q0 -17 7 -32.5t25 -27.5t48.5 -19t78.5 -7q98 0 161.5 -15t100.5 -43.5t51.5 -70t14.5 -93.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="695" 
d="M402 636v-636h-106v636h-266v110h635v-110h-263z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="870" 
d="M435 -17q-160 0 -247.5 80t-87.5 227v456h108v-456q0 -97 59 -147.5t168 -50.5t168 50.5t59 147.5v456h108v-456q0 -147 -87.5 -227t-247.5 -80z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="810" 
d="M467 0h-129l-308 746h118l255 -624l257 624h120z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1081" 
d="M1046 746l-238 -746h-118l-144 441l-140 -441h-118l-253 746h112l198 -558l140 440l-38 118h112l188 -558l184 558h115z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="823" 
d="M645 0l-229 314l-237 -314h-139l306 405l-246 341h132l184 -249l187 249h135l-253 -341l298 -405h-138z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="714" 
d="M357 418q69 72 122 156t85 172h125q-54 -126 -123.5 -229t-154.5 -194v-323h-108v323q-85 91 -154.5 194t-123.5 229h125q32 -88 85 -172t122 -156z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="707" 
d="M50 0v110l420 526h-397v110h547v-106l-434 -531h471v-109h-607z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="527" 
d="M319 -69v877h178v-80h-80v-720h80v-77h-178z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="630" 
d="M39 745h124l416 -773h-122z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="258" 
d="M30 -69v77h80v720h-80v80h178v-877h-178z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="889" 
d="M570 435l-125 225l-126 -225h-106l187 316h89l187 -316h-106z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="700" 
d="M0 -124v89h700v-89h-700z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="297" 
d="M50 935h117l80 -122h-91z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="675" 
d="M432 172l-101 192l-123 -237q25 13 77 29t133 16h14zM244 530h111l280 -530h-113l-41 78h-62q-48 0 -95 -8t-85.5 -19.5t-65 -24t-32.5 -20.5l-3 -6h-108l248 465z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="702" 
d="M223 297q34 20 75.5 25t86.5 5h53q52 0 52 45q0 21 -14.5 32t-37.5 11h-215v-118zM223 104h215q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 29 -19 43.5t-62 14.5h-53q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-88zM622 162q0 -78 -48.5 -120t-135.5 -42h-318v518h318
q32 0 60.5 -10.5t50 -30t34 -46t12.5 -59.5q0 -26 -7.5 -49t-26.5 -40q29 -17 45 -50t16 -71z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="648" 
d="M588 58q-43 -37 -98 -55.5t-115 -18.5q-59 0 -114 18.5t-97.5 54t-68 87t-25.5 117.5t26 117t69 86t98 53t113 18q57 0 111 -16.5t97 -48.5l-69 -80q-29 20 -65.5 30.5t-73.5 10.5q-38 0 -73 -10.5t-63 -31.5t-44.5 -53.5t-16.5 -76.5t17 -76t45 -53t63.5 -31.5
t72.5 -10.5q42 0 81.5 14.5t69.5 41.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="708" 
d="M273 415v-311h103q75 0 116 41t41 116q0 76 -41 115t-116 39h-103zM55 518h321q58 0 106.5 -16.5t83 -49t53.5 -80t19 -109.5q0 -60 -18.5 -109t-53 -83t-83 -52.5t-107.5 -18.5h-211v415h-64z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="622" 
d="M222 104h325v-104h-427v518h408v-103h-306v-126q34 20 75.5 25t86.5 5h76v-103h-76q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-82z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="578" 
d="M222 0h-102v518h388v-103h-286v-126q34 20 75.5 25t86.5 5h56v-103h-56q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-186z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="673" 
d="M588 58q-43 -37 -98 -55.5t-115 -18.5q-59 0 -114 18.5t-97.5 54t-68 87t-25.5 117.5t26 117t69 86t98 53t113 18q57 0 111 -16.5t97 -48.5l-69 -80q-29 20 -65.5 30.5t-73.5 10.5q-38 0 -73 -10.5t-63 -31.5t-44.5 -53.5t-16.5 -76.5t17 -76t45 -53t63.5 -31.5
t72.5 -10.5q58 0 109 27v83h-53l-44 97h199v-237z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="726" 
d="M222 0h-102v518h102v-207q26 13 61 13q32 0 74.5 -7.5t81.5 -7.5q17 0 33 2.5t32 10.5v196h102v-518h-102v217q-15 -8 -30 -10.5t-31 -2.5q-42 0 -80 7t-76 7q-17 0 -33 -2.5t-32 -9.5v-206z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="342" 
d="M120 0v518h102v-518h-102z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="545" 
d="M84 117q14 -13 40.5 -21t69.5 -8q25 0 48 6t41 19t29 33t11 48v221h-124l-46 103h272v-324q0 -53 -19 -92t-51.5 -65.5t-76 -39.5t-92.5 -13q-17 0 -38.5 2.5t-43.5 8t-41.5 12.5t-32.5 16z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="698" 
d="M229 0h-109v518h109v-332l239 332h132l-171 -233l76 -128q23 -32 45 -39t42 -7h36v-111h-45q-20 0 -37.5 2.5t-35 10.5t-36.5 24.5t-42 44.5l-72 118z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="532" 
d="M120 0v518h107v-414h275v-104h-382z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="804" 
d="M698 435v-435h-113v303l-183 -244l-183 244v-303h-113v435l-51 83h126l221 -293l221 293h126z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="720" 
d="M184 518l313 -360v360h103v-518h-103l-288 337v-337h-103v441l-51 77h129z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="752" 
d="M574 263q0 43 -17 74.5t-45 52t-63.5 30t-72.5 9.5q-38 0 -73.5 -10t-63 -31t-44.5 -53t-17 -76t17 -76t45 -53t63.5 -31t73.5 -10q37 0 72 11.5t63 33.5t45 54.5t17 74.5zM682 264q0 -66 -26 -118t-68.5 -88t-98 -55t-114.5 -19t-114 18.5t-97.5 54t-68 87t-25.5 117.5
t26 117t69 86t98 53t113 18t113.5 -17t98 -51t68.5 -85t26 -118z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="678" 
d="M222 260q34 20 76 25t87 5h46q37 0 59.5 11.5t22.5 53.5q0 19 -7 31t-18.5 18.5t-26 8.5t-30.5 2h-209v-155zM120 0v518h329q37 0 68.5 -11.5t53.5 -33t34.5 -52t12.5 -67.5q0 -78 -49.5 -122.5t-137.5 -44.5h-46q-36 0 -59.5 -1.5t-41 -4.5t-31.5 -9t-31 -15v-157h-102z
" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="752" 
d="M433 214q30 -3 54 -17.5t55 -38.5q18 21 28.5 47t10.5 58q0 43 -18.5 74.5t-47.5 52t-65.5 30t-73.5 9.5q-38 0 -73.5 -10t-63 -31t-44.5 -53t-17 -76t17 -76t45 -53t63.5 -31t73.5 -10q34 0 67 10q-2 0 -2 1q-15 8 -30 14.5t-32 8.5zM682 264q0 -51 -14.5 -92t-40.5 -74
q13 -7 26.5 -11.5t28.5 -5.5l-53 -90q-25 2 -45 12t-41 24q-38 -20 -80.5 -31.5t-87.5 -11.5q-59 0 -114 18.5t-97.5 54t-68 87t-25.5 117.5t26 117t69 86t98 53t113 18t113.5 -17t98 -51t68.5 -85t26 -118z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="733" 
d="M530 157q9 -16 20 -25t23 -13.5t23.5 -6t20.5 -1.5h36v-111h-45q-20 0 -39.5 2t-38.5 10t-37 24.5t-36 45.5l-80 130h-7q-30 0 -50.5 -1.5t-36 -5.5t-29 -9.5t-29.5 -13.5v-182h-105v518h334q36 0 65.5 -11.5t51.5 -32.5t34 -49.5t12 -62.5q0 -60 -31 -97t-88 -54z
M454 315q23 0 40.5 12.5t17.5 37.5q0 17 -7 27t-17.5 15t-24.5 6.5t-27 1.5h-211v-130q30 17 66.5 23.5t78.5 6.5h84z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="617" 
d="M448 390q-30 25 -69.5 35t-84.5 10q-19 0 -37.5 -4.5t-33.5 -12t-24.5 -18t-9.5 -21.5q0 -12 3.5 -21.5t15 -16t34 -10.5t60.5 -4q82 0 132.5 -13t77.5 -35t36 -51t9 -60q0 -47 -20 -81.5t-55.5 -57t-83.5 -34t-103 -11.5q-69 0 -130.5 29.5t-104.5 80.5l74 75
q12 -12 25.5 -27t32 -28t43.5 -21.5t60 -8.5q72 0 116 21t44 63q0 35 -39.5 46t-120.5 11q-47 0 -86 8t-67.5 26.5t-44.5 48t-16 71.5q0 35 17.5 64t46.5 49.5t68 31.5t82 11q57 0 111 -13t111 -52z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="566" 
d="M331 415v-415h-103v415h-188v103h486v-103h-195z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="720" 
d="M359 -16q-54 0 -99.5 13t-79 39.5t-52 68t-18.5 98.5v315h103v-315q0 -33 10.5 -55t30 -35.5t46.5 -19t60 -5.5t60 5.5t46.5 19t30 35.5t10.5 55v315h103v-315q0 -57 -18.5 -98.5t-52 -68t-79 -39.5t-99.5 -13h-2z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="667" 
d="M398 0h-133l-225 518h116l175 -413l179 413h117z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="845" 
d="M316 518h117l118 -380l139 380h110l-182 -518h-139l-65 205l-53 -205h-137l-179 518h111l134 -380l68 245z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="676" 
d="M191 0h-141l220 274l-191 244h141l122 -156l125 156h137l-194 -243l216 -275h-140l-147 186z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="604" 
d="M355 209v-209h-102v209l-218 309h130l138 -204l142 204h124z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="549" 
d="M60 0v104l273 311h-263v103h399v-103l-273 -311h293v-104h-429z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="340" 
d="M310 -75q-50 0 -83.5 7.5t-54 23.5t-29.5 41t-9 61v213q0 21 -2.5 35.5t-11.5 23.5t-25.5 13t-44.5 4v59q25 0 41.5 3t25.5 11t12.5 22.5t3.5 38.5v206q0 36 9 61.5t30 41.5t54.5 23.5t83.5 7.5v-66q-50 0 -67.5 -13.5t-17.5 -52.5v-193q0 -46 -14 -76.5t-46 -44.5
q32 -14 45.5 -43t13.5 -75v-201q0 -38 18 -51.5t68 -13.5v-66z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="372" 
d="M140 -249v999h92v-999h-92z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="340" 
d="M30 -9q50 0 68 13.5t18 51.5v201q0 46 13.5 75t45.5 43q-32 14 -46 44.5t-14 76.5v193q0 39 -17.5 52.5t-67.5 13.5v66q49 0 83 -7.5t55 -23.5t30 -41.5t9 -61.5v-206q0 -24 3.5 -38.5t12.5 -22.5t25 -11t42 -3v-59q-28 0 -44.5 -4t-25.5 -13t-11.5 -23.5t-2.5 -35.5
v-213q0 -36 -9 -61t-29.5 -41t-54.5 -23.5t-83 -7.5v66z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="661" 
d="M591 284q-12 -13 -27 -28t-33 -27.5t-40 -21t-48 -8.5q-35 0 -65.5 9.5t-57.5 21.5t-51.5 21.5t-48.5 9.5q-12 0 -25 -7t-25.5 -16.5t-23 -20t-16.5 -17.5l-60 72q13 16 30.5 31.5t37 27.5t39.5 19.5t38 7.5q31 0 59.5 -9t55.5 -20.5t53.5 -20.5t53.5 -9q33 0 55 17.5
t39 39.5z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="833" 
 />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="833" 
 />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="604" 
d="M434 344q0 24 -15.5 38.5t-44.5 26.5l-170 69q-12 -8 -21 -20.5t-9 -29.5q0 -28 22.5 -45t54.5 -29l153 -58q30 16 30 48zM140 112q38 -27 74 -38.5t74 -11.5q61 0 101 18.5t40 62.5q0 27 -22.5 43t-52.5 27l-153 58q-25 9 -47.5 22.5t-39.5 32.5t-27 43t-10 56
q0 30 16.5 57t36.5 43q-34 37 -34 91q0 43 19.5 72t49.5 47t66 26t69 8q25 0 54 -5t57 -14.5t54.5 -24.5t47.5 -35l-76 -53q-32 21 -68 32.5t-67 11.5q-51 0 -79 -13t-28 -49q0 -23 19 -40t45 -27l153 -57q24 -9 46.5 -23t39 -32.5t26.5 -41.5t10 -50q0 -33 -17.5 -56.5
t-37.5 -38.5q18 -17 32.5 -44t14.5 -65q0 -41 -17.5 -72t-49 -52t-75.5 -32t-97 -11t-110.5 17t-106.5 58z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="919" 
d="M634 220q-33 -28 -75.5 -42.5t-88.5 -14.5q-45 0 -87.5 14.5t-75 42t-52 67t-19.5 89.5q0 51 20 90.5t53 66t75 40.5t87 14t86 -12.5t74 -37.5l-53 -61q-48 31 -107 31q-29 0 -56.5 -8t-48.5 -24.5t-34 -41t-13 -58.5t13 -58.5t35 -41t49 -24.5t56 -8q31 0 62 11.5
t54 31.5zM771 372q0 64 -24.5 121t-66.5 99t-99 66.5t-121 24.5q-65 0 -122 -24.5t-99 -66.5t-66.5 -99t-24.5 -121q0 -65 24.5 -121.5t67 -99t99 -67t120.5 -24.5q65 0 122 24.5t99 67t66.5 99t24.5 121.5zM849 372q0 -81 -30.5 -151.5t-83.5 -123t-123.5 -83t-151.5 -30.5
t-152 30.5t-124 83t-83.5 123t-30.5 151.5t30.5 151.5t83.5 123.5t123.5 83.5t151.5 30.5t152 -30.5t124 -83.5t83.5 -123.5t30.5 -151.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="606" 
d="M204 67l-134 196l134 193h100l-115 -193l115 -196h-100zM436 67l-134 196l134 193h100l-115 -193l115 -196h-100z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="833" 
 />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="389" 
d="M0 242v95h389v-95h-389z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="919" 
d="M580 301q14 -23 33 -29t33 -6h27v-84h-34q-15 0 -30 1t-29.5 7t-28.5 18.5t-27 35.5l-60 99h-6q-23 0 -38.5 -1.5t-27.5 -4t-22 -7t-22 -10.5v-138h-79v393h253q54 0 89 -33.5t35 -84.5q0 -45 -24 -73t-67 -42zM522 421q18 0 31 9.5t13 28.5q0 26 -18.5 32t-39.5 6h-160
v-99q23 13 51 18t59 5h64zM771 372q0 64 -24.5 121t-66.5 99t-99 66.5t-121 24.5q-65 0 -122 -24.5t-99 -66.5t-66.5 -99t-24.5 -121q0 -65 24.5 -121.5t67 -99t99 -67t120.5 -24.5q65 0 122 24.5t99 67t66.5 99t24.5 121.5zM849 372q0 -81 -30.5 -151.5t-83.5 -123
t-123.5 -83t-151.5 -30.5t-152 30.5t-124 83t-83.5 123t-30.5 151.5t30.5 151.5t83.5 123.5t123.5 83.5t151.5 30.5t152 -30.5t124 -83.5t83.5 -123.5t30.5 -151.5z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="387" 
d="M251 644q0 23 -17 40.5t-41 17.5t-40.5 -17.5t-16.5 -40.5q0 -24 17 -40.5t40 -16.5t40.5 16.5t17.5 40.5zM317 645q0 -26 -9.5 -48.5t-26.5 -39.5t-39.5 -26.5t-47.5 -9.5q-26 0 -48.5 9.5t-39.5 26.5t-26.5 39t-9.5 48t9.5 48.5t26.5 39.5t39 26.5t48 9.5t48.5 -9.5
t39.5 -26.5t26.5 -39.5t9.5 -47.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="615" 
d="M70 -93v93h475v-93h-475zM354 219v-160h-93v160h-191v93h191v156h93v-156h191v-93h-191z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="700" 
d="M503 0v54q-5 -7 -19.5 -19t-35.5 -23t-46.5 -19.5t-53.5 -8.5q-26 0 -49.5 6t-43 15.5t-33 21t-19.5 23.5v-202h-103v670h103v-315q0 -40 13 -63t33.5 -34.5t47 -14.5t53.5 -3q32 0 59 5t47 18t31 35t11 57v315h102v-518h-97z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="752" 
d="M342 650q-37 0 -67 -6t-51.5 -20t-33 -38.5t-11.5 -60.5t11.5 -60.5t33 -38.5t51.5 -20t67 -6h50v250h-50zM682 750v-750h-103v650h-81v-650h-106v300h-50q-54 0 -103.5 10.5t-87 36.5t-59.5 68.5t-22 107.5q0 66 22 109.5t59.5 70t87 37t103.5 10.5h340z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="370" 
d="M255 385q0 -29 -20.5 -50.5t-49.5 -21.5q-30 0 -50 21.5t-20 50.5t20.5 50.5t49.5 21.5q30 0 50 -21.5t20 -50.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="606" 
d="M170 67h-100l116 196l-116 193h100l135 -193zM402 67h-100l115 196l-115 193h100l134 -193z" />
    <glyph glyph-name="uni0401" unicode="&#x401;" horiz-adv-x="744" 
d="M219 109h460v-109h-569v746h525v-110h-416v-226q34 20 69 24.5t72 4.5h163v-111h-163q-36 0 -71.5 -5.5t-69.5 -23.5v-190zM210 883q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM431 883q0 25 17 42.5t42 17.5t42.5 -17.5
t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="uni0402" unicode="&#x402;" horiz-adv-x="820" 
d="M30 636v110h415v-110h-152v-177q37 18 81 30t109 12q72 0 126.5 -14t91 -47t55.5 -86t19 -131q0 -43 -16 -83t-42 -71t-61 -50t-72 -19h-79v109h46q40 0 63.5 16.5t34.5 39.5t14 47.5t3 40.5q0 80 -49.5 110.5t-137.5 30.5q-45 0 -88 -12.5t-98 -32.5v-349h-109v636h-154
z" />
    <glyph glyph-name="uni0403" unicode="&#x403;" horiz-adv-x="690" 
d="M383 813h-91l80 122h117zM219 0h-109v746h525v-110h-416v-636z" />
    <glyph glyph-name="uni0404" unicode="&#x404;" horiz-adv-x="816" 
d="M669 568q-42 42 -98 66t-120 24q-68 0 -119 -18t-87 -50.5t-57 -77.5t-28 -100q33 19 68.5 24t71.5 5h240v-111h-240q-34 0 -68 -5t-66 -21q12 -47 39.5 -86t65.5 -67t84.5 -43.5t96.5 -15.5q73 0 134 31.5t103 85.5l67 -91q-57 -63 -135.5 -99t-169.5 -36q-85 0 -158 31
t-127 85t-85 127.5t-31 157.5q0 82 27 152.5t78.5 122t126.5 80.5t170 29q85 0 158.5 -31.5t128.5 -86.5z" />
    <glyph glyph-name="uni0405" unicode="&#x405;" horiz-adv-x="740" 
d="M690 227q0 -56 -25.5 -101.5t-70 -77t-105.5 -48.5t-132 -17q-43 0 -85.5 10.5t-82.5 30t-75.5 48t-63.5 64.5l78 73q15 -18 36 -38.5t49.5 -38t64 -29t79.5 -11.5q41 0 81 8t71 24.5t50 42t19 60.5q0 43 -19.5 66t-50.5 33.5t-71 12.5t-80 2q-135 0 -205 48t-70 145
q0 45 20.5 87.5t57 75t87 52t110.5 19.5q85 0 159 -26t141 -92l-68 -80q-30 24 -56.5 41t-53.5 27.5t-56.5 15t-65.5 4.5q-32 0 -60 -10t-49 -27t-33 -39.5t-12 -46.5q0 -17 7 -32.5t25 -27.5t48.5 -19t78.5 -7q98 0 161.5 -15t100.5 -43.5t51.5 -70t14.5 -93.5z" />
    <glyph glyph-name="uni0406" unicode="&#x406;" horiz-adv-x="327" 
d="M110 0v746h107v-746h-107z" />
    <glyph glyph-name="uni0407" unicode="&#x407;" horiz-adv-x="360" 
d="M126 0v746h107v-746h-107zM10 883q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM231 883q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="uni0408" unicode="&#x408;" horiz-adv-x="675" 
d="M565 295q0 -86 -27.5 -145.5t-70.5 -96.5t-94 -53.5t-99 -16.5q-33 0 -68 6t-68.5 17.5t-63.5 27.5t-54 37l55 96q11 -12 28 -25.5t41.5 -24.5t56.5 -18.5t74 -7.5q53 0 88 20t55.5 51t29 67.5t9.5 68.5v338h-159l-48 110h315v-451z" />
    <glyph glyph-name="uni0409" unicode="&#x409;" horiz-adv-x="1243" 
d="M839 327q-37 0 -72.5 5t-69.5 25v-248h251q22 0 46.5 5t44.5 17.5t33 33.5t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-98zM588 0v636h-185q-7 -73 -20 -150.5t-34.5 -152t-53 -142t-76.5 -121.5q-20 -24 -43.5 -38t-46.5 -21t-43.5 -9t-35.5 -2h-20v109h20
q13 0 27 2.5t30 12t34 28.5t39 52q30 48 50 99.5t33 106.5t21 112t14 114h-86l-47 110h532v-278q34 -18 70 -24t72 -6h98q121 0 191 -54.5t70 -165.5q0 -67 -24 -109.5t-62 -66.5t-84.5 -33t-90.5 -9h-349z" />
    <glyph glyph-name="uni040A" unicode="&#x40a;" horiz-adv-x="1310" 
d="M657 746h109v-279q33 -17 68.5 -23t71.5 -6h98q121 0 191 -54.5t70 -165.5q0 -67 -24 -109.5t-62 -66.5t-84.5 -33t-90.5 -9h-349v346q-24 -11 -50 -14.5t-49 -3.5q-25 0 -60 3.5t-70.5 7.5t-66 7.5t-46.5 3.5q-24 0 -48 -4.5t-47 -15.5v-330h-108v746h108v-306
q23 11 47 15.5t48 4.5q16 0 46.5 -3.5t66 -7.5t70.5 -7.5t60 -3.5q23 0 49 3.5t50 14.5zM906 327q-37 0 -72 5t-68 24v-247h249q22 0 46.5 5t44.5 17.5t33 33.5t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-98z" />
    <glyph glyph-name="uni040B" unicode="&#x40b;" horiz-adv-x="701" 
d="M180 0v415h-140v103h386v-103h-144v-87q31 12 61 17t72 5q53 0 96 -9t73 -29t46 -53.5t16 -81.5q0 -23 -6.5 -53t-26.5 -58t-58.5 -47t-102.5 -19v92q48 0 70 23.5t22 61.5q0 42 -30.5 56.5t-101.5 14.5q-18 0 -32 -1t-28.5 -4t-31 -7.5t-38.5 -11.5v-224h-102z" />
    <glyph glyph-name="uni040C" unicode="&#x40c;" horiz-adv-x="835" 
d="M399 813h-91l80 122h117zM219 0h-109v746h109v-307h50q35 0 71 5.5t69 23.5l189 278h131l-245 -351l134 -201q18 -28 35 -44.5t32 -25.5t29 -11.5t27 -2.5q6 0 12 0.5t12 0.5v-111h-39q-26 0 -49.5 3t-46.5 14.5t-47 35t-50 65.5l-143 229q-26 -11 -53.5 -15t-54.5 -4
h-63v-328z" />
    <glyph glyph-name="uni040E" unicode="&#x40e;" horiz-adv-x="790" 
d="M149 746q22 -99 52 -175.5t65.5 -138.5t75.5 -113.5t81 -100.5q31 52 60.5 117.5t55.5 135.5t48 141t38 134h125q-22 -84 -48.5 -168t-60 -166.5t-74.5 -161.5t-91 -152q-23 -33 -46 -54t-47.5 -33t-50 -16.5t-52.5 -4.5q-19 0 -41.5 3.5t-44 10t-39.5 15.5t-28 19l42 95
q24 -20 47.5 -28.5t63.5 -8.5q26 0 46.5 9.5t32.5 31.5q-54 49 -103.5 109.5t-91.5 135t-76 165t-58 199.5h119zM404 871q24 0 39.5 7t23.5 18.5t11 24.5t3 25h115q-3 -23 -13 -48.5t-32 -47t-57.5 -35.5t-89.5 -14t-89.5 14t-57.5 35.5t-32 47t-13 48.5h115q0 -12 3 -25
t11 -24.5t23.5 -18.5t39.5 -7z" />
    <glyph glyph-name="uni040F" unicode="&#x40f;" horiz-adv-x="836" 
d="M372 -174v174h-262v746h108v-636h399v636h109v-746h-246v-174h-108z" />
    <glyph glyph-name="uni0410" unicode="&#x410;" horiz-adv-x="811" 
d="M302 759h120l359 -759h-122l-49 109h-227q-51 0 -90.5 -7t-67.5 -18t-45 -24t-23 -25l-19 -35h-118l323 667zM561 219l-163 315l-165 -357q20 17 66 29.5t124 12.5h138z" />
    <glyph glyph-name="_193" unicode="&#x411;" horiz-adv-x="794" 
d="M218 468q34 -18 70 -24t72 -6h128q55 0 102 -13.5t81.5 -42t53.5 -73t19 -105.5q0 -56 -22.5 -94.5t-59 -63t-82.5 -35.5t-92 -11h-378v746h504v-110h-396v-168zM218 110h281q22 0 46.5 5t44.5 17t33 33t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-128q-37 0 -72.5 5
t-69.5 25v-247z" />
    <glyph glyph-name="uni0412" unicode="&#x412;" horiz-adv-x="814" 
d="M218 408q34 20 69.5 25t72.5 5h128q51 0 81 25.5t30 77.5q0 53 -31 74t-80 21h-270v-228zM218 109h281q22 0 46.5 6.5t44.5 20t33 34t13 48.5q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-128q-36 0 -72 -6t-70 -24v-188zM744 204q0 -56 -22.5 -94.5t-59 -63t-82.5 -35.5
t-92 -11h-378v746h378q47 0 87.5 -13.5t70 -39.5t46 -64t16.5 -87q0 -48 -17 -87t-50 -60q19 -9 38 -25.5t33 -40.5t23 -55t9 -70z" />
    <glyph glyph-name="_195" unicode="&#x413;" horiz-adv-x="690" 
d="M219 0h-109v746h525v-110h-416v-636z" />
    <glyph glyph-name="_196" unicode="&#x414;" horiz-adv-x="876" 
d="M652 109v527h-185q-7 -61 -24.5 -130.5t-42.5 -139.5t-57.5 -136.5t-69.5 -120.5h379zM717 -115v115h-578v-115h-109v224h110q88 108 140 238t78 289h-82l-46 110h531v-637h65v-224h-109z" />
    <glyph glyph-name="uni0415" unicode="&#x415;" horiz-adv-x="744" 
d="M219 109h460v-109h-569v746h525v-110h-416v-226q34 20 69 24.5t72 4.5h163v-111h-163q-36 0 -71.5 -5.5t-69.5 -23.5v-190z" />
    <glyph glyph-name="_200" unicode="&#x416;" horiz-adv-x="1206" 
d="M548 746h109v-307q31 1 62 7t60 22l189 278h131l-245 -351l134 -201q18 -28 35 -44.5t32 -25.5t29 -11.5t27 -2.5q6 0 12 0.5t12 0.5l1 -111h-42q-25 0 -48.5 3t-46.5 15t-47 35.5t-49 64.5l-143 229q-26 -11 -51.5 -15t-51.5 -4v-328h-109v328q-26 0 -51.5 4t-50.5 15
l-144 -229q-27 -42 -52 -65.5t-49 -35t-48.5 -14.5t-49.5 -3h-33l1 111q5 0 11 -0.5t13 -0.5q13 0 26.5 2t28.5 10.5t32 25.5t36 46l134 201l-245 351h131l188 -278q29 -16 59.5 -22t62.5 -7v307z" />
    <glyph glyph-name="_204" unicode="&#x417;" horiz-adv-x="731" 
d="M97 650q26 26 52 47.5t57 37.5t70 24.5t91 8.5q77 0 128.5 -24t82.5 -59t44 -75t13 -72q0 -51 -15.5 -83.5t-51.5 -57.5q49 -29 76 -71t27 -108q0 -45 -16.5 -87.5t-52.5 -75.5t-94 -52.5t-141 -19.5q-44 0 -90 9.5t-89.5 27t-81.5 42.5t-66 56l67 91q20 -27 49.5 -48.5
t64 -37t72.5 -23.5t74 -8q55 0 92.5 10.5t60.5 27.5t33 40t10 49q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-124v111h124q51 0 81 25.5t30 77.5q0 31 -15.5 53t-39.5 36t-52 20.5t-52 6.5q-35 0 -62.5 -6t-51 -17.5t-44 -28t-42.5 -38.5z" />
    <glyph glyph-name="_205" unicode="&#x418;" horiz-adv-x="875" 
d="M657 746h108v-746h-108v575l-439 -575h-108v746h108v-575z" />
    <glyph glyph-name="_206" unicode="&#x419;" horiz-adv-x="875" 
d="M657 746h108v-746h-108v575l-439 -575h-108v746h108v-575zM443 871q24 0 39.5 7t23.5 18.5t11 24.5t3 25h115q-3 -23 -13 -48.5t-32 -47t-57.5 -35.5t-89.5 -14t-89.5 14t-57.5 35.5t-32 47t-13 48.5h115q0 -12 3 -25t11 -24.5t23.5 -18.5t39.5 -7z" />
    <glyph glyph-name="_207" unicode="&#x41a;" horiz-adv-x="835" 
d="M219 0h-109v746h109v-307h50q35 0 71 5.5t69 23.5l189 278h131l-245 -351l134 -201q18 -28 35 -44.5t32 -25.5t29 -11.5t27 -2.5q6 0 12 0.5t12 0.5v-111h-39q-26 0 -49.5 3t-46.5 14.5t-47 35t-50 65.5l-143 229q-26 -11 -53.5 -15t-54.5 -4h-63v-328z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="837" 
d="M618 0v636h-215q-7 -73 -20 -150.5t-34.5 -152t-53 -142t-76.5 -121.5q-20 -24 -43.5 -38t-46.5 -21t-43.5 -9t-35.5 -2h-20v109h20q13 0 27 2.5t30 12t34 28.5t39 52q30 48 50 99.5t33 106.5t21 112t14 114h-86l-47 110h562v-746h-109z" />
    <glyph glyph-name="uni041C" unicode="&#x41c;" horiz-adv-x="986" 
d="M492 208l-253 305v-513h-107v641l-87 105h141l305 -366l309 366h141l-87 -105v-641h-107v513z" />
    <glyph glyph-name="uni041D" unicode="&#x41d;" horiz-adv-x="876" 
d="M218 0h-108v746h108v-306q23 11 47 15.5t48 4.5q16 0 46.5 -3.5t66 -7.5t70.5 -7.5t60 -3.5q23 0 50 4t51 15v289h109v-746h-109v347q-24 -11 -51 -15t-50 -4q-25 0 -60 3.5t-70.5 7.5t-66 7.5t-46.5 3.5q-24 0 -48 -4.5t-47 -15.5v-330z" />
    <glyph glyph-name="uni041E" unicode="&#x41e;" horiz-adv-x="923" 
d="M755 378q0 61 -23.5 112.5t-64 89t-93.5 58t-113 20.5q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q60 0 113.5 22t93 60.5t63 90.5t23.5 113zM863 372q0 -83 -32.5 -154t-87.5 -123t-128 -82t-154 -30q-85 0 -158 31
t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5t88 122t128.5 80.5t153 29q83 0 156 -30.5t127.5 -84t86 -125.5t31.5 -156z" />
    <glyph glyph-name="_213" unicode="&#x41f;" horiz-adv-x="836" 
d="M617 0v636h-399v-636h-108v746h616v-746h-109z" />
    <glyph glyph-name="uni0420" unicode="&#x420;" horiz-adv-x="761" 
d="M493 427q110 0 110 106q0 57 -28 80t-82 23h-274v-239q34 20 69.5 25t72.5 5h132zM110 0v746h383q49 0 89.5 -14.5t69 -42t44 -67t15.5 -89.5t-15.5 -89.5t-44 -68t-69 -43.5t-89.5 -15h-132q-36 0 -72 -6t-70 -24v-287h-109z" />
    <glyph glyph-name="uni0421" unicode="&#x421;" horiz-adv-x="816" 
d="M679 568q-42 42 -98 66t-120 24q-62 0 -116 -21t-93.5 -59.5t-62 -91t-22.5 -115.5q0 -60 24 -111.5t65 -88.5t94 -58t112 -21q73 0 134 31.5t103 85.5l67 -91q-57 -63 -135.5 -99t-169.5 -36q-85 0 -158 31t-127 85t-85 127.5t-31 157.5q0 82 32.5 152.5t88 122
t128.5 80.5t153 29q85 0 158.5 -31.5t128.5 -86.5z" />
    <glyph glyph-name="uni0422" unicode="&#x422;" horiz-adv-x="695" 
d="M402 636v-636h-106v636h-266v110h635v-110h-263z" />
    <glyph glyph-name="_217" unicode="&#x423;" horiz-adv-x="770" 
d="M139 746q22 -99 52 -175.5t65.5 -138.5t75.5 -113.5t81 -100.5q31 52 60.5 117.5t55.5 135.5t48 141t38 134h125q-22 -84 -48.5 -168t-60 -166.5t-74.5 -161.5t-91 -152q-23 -33 -46 -54t-47.5 -33t-50 -16.5t-52.5 -4.5q-19 0 -41.5 3.5t-44 10t-39.5 15.5t-28 19l42 95
q24 -20 47.5 -28.5t63.5 -8.5q26 0 46.5 9.5t32.5 31.5q-54 49 -103.5 109.5t-91.5 135t-76 165t-58 199.5h119v0z" />
    <glyph glyph-name="_218" unicode="&#x424;" horiz-adv-x="1141" 
d="M786 259q39 0 72.5 14.5t59 39.5t40 59t14.5 72q0 39 -14.5 72.5t-40 59t-59 40t-72.5 14.5h-157v-401q35 21 75 25.5t82 4.5zM365 259q51 0 87 -5t70 -25v401h-157q-39 0 -72.5 -14.5t-59 -40t-40 -59t-14.5 -72.5q0 -38 14.5 -72t40 -59t59 -39.5t72.5 -14.5zM786 150
q-49 0 -86 -6.5t-71 -24.5v-119h-107v119q-39 23 -77 27t-80 4q-62 0 -117 23t-97 63t-66.5 93.5t-24.5 114.5t24.5 115t66.5 94t97 63t117 23h157v37h107v-37h157q61 0 115 -23t94 -63t63 -94t23 -115t-23 -114.5t-63 -93.5t-94 -63t-115 -23z" />
    <glyph glyph-name="uni0425" unicode="&#x425;" horiz-adv-x="823" 
d="M645 0l-229 314l-237 -314h-139l306 405l-246 341h132l184 -249l187 249h135l-253 -341l298 -405h-138z" />
    <glyph glyph-name="_220" unicode="&#x426;" horiz-adv-x="921" 
d="M871 109l-120 -266h-121l70 157h-590v746h108v-637h399v637h109v-637h145z" />
    <glyph glyph-name="_221" unicode="&#x427;" horiz-adv-x="751" 
d="M641 746v-746h-109v287q-19 -9 -38.5 -16.5t-42.5 -13.5t-49.5 -9t-59.5 -3q-72 0 -126.5 14t-91 47t-55.5 86t-19 131q0 14 1.5 41.5t4 59.5t6 65t8.5 57h109q-10 -63 -15 -126.5t-5 -126.5q0 -80 49.5 -110.5t137.5 -30.5q45 0 88 12.5t98 32.5v349h109z" />
    <glyph glyph-name="_222" unicode="&#x428;" horiz-adv-x="1145" 
d="M110 0v746h109v-637h299v607h109v-607h299v637h109v-746h-925z" />
    <glyph glyph-name="_224" unicode="&#x429;" horiz-adv-x="1231" 
d="M1181 109l-120 -266h-121l70 157h-900v746h109v-637h299v607h109v-607h299v637h109v-637h146z" />
    <glyph glyph-name="_225" unicode="&#x42a;" horiz-adv-x="913" 
d="M618 109q22 0 46.5 5t44.5 17.5t33 33.5t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-128q-37 0 -72.5 5t-69.5 25v-248h281zM229 0v636h-179v110h287v-278q34 -18 70 -24t72 -6h128q121 0 191 -54.5t70 -165.5q0 -67 -24 -109.5t-62 -66.5t-84.5 -33t-90.5 -9h-378z
" />
    <glyph glyph-name="_226" unicode="&#x42b;" horiz-adv-x="1031" 
d="M469 109q22 0 46.5 5t44.5 17.5t33 33.5t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-98q-37 0 -72.5 5t-69.5 25v-248h251zM218 468q34 -18 70 -24t72 -6h98q121 0 191 -54.5t70 -165.5q0 -67 -24 -109.5t-62 -66.5t-84.5 -33t-90.5 -9h-348v746h108v-278zM814 0v746
h107v-746h-107z" />
    <glyph glyph-name="_227" unicode="&#x42c;" horiz-adv-x="794" 
d="M360 327q-37 0 -72.5 5t-69.5 25v-248h281q22 0 46.5 5t44.5 17.5t33 33.5t13 53q0 33 -13 54t-34 33t-47.5 17t-53.5 5h-128zM110 0v746h108v-278q34 -18 70 -24t72 -6h128q121 0 191 -54.5t70 -165.5q0 -67 -24 -109.5t-62 -66.5t-84.5 -33t-90.5 -9h-378z" />
    <glyph glyph-name="_228" unicode="&#x42d;" horiz-adv-x="816" 
d="M67 650q55 55 128.5 86.5t158.5 31.5q95 0 170 -29t126.5 -80.5t78.5 -122t27 -152.5q0 -84 -31 -157.5t-85 -127.5t-127 -85t-158 -31q-91 0 -169.5 36t-135.5 99l67 91q42 -54 103 -85.5t134 -31.5q50 0 96.5 15.5t84.5 43.5t65.5 67t39.5 86q-32 16 -66 21t-68 5h-240
v111h240q36 0 71.5 -5t68.5 -24q-8 55 -29 100t-56.5 77.5t-86.5 50.5t-119 18q-64 0 -120 -24t-98 -66z" />
    <glyph glyph-name="_229" unicode="&#x42e;" horiz-adv-x="1149" 
d="M985 378q0 61 -22.5 112.5t-61.5 89t-90.5 58t-108.5 20.5q-60 0 -111.5 -20t-90 -57t-60 -88.5t-21.5 -114.5q0 -60 23.5 -112.5t62.5 -91t90.5 -60.5t107.5 -22q60 0 111.5 22t89.5 60.5t59.5 90.5t21.5 113zM1089 378q0 -85 -30 -157.5t-82.5 -125t-123 -82.5
t-151.5 -30q-75 0 -141.5 26.5t-117.5 73.5t-83.5 111.5t-40.5 139.5h-101v-334h-108v746h108v-302h103q11 72 45.5 132t86 102.5t115.5 66t135 23.5q79 0 149.5 -29.5t123 -82t83 -123.5t30.5 -155z" />
    <glyph glyph-name="_230" unicode="&#x42f;" horiz-adv-x="837" 
d="M618 636h-283q-27 0 -50 -7.5t-40 -21t-27 -33t-10 -43.5q0 -60 35 -88t92 -28h141q37 0 72.5 -5t69.5 -25v251zM618 276q-34 18 -70 24t-72 6h-65l-119 -188q-26 -42 -51 -65.5t-49.5 -35.5t-49 -14.5t-49.5 -2.5h-33v110h25q12 0 25.5 2.5t28.5 11t32 25t37 45.5
l80 117q-42 6 -76.5 23.5t-59 45t-38.5 65t-14 84.5q0 49 17.5 89.5t49 68.5t74.5 43.5t94 15.5h392v-746h-109v276z" />
    <glyph glyph-name="uni0430" unicode="&#x430;" horiz-adv-x="675" 
d="M432 172l-101 192l-123 -237q25 13 77 29t133 16h14zM244 530h111l280 -530h-113l-41 78h-62q-48 0 -95 -8t-85.5 -19.5t-65 -24t-32.5 -20.5l-3 -6h-108l248 465z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="682" 
d="M438 104q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-53q-45 0 -86.5 5t-75.5 25v-148h215zM223 357q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h53q39 0 72.5 -10.5t58 -31.5t39 -52t14.5 -71q0 -41 -14.5 -71.5t-39 -50.5t-58 -30t-72.5 -10
h-318v518h399v-103h-296v-58z" />
    <glyph glyph-name="uni0432" unicode="&#x432;" horiz-adv-x="702" 
d="M223 297q34 20 75.5 25t86.5 5h53q52 0 52 45q0 21 -14.5 32t-37.5 11h-215v-118zM223 104h215q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 29 -19 43.5t-62 14.5h-53q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-88zM622 162q0 -78 -48.5 -120t-135.5 -42h-318v518h318
q32 0 60.5 -10.5t50 -30t34 -46t12.5 -59.5q0 -26 -7.5 -49t-26.5 -40q29 -17 45 -50t16 -71z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="563" 
d="M222 415v-415h-102v518h378v-103h-276z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="736" 
d="M509 104v311h-133q-9 -41 -20 -84t-24.5 -84t-31 -78t-38.5 -65h247zM572 -115v115h-428v-115h-104v219h100q37 47 59.5 89.5t35 81t19.5 73.5t14 67h-82l-46 103h471v-414h65v-219h-104z" />
    <glyph glyph-name="uni0435" unicode="&#x435;" horiz-adv-x="622" 
d="M222 104h325v-104h-427v518h408v-103h-306v-126q34 20 75.5 25t86.5 5h76v-103h-76q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-82z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="1019" 
d="M455 518h109v-210q27 0 47 5t32 16l146 189h132l-190 -243l87 -118q23 -32 49 -42t46 -10h26v-105h-35q-21 0 -39.5 1t-37.5 8.5t-38 24t-40 46.5l-91 132q-20 -8 -42.5 -10.5t-51.5 -2.5v-199h-109v199q-29 0 -51.5 2.5t-42.5 10.5l-91 -132q-21 -30 -40 -46.5t-38 -24
t-37.5 -8.5t-39.5 -1h-35v105h26q20 0 45.5 10t49.5 42l87 118l-190 243h132l146 -189q12 -11 32 -16t47 -5v210z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="614" 
d="M104 483q26 16 53 26t53 16t49 8t41 2q106 0 162 -40t56 -119q0 -23 -8 -49.5t-27 -43.5q31 -17 46 -51.5t15 -69.5q0 -51 -21.5 -85t-56 -55t-78 -30t-88.5 -9q-33 0 -68.5 6.5t-68.5 18.5t-62.5 29.5t-50.5 38.5l59 89q45 -45 94 -61t97 -16q30 0 56 3.5t44.5 12
t29.5 23t11 37.5q0 18 -7 30t-18 18.5t-26 9t-30 2.5h-109v103h109q27 0 39.5 15.5t12.5 33.5q0 29 -29 42t-83 13q-10 0 -26 -2t-35 -6.5t-39 -11.5t-38 -17z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="746" 
d="M524 518h102v-518h-102v374l-302 -374h-102v518h102v-372z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="746" 
d="M524 518h102v-518h-102v374l-302 -374h-102v518h102v-372zM383 641q24 0 39.5 7t23.5 18.5t11 24.5t3 25h115q-3 -23 -13 -48.5t-32 -47t-57.5 -35.5t-89.5 -14t-89.5 14t-57.5 35.5t-32 47t-13 48.5h115q0 -12 3 -25t11 -24.5t23.5 -18.5t39.5 -7z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="728" 
d="M229 0h-109v518h109v-210h34q27 0 45.5 4.5t33.5 16.5l146 189h132l-190 -243l87 -118q23 -32 49 -42.5t46 -10.5h36v-104h-45q-20 0 -39 1.5t-38 9.5t-38 24.5t-40 46.5l-91 130q-20 -8 -41.5 -10.5t-42.5 -2.5h-44v-199z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="694" 
d="M127 415l-44 103h491v-518h-109v415h-145q-5 -47 -13 -98t-21.5 -100t-34 -93.5t-49.5 -78.5q-13 -15 -28 -24t-30 -13.5t-28.5 -6t-22.5 -1.5h-53v104h27q16 0 35 11t41 46q19 31 30.5 60.5t19 60.5t11.5 63.5t7 69.5h-84z" />
    <glyph glyph-name="uni043C" unicode="&#x43c;" horiz-adv-x="804" 
d="M698 435v-435h-113v303l-183 -244l-183 244v-303h-113v435l-51 83h126l221 -293l221 293h126z" />
    <glyph glyph-name="uni043D" unicode="&#x43d;" horiz-adv-x="726" 
d="M222 0h-102v518h102v-207q26 13 61 13q32 0 74.5 -7.5t81.5 -7.5q17 0 33 2.5t32 10.5v196h102v-518h-102v217q-15 -8 -30 -10.5t-31 -2.5q-42 0 -80 7t-76 7q-17 0 -33 -2.5t-32 -9.5v-206z" />
    <glyph glyph-name="uni043E" unicode="&#x43e;" horiz-adv-x="752" 
d="M574 263q0 43 -17 74.5t-45 52t-63.5 30t-72.5 9.5q-38 0 -73.5 -10t-63 -31t-44.5 -53t-17 -76t17 -76t45 -53t63.5 -31t73.5 -10q37 0 72 11.5t63 33.5t45 54.5t17 74.5zM682 264q0 -66 -26 -118t-68.5 -88t-98 -55t-114.5 -19t-114 18.5t-97.5 54t-68 87t-25.5 117.5
t26 117t69 86t98 53t113 18t113.5 -17t98 -51t68.5 -85t26 -118z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="726" 
d="M504 0v415h-282v-415h-102v518h486v-518h-102z" />
    <glyph glyph-name="uni0440" unicode="&#x440;" horiz-adv-x="678" 
d="M222 260q34 20 76 25t87 5h46q37 0 59.5 12t22.5 53q0 19 -7 30.5t-18.5 18t-26 9t-30.5 2.5h-209v-155zM120 0v518h329q37 0 68.5 -11.5t53.5 -33t34.5 -52t12.5 -67.5q0 -78 -49.5 -122.5t-137.5 -44.5h-46q-36 0 -59.5 -1.5t-41 -4.5t-31.5 -9t-31 -15v-157h-102z" />
    <glyph glyph-name="uni0441" unicode="&#x441;" horiz-adv-x="648" 
d="M588 58q-43 -37 -98 -55.5t-115 -18.5q-59 0 -114 18.5t-97.5 54t-68 87t-25.5 117.5t26 117t69 86t98 53t113 18q57 0 107.5 -14.5t93.5 -46.5l-69 -80q-29 20 -62 28.5t-70 8.5q-38 0 -73 -10.5t-63 -31.5t-44.5 -53.5t-16.5 -76.5t17 -76t45 -53t63.5 -31.5
t72.5 -10.5q42 0 81.5 14.5t69.5 41.5z" />
    <glyph glyph-name="uni0442" unicode="&#x442;" horiz-adv-x="566" 
d="M331 415v-415h-103v415h-188v103h486v-103h-195z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="627" 
d="M107 138q20 -16 46 -31t49 -15q30 0 47.5 13.5t31.5 33.5q-36 18 -74 56.5t-72 89.5t-61.5 111t-43.5 122h115q23 -94 72.5 -172t124.5 -126q15 24 32.5 57t35.5 72t35 82t31 87h111q-20 -58 -45.5 -120.5t-54.5 -122t-62 -113.5t-67 -96q-20 -24 -37.5 -38t-35.5 -22
t-38.5 -10.5t-46.5 -2.5q-16 0 -35 4t-39 11.5t-40 18t-36 22.5z" />
    <glyph glyph-name="_244" unicode="&#x444;" horiz-adv-x="883" 
d="M391 387h-105q-21 0 -39.5 -8t-32 -21.5t-21.5 -32.5t-8 -40t8 -39t21.5 -31.5t32 -21t39.5 -7.5q13 0 28 -1.5t29.5 -5t27 -8.5t20.5 -12v228zM598 186q42 2 71.5 29.5t29.5 69.5q0 21 -8 40t-21.5 32.5t-32 21.5t-39.5 8h-105v-228q7 7 19.5 12t27.5 8.5t30.5 5
t27.5 1.5zM267 92q-41 0 -77 15.5t-62.5 41.5t-42 61.5t-15.5 76.5t15.5 77t42 62.5t62.5 42t77 15.5h124v51h102v-51h123q41 0 77 -15.5t62.5 -42t42 -62.5t15.5 -77t-15.5 -76.5t-42 -61.5t-62.5 -41.5t-77 -15.5q-32 0 -65.5 -4t-57.5 -26v-62h-102v62q-24 22 -57.5 26
t-65.5 4h-1z" />
    <glyph glyph-name="uni0445" unicode="&#x445;" horiz-adv-x="676" 
d="M191 0h-141l220 274l-191 244h141l122 -156l125 156h137l-194 -243l216 -275h-140l-147 186z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="766" 
d="M706 109l-120 -266h-106l70 157h-430v518h101v-409h282v409h102v-409h101z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="646" 
d="M526 518v-518h-102v190q-31 -12 -61 -17t-72 -5q-53 0 -96 9t-73 29t-46 53.5t-16 81.5q0 45 4 89.5t16 87.5h102q-11 -39 -15.5 -75.5t-4.5 -74.5q0 -56 30.5 -77t101.5 -21q18 0 32 1t28.5 4t31 7.5t38.5 11.5v224h102z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="905" 
d="M785 518v-518h-665v518h101v-409h176v390h107v-390h179v409h102z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="946" 
d="M886 109l-120 -266h-106l70 157h-610v518h101v-409h176v391h107v-391h179v409h102v-409h101z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="774" 
d="M535 104q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-53q-45 0 -86.5 5t-75.5 25v-148h215zM320 355q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h53q39 0 72.5 -10.5t58 -30.5t39 -51t14.5 -71q0 -41 -14.5 -71.5t-39 -50.5t-58 -30t-72.5 -10
h-318v415h-157v103h260v-163z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="898" 
d="M223 104h185q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-23q-45 0 -86.5 5t-75.5 25v-148zM676 0v518h102v-518h-102zM223 355q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h23q39 0 72.5 -10.5t58 -30.5t39 -51t14.5 -71q0 -41 -14.5 -71.5
t-39 -50.5t-58 -30t-72.5 -10h-288v518h103v-163z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="677" 
d="M438 104q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-53q-45 0 -86.5 5t-75.5 25v-148h215zM223 355q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h53q39 0 72.5 -10.5t58 -30.5t39 -51t14.5 -71q0 -41 -14.5 -71.5t-39 -50.5t-58 -30t-72.5 -10
h-318v518h103v-163z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="648" 
d="M120 144q30 -27 69.5 -41.5t81.5 -14.5q60 0 111.5 26t74.5 81q-13 6 -25.5 10t-28.5 6.5t-37 3.5t-51 1h-93v103h93q42 0 80 -4.5t70 -20.5q-6 34 -24.5 60t-45 43t-58.5 25.5t-65 8.5q-37 0 -73.5 -10.5t-65.5 -30.5l-69 80q43 32 97 48.5t111 16.5q58 0 113 -18
t98 -53t69 -86t26 -117t-25.5 -117.5t-68 -87t-97.5 -54t-114 -18.5q-60 0 -115 18.5t-98 55.5z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="941" 
d="M774 264q0 43 -16 74.5t-42 52t-59 30t-66 9.5q-35 0 -68 -10t-58.5 -31t-41.5 -53.5t-16 -76.5t16 -76t42 -53t59 -31t67 -10t66.5 11.5t58.5 33.5t42 55t16 75zM871 264q0 -66 -23.5 -118t-63 -88t-91 -55t-107.5 -19q-51 0 -98 15.5t-85 45.5t-64 73t-34 99h-83v-217
h-102v518h102v-196h86q11 51 37.5 91t64 67t83 41t94.5 14q55 0 106.5 -17t91 -51t63 -85t23.5 -118z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="695" 
d="M470 414l-175 1q-17 0 -33.5 -1.5t-30.5 -8t-22.5 -19t-8.5 -33.5t8.5 -33t22.5 -18.5t30.5 -8t33.5 -1.5q55 0 98 -5t77 -25v152zM270 82q-17 -29 -34 -45.5t-36 -24.5t-40 -10t-45 -2h-45v105h53q14 0 30 9t38 43l21 33q-66 14 -99 55.5t-33 104.5q0 37 13 67.5t37 53
t56.5 35t71.5 12.5h317v-518h-105v152q-17 9 -30.5 14.5t-29 9.5t-35 6t-46.5 3z" />
    <glyph glyph-name="uni0451" unicode="&#x451;" horiz-adv-x="622" 
d="M222 104h325v-104h-427v518h408v-103h-306v-126q34 20 75.5 25t86.5 5h76v-103h-76q-36 0 -59.5 -1.5t-40.5 -4.5t-31 -9t-31 -15v-82zM163 650q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM384 650q0 25 17 42.5t42 17.5
t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="uni0452" unicode="&#x452;" horiz-adv-x="820" 
d="M184 0v636h-154v110h415v-110h-152v-177q37 18 81 30t109 12q72 0 126.5 -14t91 -47t55.5 -86t19 -131q0 -14 -1.5 -41.5t-4 -59.5t-6 -65t-8.5 -57h-109q10 63 15 126.5t5 126.5q0 80 -49.5 110.5t-137.5 30.5q-45 0 -88 -12.5t-98 -32.5v-349h-109z" />
    <glyph glyph-name="uni0453" unicode="&#x453;" horiz-adv-x="563" 
d="M318 580h-91l80 122h117zM222 415v-415h-102v518h378v-103h-276z" />
    <glyph glyph-name="uni0454" unicode="&#x454;" horiz-adv-x="618" 
d="M568 58q-43 -37 -98 -55.5t-115 -18.5q-59 0 -114 18.5t-97.5 54t-68 87t-25.5 117.5t26 117t69 86t98 53t113 18q57 0 111 -16.5t97 -48.5l-69 -80q-29 20 -65.5 30.5t-73.5 10.5q-33 0 -65 -8.5t-58.5 -25.5t-45 -43t-24.5 -60q32 16 70 20.5t80 4.5h93v-103h-93
q-30 0 -51 -1t-37 -3.5t-28.5 -6.5t-25.5 -10q11 -27 30.5 -47.5t44 -33.5t53 -19.5t58.5 -6.5q42 0 81.5 14.5t69.5 41.5z" />
    <glyph glyph-name="uni0455" unicode="&#x455;" horiz-adv-x="617" 
d="M448 390q-30 25 -69.5 35t-84.5 10q-19 0 -37.5 -4.5t-33.5 -12t-24.5 -18t-9.5 -21.5q0 -12 3.5 -21.5t15 -16t34 -10.5t60.5 -4q82 0 132.5 -13t77.5 -35t36 -51t9 -60q0 -47 -20 -81.5t-55.5 -57t-83.5 -34t-103 -11.5q-69 0 -130.5 29.5t-104.5 80.5l74 75
q12 -12 25.5 -27t32 -28t43.5 -21.5t60 -8.5q72 0 116 21t44 63q0 35 -39.5 46t-120.5 11q-47 0 -86 8t-67.5 26.5t-44.5 48t-16 71.5q0 35 17.5 64t46.5 49.5t68 31.5t82 11q57 0 111 -13t111 -52z" />
    <glyph glyph-name="uni0456" unicode="&#x456;" horiz-adv-x="342" 
d="M120 0v518h102v-518h-102z" />
    <glyph glyph-name="uni0457" unicode="&#x457;" horiz-adv-x="380" 
d="M138 0v518h102v-518h-102zM20 650q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM241 650q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42z" />
    <glyph glyph-name="uni0458" unicode="&#x458;" horiz-adv-x="545" 
d="M84 117q14 -13 40.5 -21t69.5 -8q25 0 48 6t41 19t29 33t11 48v221h-124l-46 103h272v-324q0 -53 -19 -92t-51.5 -65.5t-76 -39.5t-92.5 -13q-17 0 -38.5 2.5t-43.5 8t-41.5 12.5t-32.5 16z" />
    <glyph glyph-name="uni0459" unicode="&#x459;" horiz-adv-x="978" 
d="M544 104h185q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-23q-45 0 -86.5 5t-75.5 25v-148zM544 355q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h23q39 0 72.5 -10.5t58 -30.5t39 -51t14.5 -71q0 -41 -14.5 -71.5t-39 -50.5t-58 -30t-72.5 -10
h-294v415h-125q-5 -47 -13 -98t-21.5 -100t-34 -93.5t-49.5 -78.5q-13 -15 -28 -24t-30 -13.5t-28.5 -6t-22.5 -1.5h-53v104h27q16 0 35 11t41 46q19 31 30.5 60.5t19 60.5t11.5 63.5t7 69.5h-84l-44 103h471v-163z" />
    <glyph glyph-name="uni045A" unicode="&#x45a;" horiz-adv-x="1031" 
d="M607 104h185q16 0 30.5 2.5t25.5 9t18 18t7 30.5q0 18 -7 29.5t-18 18t-26 8.5t-30 2h-23q-45 0 -86.5 5t-75.5 25v-148zM607 355q17 -9 31 -15t31 -9t40.5 -4.5t59.5 -1.5h23q39 0 72.5 -10.5t58 -30.5t39 -51t14.5 -71q0 -41 -14.5 -71.5t-39 -50.5t-58 -30t-72.5 -10
h-288v217q-15 -8 -30 -10.5t-31 -2.5q-42 0 -80 7t-76 7q-17 0 -33 -2.5t-32 -9.5v-206h-102v518h102v-207q26 13 61 13q32 0 74.5 -7.5t81.5 -7.5q17 0 33 2.5t32 10.5v196h103v-163z" />
    <glyph glyph-name="uni045B" unicode="&#x45b;" horiz-adv-x="701" 
d="M180 0v415h-140v103h386v-103h-144v-87q31 12 61 17t72 5q53 0 96 -9t73 -29t46 -53.5t16 -81.5q0 -45 -4 -89.5t-16 -87.5h-102q11 39 15.5 75.5t4.5 74.5q0 56 -31 77t-101 21q-18 0 -32 -1t-28.5 -4t-31 -7.5t-38.5 -11.5v-224h-102z" />
    <glyph glyph-name="uni045C" unicode="&#x45c;" horiz-adv-x="728" 
d="M367 580h-91l80 122h117zM229 0h-109v518h109v-210h34q27 0 45.5 4.5t33.5 16.5l146 189h132l-190 -243l87 -118q23 -32 49 -42.5t46 -10.5h36v-104h-45q-20 0 -39 1.5t-38 9.5t-38 24.5t-40 46.5l-91 130q-20 -8 -41.5 -10.5t-42.5 -2.5h-44v-199z" />
    <glyph glyph-name="uni045E" unicode="&#x45e;" horiz-adv-x="647" 
d="M117 138q20 -16 46 -31t49 -15q30 0 47.5 13.5t31.5 33.5q-36 18 -74 56.5t-72 89.5t-61.5 111t-43.5 122h115q23 -94 72.5 -172t124.5 -126q15 24 32.5 57t35.5 72t35 82t31 87h111q-20 -58 -45.5 -120.5t-54.5 -122t-62 -113.5t-67 -96q-20 -24 -37.5 -38t-35.5 -22
t-38.5 -10.5t-46.5 -2.5q-16 0 -35 4t-39 11.5t-40 18t-36 22.5zM319 641q24 0 39.5 7t23.5 18.5t11 24.5t3 25h115q-3 -23 -13 -48.5t-32 -47t-57.5 -35.5t-89.5 -14t-89.5 14t-57.5 35.5t-32 47t-13 48.5h115q0 -12 3 -25t11 -24.5t23.5 -18.5t39.5 -7z" />
    <glyph glyph-name="uni045F" unicode="&#x45f;" horiz-adv-x="726" 
d="M316 -154v154h-196v518h102v-415h282v415h102v-518h-188v-154h-102z" />
    <glyph glyph-name="uni0490" unicode="&#x490;" horiz-adv-x="690" 
d="M635 835v-199h-416v-636h-109v746h416v89h109z" />
    <glyph glyph-name="uni0491" unicode="&#x491;" horiz-adv-x="563" 
d="M498 614v-199h-276v-415h-102v518h269v96h109z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="389" 
d="M0 242v95h389v-95h-389z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1018" 
d="M0 242v95h1018v-95h-1018z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="239" 
d="M177 597q0 -25 -17 -42.5t-42 -17.5q-33 0 -48.5 21.5t-15.5 49.5q0 22 8 46.5t23 48t36 44t47 34.5l23 -30q-35 -27 -49.5 -48.5t-14.5 -46.5q22 -3 36 -19.5t14 -39.5z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="239" 
d="M57 698q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -46t-23 -49t-36 -44.5t-47 -33.5l-23 30q35 27 49.5 48.5t14.5 46.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" 
d="M50 62q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -45.5t-23 -48t-36 -44.5t-47 -34l-23 30q35 27 49.5 48.5t14.5 45.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="397" 
d="M339 604q0 -25 -17 -42.5t-42 -17.5q-33 0 -48.5 21.5t-15.5 49.5q0 22 8 46.5t23 48t36 44t47 34.5l23 -30q-35 -27 -49.5 -48.5t-14.5 -46.5q22 -3 36 -19.5t14 -39.5zM179 604q0 -25 -17 -42.5t-42 -17.5q-33 0 -48.5 21.5t-15.5 49.5q0 22 8 46.5t23 48t36 44
t47 34.5l23 -30q-35 -27 -49.5 -48.5t-14.5 -46.5q22 -3 36 -19.5t14 -39.5z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="397" 
d="M60 699q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -46t-23 -49t-36 -44.5t-47 -33.5l-23 30q35 27 49.5 48.5t14.5 46.5q-22 3 -36 19.5t-14 39.5zM220 699q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -46t-23 -49t-36 -44.5t-47 -33.5
l-23 30q35 27 49.5 48.5t14.5 46.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="377" 
d="M50 62q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -45.5t-23 -48t-36 -44.5t-47 -34l-23 30q35 27 49.5 48.5t14.5 45.5q-22 3 -36 19.5t-14 39.5zM210 62q0 25 17 42.5t42 17.5q33 0 48.5 -21.5t15.5 -49.5q0 -21 -8 -45.5t-23 -48t-36 -44.5t-47 -34
l-23 30q35 27 49.5 48.5t14.5 45.5q-22 3 -36 19.5t-14 39.5z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="593" 
d="M346 448v-448h-99v448h-189v94h189v203h99v-203h188v-94h-188z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="593" 
d="M346 191v-191h-99v191h-189v94h189v179h-189v94h189v187h99v-187h188v-94h-188v-179h188v-94h-188z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="382" 
d="M70 411q0 25 9.5 47t26 38.5t38.5 26t47 9.5t47 -9.5t38.5 -26t26 -38.5t9.5 -47t-9.5 -47t-26 -38.5t-38.5 -26t-47 -9.5t-47 9.5t-38.5 26t-26 38.5t-9.5 47z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="575" 
d="M50 62q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM228 62q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17t-17 42zM406 62q0 25 17 42.5t42 17.5t42.5 -17.5t17.5 -42.5t-17.5 -42t-42.5 -17t-42 17
t-17 42z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1522" 
d="M1388 162q0 29 -11.5 50t-29.5 34.5t-41.5 20t-47.5 6.5q-25 0 -48.5 -7t-42 -21t-29.5 -35.5t-11 -49.5q0 -29 11.5 -50t30 -35t42 -20.5t47.5 -6.5t47.5 7.5t42 22.5t29.5 36t11 48zM1477 163q0 -47 -18.5 -84.5t-49.5 -63t-71 -39t-82 -13.5t-81.5 13.5t-70 39
t-48.5 62t-18 83.5q0 48 18.5 84.5t49.5 62t70.5 38.5t81.5 13t81 -12t70 -36.5t49.5 -61.5t18.5 -86zM890 159q0 29 -11.5 50t-29.5 34.5t-41.5 20t-47.5 6.5q-25 0 -48.5 -7t-42 -21t-29.5 -35.5t-11 -49.5q0 -29 11.5 -50t30 -35t42 -20.5t47.5 -6.5t47.5 7.5t42 22.5
t29.5 36t11 48zM979 160q0 -47 -18.5 -84.5t-49.5 -63t-71 -39t-82 -13.5t-81.5 13.5t-70 39t-48.5 62t-18 83.5q0 48 18.5 84.5t49.5 62t70.5 38.5t81.5 13t81 -12t70 -36.5t49.5 -61.5t18.5 -86zM400 514q0 29 -11.5 50t-29.5 34.5t-41.5 20t-47.5 6.5q-25 0 -48.5 -7
t-42 -21t-29.5 -35.5t-11 -49.5q0 -29 11.5 -50t30 -35t42 -20.5t47.5 -6.5t47.5 7.5t42 22.5t29.5 36t11 48zM489 515q0 -47 -18.5 -84.5t-49.5 -63.5t-71 -39.5t-82 -13.5t-81.5 13.5t-70 39t-48.5 62.5t-18 84q0 48 18.5 84.5t49.5 62t70.5 38.5t81.5 13t81 -12t70 -36.5
t49.5 -61.5t18.5 -86zM304 -39h-116l541 757h117z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="374" 
d="M70 263l134 193h100l-115 -193l115 -196h-100z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="374" 
d="M170 67h-100l115 196l-115 193h100l134 -193z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="855" 
d="M624 466l-14 -73h-403q-1 -5 -1 -11v-11q0 -24 3 -45h388l-14 -72h-351q18 -37 46 -67t63.5 -51t76 -32.5t83.5 -11.5q73 0 134 31.5t103 85.5l67 -91q-57 -63 -135.5 -99t-169.5 -36q-68 0 -128.5 20t-110 55.5t-86 85.5t-56.5 110h-78l14 72h48q-2 14 -3 28.5t-1 29.5
v9h-59l15 73h53q15 66 52 121.5t89.5 95.5t117 62.5t134.5 22.5q85 0 158.5 -31.5t128.5 -86.5l-70 -82q-42 42 -98 66t-120 24q-50 0 -94.5 -13.5t-81 -39t-63.5 -61t-41 -78.5h404z" />
    <glyph glyph-name="uni20BD" unicode="&#x20bd;" horiz-adv-x="840" 
d="M572 427q110 0 110 106q0 57 -28 80.5t-82 23.5h-274v-210h274zM110 427h79v319h383q49 0 89.5 -14.5t69 -42t44 -67t15.5 -89.5t-15.5 -89.5t-44 -68t-69 -43.5t-89.5 -15h-274v-70h256v-110h-256v-137h-109v137h-79v110h79v70h-79v110z" />
    <glyph glyph-name="uni2116" unicode="&#x2116;" horiz-adv-x="1128" 
d="M1032 397q0 23 -17 40.5t-41 17.5t-40.5 -17.5t-16.5 -40.5q0 -24 17 -40.5t40 -16.5t40.5 16.5t17.5 40.5zM857 123v68h239v-68h-239zM1098 398q0 -26 -9.5 -48.5t-26.5 -39.5t-39.5 -26.5t-47.5 -9.5q-26 0 -48.5 9.5t-39.5 26.5t-26.5 39t-9.5 48t9.5 48.5t26.5 39.5
t39 26.5t48 9.5t48.5 -9.5t39.5 -26.5t26.5 -39.5t9.5 -47.5zM45 746h141l479 -570v570h249v-95h-139v-651h-108l-428 513v-513h-107v641z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="851" 
d="M750 699v-244h-65v171l-106 -137l-106 137v-171h-65v244l-31 47h72l130 -164l129 164h73zM239 688v-233h-60v233h-109v58h283v-58h-114z" />
    <hkern u1="&#x32;" u2="&#x34;" k="19" />
    <hkern u1="&#x32;" u2="&#x33;" k="29" />
    <hkern u1="&#x34;" u2="&#x32;" k="19" />
    <hkern u1="&#x37;" u2="&#x35;" k="19" />
    <hkern u1="&#x37;" u2="&#x34;" k="69" />
    <hkern u1="&#x38;" u2="&#x39;" k="9" />
    <hkern u1="A" u2="y" k="39" />
    <hkern u1="A" u2="g" k="19" />
    <hkern u1="A" u2="d" k="29" />
    <hkern u1="A" u2="c" k="29" />
    <hkern u1="A" u2="a" k="-20" />
    <hkern u1="A" u2="Y" k="39" />
    <hkern u1="A" u2="V" k="69" />
    <hkern u1="A" u2="U" k="9" />
    <hkern u1="A" u2="T" k="69" />
    <hkern u1="A" u2="Q" k="49" />
    <hkern u1="A" u2="O" k="39" />
    <hkern u1="A" u2="G" k="59" />
    <hkern u1="A" u2="D" k="29" />
    <hkern u1="A" u2="C" k="39" />
    <hkern u1="A" u2="A" k="-60" />
    <hkern u1="B" u2="a" k="19" />
    <hkern u1="B" u2="Y" k="19" />
    <hkern u1="B" u2="W" k="29" />
    <hkern u1="B" u2="V" k="49" />
    <hkern u1="C" u2="a" k="-10" />
    <hkern u1="C" u2="Z" k="-30" />
    <hkern u1="C" u2="W" k="-10" />
    <hkern u1="D" u2="A" k="39" />
    <hkern u1="E" u2="Q" k="39" />
    <hkern u1="E" u2="O" k="49" />
    <hkern u1="F" u2="y" k="29" />
    <hkern u1="F" u2="x" k="69" />
    <hkern u1="F" u2="v" k="39" />
    <hkern u1="F" u2="u" k="69" />
    <hkern u1="F" u2="t" k="39" />
    <hkern u1="F" u2="s" k="39" />
    <hkern u1="F" u2="r" k="39" />
    <hkern u1="F" u2="m" k="29" />
    <hkern u1="F" u2="l" k="19" />
    <hkern u1="F" u2="k" k="59" />
    <hkern u1="F" u2="j" k="29" />
    <hkern u1="F" u2="i" k="49" />
    <hkern u1="F" u2="h" k="49" />
    <hkern u1="F" u2="g" k="59" />
    <hkern u1="F" u2="f" k="59" />
    <hkern u1="F" u2="e" k="49" />
    <hkern u1="F" u2="d" k="49" />
    <hkern u1="F" u2="c" k="89" />
    <hkern u1="F" u2="b" k="69" />
    <hkern u1="F" u2="a" k="99" />
    <hkern u1="F" u2="O" k="39" />
    <hkern u1="F" u2="J" k="69" />
    <hkern u1="F" u2="G" k="19" />
    <hkern u1="F" u2="A" k="99" />
    <hkern u1="G" u2="z" k="-20" />
    <hkern u1="I" u2="J" k="39" />
    <hkern u1="J" u2="X" k="29" />
    <hkern u1="J" u2="V" k="19" />
    <hkern u1="J" u2="Q" k="19" />
    <hkern u1="J" u2="O" k="19" />
    <hkern u1="K" u2="y" k="29" />
    <hkern u1="K" u2="Z" k="-10" />
    <hkern u1="K" u2="V" k="39" />
    <hkern u1="K" u2="U" k="19" />
    <hkern u1="K" u2="G" k="59" />
    <hkern u1="L" u2="z" k="-30" />
    <hkern u1="L" u2="y" k="29" />
    <hkern u1="L" u2="j" k="-30" />
    <hkern u1="L" u2="a" k="-30" />
    <hkern u1="L" u2="Z" k="-20" />
    <hkern u1="L" u2="Y" k="109" />
    <hkern u1="L" u2="W" k="59" />
    <hkern u1="L" u2="V" k="109" />
    <hkern u1="L" u2="O" k="29" />
    <hkern u1="L" u2="G" k="19" />
    <hkern u1="L" u2="A" k="-20" />
    <hkern u1="O" u2="j" k="19" />
    <hkern u1="O" u2="a" k="29" />
    <hkern u1="O" u2="Y" k="19" />
    <hkern u1="O" u2="X" k="29" />
    <hkern u1="O" u2="S" k="19" />
    <hkern u1="O" u2="N" k="19" />
    <hkern u1="O" u2="J" k="69" />
    <hkern u1="O" u2="A" k="39" />
    <hkern u1="P" u2="x" k="29" />
    <hkern u1="P" u2="u" k="9" />
    <hkern u1="P" u2="s" k="29" />
    <hkern u1="P" u2="r" k="29" />
    <hkern u1="P" u2="q" k="39" />
    <hkern u1="P" u2="p" k="29" />
    <hkern u1="P" u2="o" k="29" />
    <hkern u1="P" u2="l" k="29" />
    <hkern u1="P" u2="k" k="29" />
    <hkern u1="P" u2="j" k="69" />
    <hkern u1="P" u2="i" k="29" />
    <hkern u1="P" u2="h" k="19" />
    <hkern u1="P" u2="g" k="29" />
    <hkern u1="P" u2="f" k="19" />
    <hkern u1="P" u2="e" k="29" />
    <hkern u1="P" u2="c" k="19" />
    <hkern u1="P" u2="a" k="59" />
    <hkern u1="P" u2="Y" k="9" />
    <hkern u1="P" u2="X" k="49" />
    <hkern u1="P" u2="V" k="19" />
    <hkern u1="P" u2="U" k="9" />
    <hkern u1="P" u2="J" k="99" />
    <hkern u1="P" u2="A" k="99" />
    <hkern u1="Q" u2="Y" k="29" />
    <hkern u1="Q" u2="W" k="19" />
    <hkern u1="Q" u2="V" k="49" />
    <hkern u1="Q" u2="T" k="49" />
    <hkern u1="R" u2="a" k="-20" />
    <hkern u1="R" u2="Y" k="19" />
    <hkern u1="R" u2="X" k="29" />
    <hkern u1="R" u2="W" k="19" />
    <hkern u1="R" u2="V" k="49" />
    <hkern u1="R" u2="U" k="29" />
    <hkern u1="R" u2="T" k="39" />
    <hkern u1="R" u2="Q" k="9" />
    <hkern u1="S" u2="Z" k="-10" />
    <hkern u1="S" u2="Y" k="9" />
    <hkern u1="S" u2="X" k="29" />
    <hkern u1="S" u2="A" k="-20" />
    <hkern u1="T" u2="z" k="59" />
    <hkern u1="T" u2="y" k="59" />
    <hkern u1="T" u2="x" k="59" />
    <hkern u1="T" u2="w" k="59" />
    <hkern u1="T" u2="v" k="79" />
    <hkern u1="T" u2="u" k="59" />
    <hkern u1="T" u2="t" k="39" />
    <hkern u1="T" u2="s" k="69" />
    <hkern u1="T" u2="r" k="89" />
    <hkern u1="T" u2="q" k="119" />
    <hkern u1="T" u2="p" k="39" />
    <hkern u1="T" u2="o" k="59" />
    <hkern u1="T" u2="n" k="29" />
    <hkern u1="T" u2="m" k="39" />
    <hkern u1="T" u2="l" k="49" />
    <hkern u1="T" u2="k" k="39" />
    <hkern u1="T" u2="j" k="69" />
    <hkern u1="T" u2="i" k="39" />
    <hkern u1="T" u2="h" k="29" />
    <hkern u1="T" u2="g" k="79" />
    <hkern u1="T" u2="f" k="39" />
    <hkern u1="T" u2="e" k="39" />
    <hkern u1="T" u2="d" k="29" />
    <hkern u1="T" u2="c" k="39" />
    <hkern u1="T" u2="b" k="29" />
    <hkern u1="T" u2="a" k="69" />
    <hkern u1="T" u2="X" k="29" />
    <hkern u1="T" u2="W" k="-10" />
    <hkern u1="T" u2="V" k="-10" />
    <hkern u1="T" u2="U" k="19" />
    <hkern u1="T" u2="T" k="-10" />
    <hkern u1="T" u2="Q" k="9" />
    <hkern u1="T" u2="J" k="99" />
    <hkern u1="T" u2="A" k="29" />
    <hkern u1="U" u2="x" k="29" />
    <hkern u1="U" u2="w" k="19" />
    <hkern u1="U" u2="u" k="19" />
    <hkern u1="U" u2="t" k="19" />
    <hkern u1="U" u2="s" k="19" />
    <hkern u1="U" u2="q" k="9" />
    <hkern u1="U" u2="p" k="9" />
    <hkern u1="U" u2="o" k="19" />
    <hkern u1="U" u2="n" k="9" />
    <hkern u1="U" u2="m" k="29" />
    <hkern u1="U" u2="l" k="19" />
    <hkern u1="U" u2="k" k="9" />
    <hkern u1="U" u2="j" k="19" />
    <hkern u1="U" u2="h" k="9" />
    <hkern u1="U" u2="g" k="9" />
    <hkern u1="U" u2="f" k="9" />
    <hkern u1="U" u2="c" k="19" />
    <hkern u1="U" u2="a" k="59" />
    <hkern u1="U" u2="X" k="29" />
    <hkern u1="U" u2="T" k="19" />
    <hkern u1="U" u2="J" k="39" />
    <hkern u1="U" u2="A" k="29" />
    <hkern u1="V" u2="z" k="109" />
    <hkern u1="V" u2="y" k="59" />
    <hkern u1="V" u2="x" k="99" />
    <hkern u1="V" u2="w" k="59" />
    <hkern u1="V" u2="v" k="49" />
    <hkern u1="V" u2="u" k="79" />
    <hkern u1="V" u2="t" k="69" />
    <hkern u1="V" u2="s" k="99" />
    <hkern u1="V" u2="r" k="99" />
    <hkern u1="V" u2="q" k="99" />
    <hkern u1="V" u2="p" k="49" />
    <hkern u1="V" u2="o" k="99" />
    <hkern u1="V" u2="n" k="59" />
    <hkern u1="V" u2="m" k="29" />
    <hkern u1="V" u2="l" k="59" />
    <hkern u1="V" u2="k" k="79" />
    <hkern u1="V" u2="j" k="99" />
    <hkern u1="V" u2="i" k="49" />
    <hkern u1="V" u2="h" k="39" />
    <hkern u1="V" u2="g" k="59" />
    <hkern u1="V" u2="f" k="19" />
    <hkern u1="V" u2="c" k="59" />
    <hkern u1="V" u2="b" k="49" />
    <hkern u1="V" u2="a" k="89" />
    <hkern u1="V" u2="Z" k="19" />
    <hkern u1="V" u2="Y" k="-10" />
    <hkern u1="V" u2="X" k="19" />
    <hkern u1="V" u2="T" k="-10" />
    <hkern u1="V" u2="R" k="19" />
    <hkern u1="V" u2="Q" k="39" />
    <hkern u1="V" u2="P" k="19" />
    <hkern u1="V" u2="L" k="29" />
    <hkern u1="V" u2="K" k="19" />
    <hkern u1="V" u2="J" k="79" />
    <hkern u1="V" u2="G" k="39" />
    <hkern u1="V" u2="B" k="19" />
    <hkern u1="V" u2="A" k="49" />
    <hkern u1="W" u2="z" k="19" />
    <hkern u1="W" u2="y" k="39" />
    <hkern u1="W" u2="x" k="49" />
    <hkern u1="W" u2="w" k="29" />
    <hkern u1="W" u2="v" k="39" />
    <hkern u1="W" u2="u" k="39" />
    <hkern u1="W" u2="t" k="19" />
    <hkern u1="W" u2="s" k="39" />
    <hkern u1="W" u2="r" k="49" />
    <hkern u1="W" u2="q" k="49" />
    <hkern u1="W" u2="p" k="49" />
    <hkern u1="W" u2="o" k="49" />
    <hkern u1="W" u2="n" k="29" />
    <hkern u1="W" u2="m" k="19" />
    <hkern u1="W" u2="l" k="29" />
    <hkern u1="W" u2="k" k="29" />
    <hkern u1="W" u2="j" k="49" />
    <hkern u1="W" u2="i" k="29" />
    <hkern u1="W" u2="h" k="39" />
    <hkern u1="W" u2="g" k="49" />
    <hkern u1="W" u2="f" k="29" />
    <hkern u1="W" u2="e" k="39" />
    <hkern u1="W" u2="c" k="39" />
    <hkern u1="W" u2="b" k="39" />
    <hkern u1="W" u2="a" k="49" />
    <hkern u1="W" u2="X" k="39" />
    <hkern u1="W" u2="Q" k="29" />
    <hkern u1="W" u2="J" k="59" />
    <hkern u1="W" u2="C" k="19" />
    <hkern u1="X" u2="y" k="69" />
    <hkern u1="X" u2="w" k="49" />
    <hkern u1="X" u2="v" k="39" />
    <hkern u1="X" u2="u" k="29" />
    <hkern u1="X" u2="t" k="29" />
    <hkern u1="X" u2="s" k="19" />
    <hkern u1="X" u2="r" k="29" />
    <hkern u1="X" u2="q" k="49" />
    <hkern u1="X" u2="p" k="19" />
    <hkern u1="X" u2="o" k="39" />
    <hkern u1="X" u2="m" k="19" />
    <hkern u1="X" u2="h" k="19" />
    <hkern u1="X" u2="g" k="39" />
    <hkern u1="X" u2="f" k="29" />
    <hkern u1="X" u2="e" k="29" />
    <hkern u1="X" u2="d" k="39" />
    <hkern u1="X" u2="c" k="39" />
    <hkern u1="X" u2="b" k="9" />
    <hkern u1="X" u2="Y" k="9" />
    <hkern u1="X" u2="W" k="39" />
    <hkern u1="X" u2="V" k="29" />
    <hkern u1="X" u2="U" k="29" />
    <hkern u1="X" u2="T" k="19" />
    <hkern u1="X" u2="S" k="9" />
    <hkern u1="X" u2="Q" k="39" />
    <hkern u1="X" u2="O" k="29" />
    <hkern u1="X" u2="K" k="9" />
    <hkern u1="X" u2="J" k="-10" />
    <hkern u1="X" u2="G" k="29" />
    <hkern u1="X" u2="C" k="19" />
    <hkern u1="Y" u2="z" k="49" />
    <hkern u1="Y" u2="y" k="59" />
    <hkern u1="Y" u2="x" k="59" />
    <hkern u1="Y" u2="w" k="49" />
    <hkern u1="Y" u2="v" k="59" />
    <hkern u1="Y" u2="u" k="59" />
    <hkern u1="Y" u2="t" k="59" />
    <hkern u1="Y" u2="s" k="59" />
    <hkern u1="Y" u2="r" k="89" />
    <hkern u1="Y" u2="q" k="69" />
    <hkern u1="Y" u2="p" k="29" />
    <hkern u1="Y" u2="o" k="59" />
    <hkern u1="Y" u2="n" k="49" />
    <hkern u1="Y" u2="m" k="39" />
    <hkern u1="Y" u2="l" k="69" />
    <hkern u1="Y" u2="k" k="29" />
    <hkern u1="Y" u2="j" k="79" />
    <hkern u1="Y" u2="i" k="59" />
    <hkern u1="Y" u2="h" k="49" />
    <hkern u1="Y" u2="g" k="79" />
    <hkern u1="Y" u2="f" k="19" />
    <hkern u1="Y" u2="e" k="39" />
    <hkern u1="Y" u2="d" k="19" />
    <hkern u1="Y" u2="c" k="59" />
    <hkern u1="Y" u2="b" k="49" />
    <hkern u1="Y" u2="a" k="99" />
    <hkern u1="Y" u2="X" k="19" />
    <hkern u1="Y" u2="V" k="-10" />
    <hkern u1="Y" u2="S" k="19" />
    <hkern u1="Y" u2="Q" k="39" />
    <hkern u1="Y" u2="O" k="19" />
    <hkern u1="Y" u2="J" k="139" />
    <hkern u1="Y" u2="G" k="19" />
    <hkern u1="Y" u2="C" k="39" />
    <hkern u1="Y" u2="A" k="49" />
    <hkern u1="Z" u2="W" k="39" />
    <hkern u1="Z" u2="V" k="19" />
    <hkern u1="Z" u2="K" k="-10" />
    <hkern u1="a" u2="y" k="79" />
    <hkern u1="a" u2="x" k="9" />
    <hkern u1="a" u2="w" k="19" />
    <hkern u1="a" u2="v" k="49" />
    <hkern u1="a" u2="u" k="69" />
    <hkern u1="a" u2="t" k="59" />
    <hkern u1="a" u2="r" k="49" />
    <hkern u1="a" u2="q" k="29" />
    <hkern u1="a" u2="p" k="19" />
    <hkern u1="a" u2="o" k="19" />
    <hkern u1="a" u2="l" k="19" />
    <hkern u1="a" u2="k" k="29" />
    <hkern u1="a" u2="i" k="29" />
    <hkern u1="a" u2="g" k="39" />
    <hkern u1="a" u2="e" k="29" />
    <hkern u1="a" u2="d" k="49" />
    <hkern u1="a" u2="c" k="19" />
    <hkern u1="a" u2="a" k="-10" />
    <hkern u1="b" u2="z" k="-10" />
    <hkern u1="b" u2="x" k="29" />
    <hkern u1="b" u2="w" k="14" />
    <hkern u1="b" u2="v" k="9" />
    <hkern u1="b" u2="u" k="19" />
    <hkern u1="b" u2="t" k="29" />
    <hkern u1="b" u2="q" k="9" />
    <hkern u1="b" u2="o" k="9" />
    <hkern u1="b" u2="m" k="-10" />
    <hkern u1="b" u2="l" k="19" />
    <hkern u1="b" u2="k" k="9" />
    <hkern u1="b" u2="h" k="9" />
    <hkern u1="b" u2="d" k="19" />
    <hkern u1="c" u2="z" k="-10" />
    <hkern u1="c" u2="y" k="19" />
    <hkern u1="c" u2="t" k="-10" />
    <hkern u1="c" u2="s" k="-20" />
    <hkern u1="d" u2="y" k="39" />
    <hkern u1="d" u2="x" k="49" />
    <hkern u1="d" u2="w" k="29" />
    <hkern u1="d" u2="v" k="39" />
    <hkern u1="d" u2="u" k="19" />
    <hkern u1="d" u2="j" k="39" />
    <hkern u1="d" u2="b" k="9" />
    <hkern u1="d" u2="a" k="19" />
    <hkern u1="e" u2="z" k="-40" />
    <hkern u1="e" u2="y" k="19" />
    <hkern u1="e" u2="x" k="19" />
    <hkern u1="e" u2="n" k="-10" />
    <hkern u1="e" u2="m" k="-20" />
    <hkern u1="f" u2="z" k="-10" />
    <hkern u1="f" u2="x" k="19" />
    <hkern u1="f" u2="u" k="9" />
    <hkern u1="f" u2="s" k="9" />
    <hkern u1="f" u2="q" k="9" />
    <hkern u1="f" u2="o" k="9" />
    <hkern u1="f" u2="n" k="-10" />
    <hkern u1="f" u2="l" k="19" />
    <hkern u1="f" u2="k" k="9" />
    <hkern u1="f" u2="j" k="19" />
    <hkern u1="f" u2="h" k="19" />
    <hkern u1="f" u2="g" k="29" />
    <hkern u1="f" u2="a" k="59" />
    <hkern u1="g" u2="w" k="-10" />
    <hkern u1="g" u2="v" k="9" />
    <hkern u1="g" u2="p" k="9" />
    <hkern u1="h" u2="q" k="9" />
    <hkern u1="h" u2="o" k="9" />
    <hkern u1="h" u2="b" k="19" />
    <hkern u1="i" u2="z" k="-10" />
    <hkern u1="i" u2="y" k="9" />
    <hkern u1="i" u2="x" k="19" />
    <hkern u1="i" u2="u" k="9" />
    <hkern u1="i" u2="c" k="19" />
    <hkern u1="i" u2="a" k="29" />
    <hkern u1="j" u2="y" k="39" />
    <hkern u1="j" u2="x" k="49" />
    <hkern u1="j" u2="w" k="29" />
    <hkern u1="j" u2="v" k="39" />
    <hkern u1="j" u2="u" k="29" />
    <hkern u1="j" u2="s" k="9" />
    <hkern u1="j" u2="r" k="19" />
    <hkern u1="j" u2="q" k="19" />
    <hkern u1="j" u2="o" k="19" />
    <hkern u1="j" u2="l" k="19" />
    <hkern u1="j" u2="k" k="9" />
    <hkern u1="j" u2="g" k="9" />
    <hkern u1="j" u2="b" k="29" />
    <hkern u1="j" u2="a" k="49" />
    <hkern u1="k" u2="z" k="-20" />
    <hkern u1="k" u2="y" k="9" />
    <hkern u1="k" u2="w" k="29" />
    <hkern u1="k" u2="t" k="19" />
    <hkern u1="k" u2="q" k="19" />
    <hkern u1="k" u2="d" k="9" />
    <hkern u1="l" u2="z" k="-20" />
    <hkern u1="l" u2="y" k="79" />
    <hkern u1="l" u2="w" k="9" />
    <hkern u1="l" u2="v" k="19" />
    <hkern u1="l" u2="t" k="29" />
    <hkern u1="l" u2="q" k="19" />
    <hkern u1="l" u2="n" k="-10" />
    <hkern u1="l" u2="j" k="-10" />
    <hkern u1="l" u2="e" k="-10" />
    <hkern u1="l" u2="d" k="19" />
    <hkern u1="l" u2="b" k="-10" />
    <hkern u1="l" u2="a" k="-10" />
    <hkern u1="m" u2="y" k="9" />
    <hkern u1="m" u2="w" k="-10" />
    <hkern u1="m" u2="q" k="9" />
    <hkern u1="m" u2="o" k="9" />
    <hkern u1="m" u2="f" k="-10" />
    <hkern u1="m" u2="e" k="-10" />
    <hkern u1="m" u2="d" k="-10" />
    <hkern u1="m" u2="b" k="-10" />
    <hkern u1="n" u2="y" k="29" />
    <hkern u1="n" u2="v" k="39" />
    <hkern u1="n" u2="u" k="29" />
    <hkern u1="n" u2="t" k="29" />
    <hkern u1="n" u2="q" k="19" />
    <hkern u1="n" u2="o" k="9" />
    <hkern u1="n" u2="g" k="19" />
    <hkern u1="o" u2="y" k="19" />
    <hkern u1="o" u2="x" k="29" />
    <hkern u1="o" u2="v" k="19" />
    <hkern u1="o" u2="u" k="19" />
    <hkern u1="o" u2="t" k="19" />
    <hkern u1="o" u2="m" k="9" />
    <hkern u1="o" u2="l" k="19" />
    <hkern u1="o" u2="i" k="19" />
    <hkern u1="o" u2="f" k="9" />
    <hkern u1="o" u2="b" k="9" />
    <hkern u1="o" u2="a" k="19" />
    <hkern u1="p" u2="z" k="-10" />
    <hkern u1="p" u2="y" k="9" />
    <hkern u1="p" u2="x" k="49" />
    <hkern u1="p" u2="v" k="19" />
    <hkern u1="p" u2="l" k="19" />
    <hkern u1="p" u2="j" k="49" />
    <hkern u1="p" u2="h" k="19" />
    <hkern u1="p" u2="g" k="19" />
    <hkern u1="p" u2="e" k="19" />
    <hkern u1="p" u2="d" k="9" />
    <hkern u1="p" u2="a" k="69" />
    <hkern u1="q" u2="z" k="-10" />
    <hkern u1="q" u2="y" k="19" />
    <hkern u1="q" u2="w" k="9" />
    <hkern u1="q" u2="v" k="19" />
    <hkern u1="q" u2="u" k="19" />
    <hkern u1="q" u2="r" k="1" />
    <hkern u1="r" u2="z" k="-10" />
    <hkern u1="r" u2="y" k="49" />
    <hkern u1="r" u2="x" k="19" />
    <hkern u1="r" u2="w" k="29" />
    <hkern u1="r" u2="v" k="49" />
    <hkern u1="r" u2="u" k="29" />
    <hkern u1="r" u2="t" k="29" />
    <hkern u1="r" u2="s" k="19" />
    <hkern u1="r" u2="c" k="19" />
    <hkern u1="s" u2="z" k="-10" />
    <hkern u1="s" u2="y" k="39" />
    <hkern u1="s" u2="v" k="19" />
    <hkern u1="s" u2="u" k="4" />
    <hkern u1="s" u2="j" k="19" />
    <hkern u1="s" u2="a" k="29" />
    <hkern u1="t" u2="z" k="-20" />
    <hkern u1="t" u2="x" k="19" />
    <hkern u1="t" u2="v" k="-10" />
    <hkern u1="t" u2="u" k="49" />
    <hkern u1="t" u2="t" k="-10" />
    <hkern u1="t" u2="r" k="19" />
    <hkern u1="t" u2="j" k="49" />
    <hkern u1="t" u2="b" k="9" />
    <hkern u1="t" u2="a" k="29" />
    <hkern u1="u" u2="z" k="9" />
    <hkern u1="u" u2="y" k="19" />
    <hkern u1="u" u2="x" k="29" />
    <hkern u1="u" u2="w" k="-10" />
    <hkern u1="u" u2="v" k="19" />
    <hkern u1="u" u2="t" k="29" />
    <hkern u1="u" u2="s" k="9" />
    <hkern u1="u" u2="r" k="19" />
    <hkern u1="u" u2="o" k="19" />
    <hkern u1="u" u2="n" k="29" />
    <hkern u1="u" u2="j" k="39" />
    <hkern u1="u" u2="i" k="9" />
    <hkern u1="u" u2="f" k="19" />
    <hkern u1="u" u2="d" k="9" />
    <hkern u1="u" u2="c" k="19" />
    <hkern u1="u" u2="b" k="29" />
    <hkern u1="u" u2="a" k="49" />
    <hkern u1="v" u2="x" k="9" />
    <hkern u1="v" u2="s" k="9" />
    <hkern u1="v" u2="r" k="29" />
    <hkern u1="v" u2="q" k="29" />
    <hkern u1="v" u2="p" k="39" />
    <hkern u1="v" u2="o" k="19" />
    <hkern u1="v" u2="l" k="9" />
    <hkern u1="v" u2="j" k="49" />
    <hkern u1="v" u2="g" k="19" />
    <hkern u1="v" u2="c" k="19" />
    <hkern u1="v" u2="b" k="9" />
    <hkern u1="v" u2="a" k="49" />
    <hkern u1="w" u2="s" k="29" />
    <hkern u1="w" u2="r" k="19" />
    <hkern u1="w" u2="q" k="19" />
    <hkern u1="w" u2="p" k="29" />
    <hkern u1="w" u2="o" k="9" />
    <hkern u1="w" u2="l" k="9" />
    <hkern u1="w" u2="k" k="19" />
    <hkern u1="w" u2="j" k="44" />
    <hkern u1="w" u2="g" k="9" />
    <hkern u1="w" u2="c" k="29" />
    <hkern u1="w" u2="b" k="9" />
    <hkern u1="w" u2="a" k="39" />
    <hkern u1="x" u2="v" k="19" />
    <hkern u1="x" u2="u" k="29" />
    <hkern u1="x" u2="t" k="29" />
    <hkern u1="x" u2="s" k="19" />
    <hkern u1="x" u2="r" k="29" />
    <hkern u1="x" u2="q" k="39" />
    <hkern u1="x" u2="p" k="19" />
    <hkern u1="x" u2="o" k="29" />
    <hkern u1="x" u2="l" k="29" />
    <hkern u1="x" u2="k" k="19" />
    <hkern u1="x" u2="f" k="9" />
    <hkern u1="x" u2="e" k="19" />
    <hkern u1="x" u2="d" k="9" />
    <hkern u1="x" u2="c" k="19" />
    <hkern u1="y" u2="u" k="29" />
    <hkern u1="y" u2="s" k="24" />
    <hkern u1="y" u2="r" k="49" />
    <hkern u1="y" u2="q" k="49" />
    <hkern u1="y" u2="p" k="9" />
    <hkern u1="y" u2="o" k="19" />
    <hkern u1="y" u2="l" k="39" />
    <hkern u1="y" u2="k" k="9" />
    <hkern u1="y" u2="j" k="69" />
    <hkern u1="y" u2="g" k="49" />
    <hkern u1="y" u2="f" k="19" />
    <hkern u1="y" u2="e" k="19" />
    <hkern u1="y" u2="c" k="29" />
    <hkern u1="y" u2="a" k="69" />
    <hkern u1="z" u2="t" k="-10" />
    <hkern u1="z" u2="s" k="-5" />
    <hkern u1="z" u2="p" k="-10" />
    <hkern u1="z" u2="n" k="-10" />
    <hkern u1="z" u2="j" k="-10" />
    <hkern u1="z" u2="i" k="-10" />
    <hkern u1="z" u2="e" k="-10" />
    <hkern u1="z" u2="d" k="-10" />
    <hkern u1="z" u2="b" k="-10" />
    <hkern u1="z" u2="a" k="-10" />
    <hkern u1="&#x401;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x401;" u2="&#x444;" k="19" />
    <hkern u1="&#x401;" u2="&#x443;" k="-10" />
    <hkern u1="&#x401;" u2="&#x442;" k="19" />
    <hkern u1="&#x401;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x401;" u2="&#x42f;" k="-10" />
    <hkern u1="&#x401;" u2="&#x424;" k="49" />
    <hkern u1="&#x401;" u2="&#x421;" k="39" />
    <hkern u1="&#x404;" u2="&#x491;" k="-20" />
    <hkern u1="&#x404;" u2="&#x456;" k="-20" />
    <hkern u1="&#x404;" u2="&#x454;" k="-20" />
    <hkern u1="&#x404;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x404;" u2="&#x444;" k="49" />
    <hkern u1="&#x404;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x404;" u2="&#x41d;" k="19" />
    <hkern u1="&#x406;" u2="&#x491;" k="-20" />
    <hkern u1="&#x406;" u2="&#x457;" k="-20" />
    <hkern u1="&#x406;" u2="&#x456;" k="-30" />
    <hkern u1="&#x406;" u2="&#x454;" k="-30" />
    <hkern u1="&#x406;" u2="&#x44f;" k="-40" />
    <hkern u1="&#x406;" u2="&#x443;" k="-40" />
    <hkern u1="&#x406;" u2="&#x43c;" k="-20" />
    <hkern u1="&#x406;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x407;" u2="&#x457;" k="-20" />
    <hkern u1="&#x407;" u2="&#x454;" k="-20" />
    <hkern u1="&#x407;" u2="&#x451;" k="-10" />
    <hkern u1="&#x407;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x407;" u2="&#x443;" k="-20" />
    <hkern u1="&#x407;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x407;" u2="&#x434;" k="-20" />
    <hkern u1="&#x407;" u2="&#x407;" k="-40" />
    <hkern u1="&#x410;" u2="&#x444;" k="19" />
    <hkern u1="&#x410;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x410;" u2="&#x434;" k="-10" />
    <hkern u1="&#x410;" u2="&#x430;" k="-30" />
    <hkern u1="&#x410;" u2="&#x427;" k="29" />
    <hkern u1="&#x410;" u2="&#x424;" k="89" />
    <hkern u1="&#x410;" u2="&#x422;" k="59" />
    <hkern u1="&#x410;" u2="&#x41e;" k="19" />
    <hkern u1="&#x410;" u2="&#x410;" k="-20" />
    <hkern u1="&#x410;" u2="&#x404;" k="19" />
    <hkern u1="&#x411;" u2="&#x454;" k="-10" />
    <hkern u1="&#x411;" u2="&#x44f;" k="-10" />
    <hkern u1="&#x411;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x411;" u2="&#x443;" k="-10" />
    <hkern u1="&#x411;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x411;" u2="&#x424;" k="14" />
    <hkern u1="&#x411;" u2="&#x416;" k="29" />
    <hkern u1="&#x412;" u2="&#x447;" k="-10" />
    <hkern u1="&#x412;" u2="&#x443;" k="-10" />
    <hkern u1="&#x412;" u2="&#x427;" k="-10" />
    <hkern u1="&#x412;" u2="&#x425;" k="29" />
    <hkern u1="&#x412;" u2="&#x424;" k="19" />
    <hkern u1="&#x412;" u2="&#x416;" k="9" />
    <hkern u1="&#x413;" u2="&#x491;" k="39" />
    <hkern u1="&#x413;" u2="&#x456;" k="69" />
    <hkern u1="&#x413;" u2="&#x454;" k="59" />
    <hkern u1="&#x413;" u2="&#x451;" k="59" />
    <hkern u1="&#x413;" u2="&#x44f;" k="69" />
    <hkern u1="&#x413;" u2="&#x44e;" k="99" />
    <hkern u1="&#x413;" u2="&#x44d;" k="79" />
    <hkern u1="&#x413;" u2="&#x44c;" k="99" />
    <hkern u1="&#x413;" u2="&#x44b;" k="99" />
    <hkern u1="&#x413;" u2="&#x44a;" k="79" />
    <hkern u1="&#x413;" u2="&#x449;" k="99" />
    <hkern u1="&#x413;" u2="&#x448;" k="99" />
    <hkern u1="&#x413;" u2="&#x447;" k="69" />
    <hkern u1="&#x413;" u2="&#x446;" k="99" />
    <hkern u1="&#x413;" u2="&#x445;" k="99" />
    <hkern u1="&#x413;" u2="&#x444;" k="119" />
    <hkern u1="&#x413;" u2="&#x442;" k="59" />
    <hkern u1="&#x413;" u2="&#x441;" k="109" />
    <hkern u1="&#x413;" u2="&#x440;" k="79" />
    <hkern u1="&#x413;" u2="&#x43f;" k="79" />
    <hkern u1="&#x413;" u2="&#x43e;" k="99" />
    <hkern u1="&#x413;" u2="&#x43d;" k="79" />
    <hkern u1="&#x413;" u2="&#x43c;" k="69" />
    <hkern u1="&#x413;" u2="&#x43b;" k="69" />
    <hkern u1="&#x413;" u2="&#x43a;" k="99" />
    <hkern u1="&#x413;" u2="&#x439;" k="79" />
    <hkern u1="&#x413;" u2="&#x438;" k="99" />
    <hkern u1="&#x413;" u2="&#x437;" k="99" />
    <hkern u1="&#x413;" u2="&#x436;" k="119" />
    <hkern u1="&#x413;" u2="&#x435;" k="79" />
    <hkern u1="&#x413;" u2="&#x434;" k="119" />
    <hkern u1="&#x413;" u2="&#x433;" k="79" />
    <hkern u1="&#x413;" u2="&#x432;" k="79" />
    <hkern u1="&#x413;" u2="&#x431;" k="79" />
    <hkern u1="&#x413;" u2="&#x430;" k="119" />
    <hkern u1="&#x413;" u2="&#x427;" k="-20" />
    <hkern u1="&#x413;" u2="&#x426;" k="9" />
    <hkern u1="&#x413;" u2="&#x425;" k="19" />
    <hkern u1="&#x413;" u2="&#x424;" k="29" />
    <hkern u1="&#x413;" u2="&#x423;" k="-10" />
    <hkern u1="&#x413;" u2="&#x416;" k="29" />
    <hkern u1="&#x413;" u2="&#x414;" k="129" />
    <hkern u1="&#x413;" u2="&#x410;" k="109" />
    <hkern u1="&#x414;" u2="&#x491;" k="-10" />
    <hkern u1="&#x414;" u2="&#x457;" k="-10" />
    <hkern u1="&#x414;" u2="&#x456;" k="-10" />
    <hkern u1="&#x414;" u2="&#x454;" k="-50" />
    <hkern u1="&#x414;" u2="&#x451;" k="-10" />
    <hkern u1="&#x414;" u2="&#x44f;" k="-50" />
    <hkern u1="&#x414;" u2="&#x44e;" k="-10" />
    <hkern u1="&#x414;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x414;" u2="&#x44c;" k="-10" />
    <hkern u1="&#x414;" u2="&#x44b;" k="-10" />
    <hkern u1="&#x414;" u2="&#x445;" k="-20" />
    <hkern u1="&#x414;" u2="&#x443;" k="-20" />
    <hkern u1="&#x414;" u2="&#x441;" k="-10" />
    <hkern u1="&#x414;" u2="&#x440;" k="-10" />
    <hkern u1="&#x414;" u2="&#x43f;" k="-10" />
    <hkern u1="&#x414;" u2="&#x43c;" k="-20" />
    <hkern u1="&#x414;" u2="&#x43b;" k="-60" />
    <hkern u1="&#x414;" u2="&#x43a;" k="-10" />
    <hkern u1="&#x414;" u2="&#x439;" k="-10" />
    <hkern u1="&#x414;" u2="&#x438;" k="-10" />
    <hkern u1="&#x414;" u2="&#x437;" k="-10" />
    <hkern u1="&#x414;" u2="&#x436;" k="-10" />
    <hkern u1="&#x414;" u2="&#x435;" k="-10" />
    <hkern u1="&#x414;" u2="&#x434;" k="-40" />
    <hkern u1="&#x414;" u2="&#x433;" k="-10" />
    <hkern u1="&#x414;" u2="&#x432;" k="-10" />
    <hkern u1="&#x414;" u2="&#x431;" k="-10" />
    <hkern u1="&#x414;" u2="&#x430;" k="-40" />
    <hkern u1="&#x414;" u2="&#x424;" k="19" />
    <hkern u1="&#x414;" u2="&#x420;" k="-20" />
    <hkern u1="&#x414;" u2="&#x41f;" k="-20" />
    <hkern u1="&#x414;" u2="&#x41b;" k="-20" />
    <hkern u1="&#x414;" u2="&#x41a;" k="9" />
    <hkern u1="&#x414;" u2="&#x415;" k="19" />
    <hkern u1="&#x414;" u2="&#x414;" k="-30" />
    <hkern u1="&#x414;" u2="&#x401;" k="19" />
    <hkern u1="&#x415;" u2="&#x44f;" k="-15" />
    <hkern u1="&#x415;" u2="&#x447;" k="19" />
    <hkern u1="&#x415;" u2="&#x444;" k="29" />
    <hkern u1="&#x415;" u2="&#x443;" k="-10" />
    <hkern u1="&#x415;" u2="&#x43e;" k="19" />
    <hkern u1="&#x415;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x415;" u2="&#x43b;" k="-30" />
    <hkern u1="&#x415;" u2="&#x424;" k="39" />
    <hkern u1="&#x415;" u2="&#x41e;" k="69" />
    <hkern u1="&#x415;" u2="&#x41b;" k="-10" />
    <hkern u1="&#x415;" u2="&#x414;" k="-10" />
    <hkern u1="&#x416;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x416;" u2="&#x44a;" k="49" />
    <hkern u1="&#x416;" u2="&#x447;" k="19" />
    <hkern u1="&#x416;" u2="&#x444;" k="29" />
    <hkern u1="&#x416;" u2="&#x442;" k="9" />
    <hkern u1="&#x416;" u2="&#x434;" k="-10" />
    <hkern u1="&#x416;" u2="&#x42a;" k="49" />
    <hkern u1="&#x416;" u2="&#x427;" k="19" />
    <hkern u1="&#x416;" u2="&#x424;" k="69" />
    <hkern u1="&#x416;" u2="&#x422;" k="29" />
    <hkern u1="&#x416;" u2="&#x421;" k="49" />
    <hkern u1="&#x416;" u2="&#x420;" k="19" />
    <hkern u1="&#x416;" u2="&#x41e;" k="49" />
    <hkern u1="&#x416;" u2="&#x414;" k="-10" />
    <hkern u1="&#x416;" u2="&#x401;" k="19" />
    <hkern u1="&#x417;" u2="&#x454;" k="-10" />
    <hkern u1="&#x417;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x417;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x417;" u2="&#x447;" k="-10" />
    <hkern u1="&#x417;" u2="&#x445;" k="9" />
    <hkern u1="&#x417;" u2="&#x443;" k="-10" />
    <hkern u1="&#x417;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x417;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x417;" u2="&#x431;" k="-10" />
    <hkern u1="&#x418;" u2="&#x443;" k="-20" />
    <hkern u1="&#x418;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x418;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x419;" u2="&#x414;" k="9" />
    <hkern u1="&#x41a;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x41a;" u2="&#x444;" k="29" />
    <hkern u1="&#x41a;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x41a;" u2="&#x42d;" k="19" />
    <hkern u1="&#x41a;" u2="&#x42a;" k="49" />
    <hkern u1="&#x41a;" u2="&#x424;" k="94" />
    <hkern u1="&#x41a;" u2="&#x415;" k="19" />
    <hkern u1="&#x41b;" u2="&#x44f;" k="-10" />
    <hkern u1="&#x41b;" u2="&#x423;" k="-20" />
    <hkern u1="&#x41b;" u2="&#x421;" k="9" />
    <hkern u1="&#x41c;" u2="&#x457;" k="-10" />
    <hkern u1="&#x41c;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x41c;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x41c;" u2="&#x424;" k="19" />
    <hkern u1="&#x41c;" u2="&#x414;" k="29" />
    <hkern u1="&#x41d;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x41d;" u2="&#x443;" k="-20" />
    <hkern u1="&#x41d;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x41d;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x41d;" u2="&#x42a;" k="39" />
    <hkern u1="&#x41e;" u2="&#x454;" k="-30" />
    <hkern u1="&#x41e;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x41e;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x41e;" u2="&#x447;" k="-20" />
    <hkern u1="&#x41e;" u2="&#x443;" k="-30" />
    <hkern u1="&#x41e;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x41e;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x41e;" u2="&#x425;" k="69" />
    <hkern u1="&#x41e;" u2="&#x422;" k="19" />
    <hkern u1="&#x41e;" u2="&#x416;" k="59" />
    <hkern u1="&#x41e;" u2="&#x414;" k="59" />
    <hkern u1="&#x41e;" u2="&#x410;" k="19" />
    <hkern u1="&#x41f;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x443;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x41f;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x41f;" u2="&#x42a;" k="19" />
    <hkern u1="&#x41f;" u2="&#x425;" k="19" />
    <hkern u1="&#x41f;" u2="&#x424;" k="9" />
    <hkern u1="&#x41f;" u2="&#x416;" k="19" />
    <hkern u1="&#x420;" u2="&#x491;" k="9" />
    <hkern u1="&#x420;" u2="&#x457;" k="-10" />
    <hkern u1="&#x420;" u2="&#x456;" k="19" />
    <hkern u1="&#x420;" u2="&#x451;" k="9" />
    <hkern u1="&#x420;" u2="&#x44f;" k="9" />
    <hkern u1="&#x420;" u2="&#x44e;" k="19" />
    <hkern u1="&#x420;" u2="&#x44c;" k="19" />
    <hkern u1="&#x420;" u2="&#x44b;" k="19" />
    <hkern u1="&#x420;" u2="&#x449;" k="9" />
    <hkern u1="&#x420;" u2="&#x448;" k="9" />
    <hkern u1="&#x420;" u2="&#x446;" k="9" />
    <hkern u1="&#x420;" u2="&#x445;" k="19" />
    <hkern u1="&#x420;" u2="&#x444;" k="29" />
    <hkern u1="&#x420;" u2="&#x442;" k="-10" />
    <hkern u1="&#x420;" u2="&#x441;" k="19" />
    <hkern u1="&#x420;" u2="&#x440;" k="9" />
    <hkern u1="&#x420;" u2="&#x43f;" k="9" />
    <hkern u1="&#x420;" u2="&#x43e;" k="19" />
    <hkern u1="&#x420;" u2="&#x43d;" k="19" />
    <hkern u1="&#x420;" u2="&#x43a;" k="19" />
    <hkern u1="&#x420;" u2="&#x439;" k="19" />
    <hkern u1="&#x420;" u2="&#x438;" k="29" />
    <hkern u1="&#x420;" u2="&#x437;" k="9" />
    <hkern u1="&#x420;" u2="&#x436;" k="39" />
    <hkern u1="&#x420;" u2="&#x435;" k="9" />
    <hkern u1="&#x420;" u2="&#x434;" k="29" />
    <hkern u1="&#x420;" u2="&#x432;" k="19" />
    <hkern u1="&#x420;" u2="&#x431;" k="19" />
    <hkern u1="&#x420;" u2="&#x430;" k="89" />
    <hkern u1="&#x420;" u2="&#x42f;" k="9" />
    <hkern u1="&#x420;" u2="&#x425;" k="69" />
    <hkern u1="&#x420;" u2="&#x424;" k="-10" />
    <hkern u1="&#x420;" u2="&#x423;" k="-10" />
    <hkern u1="&#x420;" u2="&#x421;" k="19" />
    <hkern u1="&#x420;" u2="&#x41b;" k="59" />
    <hkern u1="&#x420;" u2="&#x417;" k="19" />
    <hkern u1="&#x420;" u2="&#x416;" k="49" />
    <hkern u1="&#x420;" u2="&#x414;" k="109" />
    <hkern u1="&#x420;" u2="&#x410;" k="29" />
    <hkern u1="&#x421;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x421;" u2="&#x443;" k="-20" />
    <hkern u1="&#x421;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x421;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x421;" u2="&#x434;" k="-10" />
    <hkern u1="&#x421;" u2="&#x427;" k="-20" />
    <hkern u1="&#x421;" u2="&#x424;" k="9" />
    <hkern u1="&#x421;" u2="&#x423;" k="-10" />
    <hkern u1="&#x421;" u2="&#x414;" k="-10" />
    <hkern u1="&#x422;" u2="&#x457;" k="-10" />
    <hkern u1="&#x422;" u2="&#x44e;" k="9" />
    <hkern u1="&#x422;" u2="&#x44c;" k="9" />
    <hkern u1="&#x422;" u2="&#x44b;" k="19" />
    <hkern u1="&#x422;" u2="&#x447;" k="9" />
    <hkern u1="&#x422;" u2="&#x446;" k="19" />
    <hkern u1="&#x422;" u2="&#x444;" k="89" />
    <hkern u1="&#x422;" u2="&#x441;" k="19" />
    <hkern u1="&#x422;" u2="&#x43e;" k="59" />
    <hkern u1="&#x422;" u2="&#x43d;" k="29" />
    <hkern u1="&#x422;" u2="&#x43c;" k="39" />
    <hkern u1="&#x422;" u2="&#x43b;" k="9" />
    <hkern u1="&#x422;" u2="&#x43a;" k="29" />
    <hkern u1="&#x422;" u2="&#x436;" k="59" />
    <hkern u1="&#x422;" u2="&#x434;" k="19" />
    <hkern u1="&#x422;" u2="&#x433;" k="-10" />
    <hkern u1="&#x422;" u2="&#x432;" k="29" />
    <hkern u1="&#x422;" u2="&#x431;" k="29" />
    <hkern u1="&#x422;" u2="&#x430;" k="49" />
    <hkern u1="&#x422;" u2="&#x42f;" k="19" />
    <hkern u1="&#x422;" u2="&#x42d;" k="-10" />
    <hkern u1="&#x422;" u2="&#x42a;" k="-20" />
    <hkern u1="&#x422;" u2="&#x427;" k="-20" />
    <hkern u1="&#x422;" u2="&#x425;" k="19" />
    <hkern u1="&#x422;" u2="&#x424;" k="19" />
    <hkern u1="&#x422;" u2="&#x423;" k="-20" />
    <hkern u1="&#x422;" u2="&#x422;" k="-20" />
    <hkern u1="&#x422;" u2="&#x41b;" k="39" />
    <hkern u1="&#x422;" u2="&#x416;" k="9" />
    <hkern u1="&#x422;" u2="&#x414;" k="69" />
    <hkern u1="&#x423;" u2="&#x491;" k="39" />
    <hkern u1="&#x423;" u2="&#x457;" k="-10" />
    <hkern u1="&#x423;" u2="&#x456;" k="29" />
    <hkern u1="&#x423;" u2="&#x454;" k="29" />
    <hkern u1="&#x423;" u2="&#x44e;" k="29" />
    <hkern u1="&#x423;" u2="&#x44d;" k="29" />
    <hkern u1="&#x423;" u2="&#x44b;" k="39" />
    <hkern u1="&#x423;" u2="&#x449;" k="39" />
    <hkern u1="&#x423;" u2="&#x448;" k="39" />
    <hkern u1="&#x423;" u2="&#x446;" k="39" />
    <hkern u1="&#x423;" u2="&#x445;" k="49" />
    <hkern u1="&#x423;" u2="&#x444;" k="59" />
    <hkern u1="&#x423;" u2="&#x441;" k="49" />
    <hkern u1="&#x423;" u2="&#x440;" k="19" />
    <hkern u1="&#x423;" u2="&#x43f;" k="19" />
    <hkern u1="&#x423;" u2="&#x43e;" k="49" />
    <hkern u1="&#x423;" u2="&#x43d;" k="9" />
    <hkern u1="&#x423;" u2="&#x43a;" k="19" />
    <hkern u1="&#x423;" u2="&#x439;" k="9" />
    <hkern u1="&#x423;" u2="&#x438;" k="9" />
    <hkern u1="&#x423;" u2="&#x437;" k="29" />
    <hkern u1="&#x423;" u2="&#x436;" k="29" />
    <hkern u1="&#x423;" u2="&#x434;" k="59" />
    <hkern u1="&#x423;" u2="&#x432;" k="9" />
    <hkern u1="&#x423;" u2="&#x431;" k="19" />
    <hkern u1="&#x423;" u2="&#x430;" k="99" />
    <hkern u1="&#x423;" u2="&#x42f;" k="29" />
    <hkern u1="&#x423;" u2="&#x427;" k="-10" />
    <hkern u1="&#x423;" u2="&#x425;" k="19" />
    <hkern u1="&#x423;" u2="&#x424;" k="29" />
    <hkern u1="&#x423;" u2="&#x422;" k="-10" />
    <hkern u1="&#x423;" u2="&#x421;" k="19" />
    <hkern u1="&#x423;" u2="&#x41b;" k="49" />
    <hkern u1="&#x423;" u2="&#x416;" k="29" />
    <hkern u1="&#x423;" u2="&#x414;" k="109" />
    <hkern u1="&#x423;" u2="&#x413;" k="29" />
    <hkern u1="&#x423;" u2="&#x412;" k="9" />
    <hkern u1="&#x423;" u2="&#x410;" k="49" />
    <hkern u1="&#x424;" u2="&#x447;" k="-20" />
    <hkern u1="&#x424;" u2="&#x446;" k="19" />
    <hkern u1="&#x424;" u2="&#x445;" k="24" />
    <hkern u1="&#x424;" u2="&#x444;" k="24" />
    <hkern u1="&#x424;" u2="&#x441;" k="19" />
    <hkern u1="&#x424;" u2="&#x43b;" k="9" />
    <hkern u1="&#x424;" u2="&#x43a;" k="19" />
    <hkern u1="&#x424;" u2="&#x437;" k="29" />
    <hkern u1="&#x424;" u2="&#x436;" k="19" />
    <hkern u1="&#x424;" u2="&#x434;" k="49" />
    <hkern u1="&#x424;" u2="&#x430;" k="49" />
    <hkern u1="&#x424;" u2="&#x42f;" k="19" />
    <hkern u1="&#x424;" u2="&#x42c;" k="19" />
    <hkern u1="&#x424;" u2="&#x42a;" k="39" />
    <hkern u1="&#x424;" u2="&#x427;" k="-20" />
    <hkern u1="&#x424;" u2="&#x426;" k="19" />
    <hkern u1="&#x424;" u2="&#x425;" k="69" />
    <hkern u1="&#x424;" u2="&#x423;" k="19" />
    <hkern u1="&#x424;" u2="&#x422;" k="19" />
    <hkern u1="&#x424;" u2="&#x420;" k="19" />
    <hkern u1="&#x424;" u2="&#x41f;" k="19" />
    <hkern u1="&#x424;" u2="&#x41c;" k="19" />
    <hkern u1="&#x424;" u2="&#x41b;" k="49" />
    <hkern u1="&#x424;" u2="&#x41a;" k="9" />
    <hkern u1="&#x424;" u2="&#x416;" k="59" />
    <hkern u1="&#x424;" u2="&#x415;" k="19" />
    <hkern u1="&#x424;" u2="&#x414;" k="139" />
    <hkern u1="&#x424;" u2="&#x413;" k="9" />
    <hkern u1="&#x424;" u2="&#x412;" k="19" />
    <hkern u1="&#x424;" u2="&#x411;" k="9" />
    <hkern u1="&#x424;" u2="&#x410;" k="59" />
    <hkern u1="&#x424;" u2="&#x401;" k="19" />
    <hkern u1="&#x425;" u2="&#x490;" k="9" />
    <hkern u1="&#x425;" u2="&#x44f;" k="-10" />
    <hkern u1="&#x425;" u2="&#x44a;" k="29" />
    <hkern u1="&#x425;" u2="&#x449;" k="19" />
    <hkern u1="&#x425;" u2="&#x447;" k="29" />
    <hkern u1="&#x425;" u2="&#x445;" k="-10" />
    <hkern u1="&#x425;" u2="&#x444;" k="19" />
    <hkern u1="&#x425;" u2="&#x443;" k="-10" />
    <hkern u1="&#x425;" u2="&#x442;" k="39" />
    <hkern u1="&#x425;" u2="&#x441;" k="29" />
    <hkern u1="&#x425;" u2="&#x440;" k="19" />
    <hkern u1="&#x425;" u2="&#x43f;" k="19" />
    <hkern u1="&#x425;" u2="&#x43e;" k="29" />
    <hkern u1="&#x425;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x425;" u2="&#x43a;" k="19" />
    <hkern u1="&#x425;" u2="&#x42e;" k="-10" />
    <hkern u1="&#x425;" u2="&#x42d;" k="9" />
    <hkern u1="&#x425;" u2="&#x42a;" k="29" />
    <hkern u1="&#x425;" u2="&#x424;" k="69" />
    <hkern u1="&#x425;" u2="&#x422;" k="19" />
    <hkern u1="&#x425;" u2="&#x421;" k="39" />
    <hkern u1="&#x425;" u2="&#x41f;" k="19" />
    <hkern u1="&#x425;" u2="&#x41e;" k="69" />
    <hkern u1="&#x425;" u2="&#x413;" k="19" />
    <hkern u1="&#x426;" u2="&#x44f;" k="-25" />
    <hkern u1="&#x426;" u2="&#x447;" k="19" />
    <hkern u1="&#x426;" u2="&#x445;" k="-10" />
    <hkern u1="&#x426;" u2="&#x443;" k="-10" />
    <hkern u1="&#x426;" u2="&#x43b;" k="-30" />
    <hkern u1="&#x426;" u2="&#x434;" k="-20" />
    <hkern u1="&#x426;" u2="&#x42a;" k="39" />
    <hkern u1="&#x426;" u2="&#x427;" k="29" />
    <hkern u1="&#x426;" u2="&#x424;" k="49" />
    <hkern u1="&#x426;" u2="&#x423;" k="29" />
    <hkern u1="&#x426;" u2="&#x422;" k="59" />
    <hkern u1="&#x426;" u2="&#x421;" k="19" />
    <hkern u1="&#x426;" u2="&#x41e;" k="49" />
    <hkern u1="&#x426;" u2="&#x412;" k="-10" />
    <hkern u1="&#x426;" u2="&#x410;" k="-10" />
    <hkern u1="&#x427;" u2="&#x454;" k="-20" />
    <hkern u1="&#x427;" u2="&#x44f;" k="-40" />
    <hkern u1="&#x427;" u2="&#x44e;" k="-10" />
    <hkern u1="&#x427;" u2="&#x44d;" k="-40" />
    <hkern u1="&#x427;" u2="&#x44c;" k="-10" />
    <hkern u1="&#x427;" u2="&#x445;" k="-10" />
    <hkern u1="&#x427;" u2="&#x443;" k="-20" />
    <hkern u1="&#x427;" u2="&#x43e;" k="-10" />
    <hkern u1="&#x427;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x427;" u2="&#x43b;" k="-40" />
    <hkern u1="&#x427;" u2="&#x43a;" k="-20" />
    <hkern u1="&#x427;" u2="&#x439;" k="-10" />
    <hkern u1="&#x427;" u2="&#x438;" k="-20" />
    <hkern u1="&#x427;" u2="&#x433;" k="-20" />
    <hkern u1="&#x427;" u2="&#x432;" k="-10" />
    <hkern u1="&#x427;" u2="&#x431;" k="-10" />
    <hkern u1="&#x427;" u2="&#x430;" k="-20" />
    <hkern u1="&#x428;" u2="&#x44f;" k="-50" />
    <hkern u1="&#x428;" u2="&#x44d;" k="-50" />
    <hkern u1="&#x428;" u2="&#x443;" k="-40" />
    <hkern u1="&#x428;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x428;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x429;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x429;" u2="&#x44d;" k="-30" />
    <hkern u1="&#x429;" u2="&#x447;" k="9" />
    <hkern u1="&#x429;" u2="&#x444;" k="19" />
    <hkern u1="&#x429;" u2="&#x443;" k="-10" />
    <hkern u1="&#x429;" u2="&#x442;" k="39" />
    <hkern u1="&#x429;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x429;" u2="&#x434;" k="-30" />
    <hkern u1="&#x429;" u2="&#x42a;" k="29" />
    <hkern u1="&#x429;" u2="&#x427;" k="59" />
    <hkern u1="&#x429;" u2="&#x424;" k="59" />
    <hkern u1="&#x429;" u2="&#x422;" k="59" />
    <hkern u1="&#x429;" u2="&#x41e;" k="39" />
    <hkern u1="&#x429;" u2="&#x404;" k="39" />
    <hkern u1="&#x42a;" u2="&#x425;" k="19" />
    <hkern u1="&#x42a;" u2="&#x422;" k="99" />
    <hkern u1="&#x42a;" u2="&#x416;" k="9" />
    <hkern u1="&#x42b;" u2="&#x424;" k="19" />
    <hkern u1="&#x42c;" u2="&#x427;" k="19" />
    <hkern u1="&#x42c;" u2="&#x425;" k="29" />
    <hkern u1="&#x42c;" u2="&#x422;" k="99" />
    <hkern u1="&#x42d;" u2="&#x456;" k="-10" />
    <hkern u1="&#x42d;" u2="&#x454;" k="-10" />
    <hkern u1="&#x42d;" u2="&#x44f;" k="-40" />
    <hkern u1="&#x42d;" u2="&#x44d;" k="-20" />
    <hkern u1="&#x42d;" u2="&#x447;" k="-10" />
    <hkern u1="&#x42d;" u2="&#x444;" k="-20" />
    <hkern u1="&#x42d;" u2="&#x443;" k="-30" />
    <hkern u1="&#x42d;" u2="&#x442;" k="-10" />
    <hkern u1="&#x42d;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x42d;" u2="&#x425;" k="29" />
    <hkern u1="&#x42d;" u2="&#x416;" k="29" />
    <hkern u1="&#x42d;" u2="&#x414;" k="29" />
    <hkern u1="&#x42e;" u2="&#x44d;" k="-20" />
    <hkern u1="&#x42e;" u2="&#x447;" k="-10" />
    <hkern u1="&#x42e;" u2="&#x446;" k="9" />
    <hkern u1="&#x42e;" u2="&#x445;" k="39" />
    <hkern u1="&#x42e;" u2="&#x427;" k="19" />
    <hkern u1="&#x42e;" u2="&#x425;" k="49" />
    <hkern u1="&#x42e;" u2="&#x422;" k="19" />
    <hkern u1="&#x42e;" u2="&#x416;" k="29" />
    <hkern u1="&#x42e;" u2="&#x414;" k="39" />
    <hkern u1="&#x42e;" u2="&#x410;" k="49" />
    <hkern u1="&#x42f;" u2="&#x454;" k="-10" />
    <hkern u1="&#x42f;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x42f;" u2="&#x44d;" k="-20" />
    <hkern u1="&#x42f;" u2="&#x447;" k="-10" />
    <hkern u1="&#x42f;" u2="&#x445;" k="-10" />
    <hkern u1="&#x42f;" u2="&#x443;" k="-20" />
    <hkern u1="&#x42f;" u2="&#x425;" k="9" />
    <hkern u1="&#x42f;" u2="&#x422;" k="19" />
    <hkern u1="&#x430;" u2="&#x457;" k="29" />
    <hkern u1="&#x430;" u2="&#x454;" k="-10" />
    <hkern u1="&#x430;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x430;" u2="&#x446;" k="29" />
    <hkern u1="&#x430;" u2="&#x444;" k="29" />
    <hkern u1="&#x430;" u2="&#x443;" k="-20" />
    <hkern u1="&#x430;" u2="&#x43d;" k="9" />
    <hkern u1="&#x430;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x430;" u2="&#x43b;" k="-30" />
    <hkern u1="&#x430;" u2="&#x434;" k="9" />
    <hkern u1="&#x430;" u2="&#x433;" k="29" />
    <hkern u1="&#x430;" u2="&#x432;" k="19" />
    <hkern u1="&#x430;" u2="&#x431;" k="29" />
    <hkern u1="&#x430;" u2="&#x430;" k="-20" />
    <hkern u1="&#x431;" u2="&#x44a;" k="29" />
    <hkern u1="&#x431;" u2="&#x444;" k="9" />
    <hkern u1="&#x431;" u2="&#x443;" k="-10" />
    <hkern u1="&#x431;" u2="&#x434;" k="-10" />
    <hkern u1="&#x432;" u2="&#x430;" k="9" />
    <hkern u1="&#x433;" u2="&#x44d;" k="-20" />
    <hkern u1="&#x433;" u2="&#x447;" k="-10" />
    <hkern u1="&#x433;" u2="&#x445;" k="9" />
    <hkern u1="&#x433;" u2="&#x444;" k="39" />
    <hkern u1="&#x433;" u2="&#x441;" k="19" />
    <hkern u1="&#x433;" u2="&#x43e;" k="19" />
    <hkern u1="&#x433;" u2="&#x434;" k="49" />
    <hkern u1="&#x433;" u2="&#x430;" k="79" />
    <hkern u1="&#x434;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x434;" u2="&#x444;" k="19" />
    <hkern u1="&#x434;" u2="&#x443;" k="-20" />
    <hkern u1="&#x434;" u2="&#x441;" k="-10" />
    <hkern u1="&#x434;" u2="&#x43d;" k="-20" />
    <hkern u1="&#x434;" u2="&#x43c;" k="-30" />
    <hkern u1="&#x434;" u2="&#x43b;" k="-40" />
    <hkern u1="&#x434;" u2="&#x437;" k="-20" />
    <hkern u1="&#x434;" u2="&#x434;" k="-40" />
    <hkern u1="&#x434;" u2="&#x430;" k="-20" />
    <hkern u1="&#x435;" u2="&#x454;" k="-10" />
    <hkern u1="&#x435;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x435;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x435;" u2="&#x443;" k="-30" />
    <hkern u1="&#x435;" u2="&#x434;" k="-10" />
    <hkern u1="&#x436;" u2="&#x454;" k="9" />
    <hkern u1="&#x436;" u2="&#x451;" k="9" />
    <hkern u1="&#x436;" u2="&#x44a;" k="49" />
    <hkern u1="&#x436;" u2="&#x444;" k="39" />
    <hkern u1="&#x436;" u2="&#x443;" k="-40" />
    <hkern u1="&#x436;" u2="&#x441;" k="19" />
    <hkern u1="&#x436;" u2="&#x43e;" k="29" />
    <hkern u1="&#x436;" u2="&#x439;" k="9" />
    <hkern u1="&#x437;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x437;" u2="&#x443;" k="-30" />
    <hkern u1="&#x437;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x437;" u2="&#x434;" k="-10" />
    <hkern u1="&#x438;" u2="&#x444;" k="9" />
    <hkern u1="&#x438;" u2="&#x443;" k="-20" />
    <hkern u1="&#x439;" u2="&#x444;" k="19" />
    <hkern u1="&#x439;" u2="&#x443;" k="-20" />
    <hkern u1="&#x439;" u2="&#x441;" k="9" />
    <hkern u1="&#x439;" u2="&#x436;" k="9" />
    <hkern u1="&#x439;" u2="&#x434;" k="9" />
    <hkern u1="&#x43a;" u2="&#x44a;" k="39" />
    <hkern u1="&#x43a;" u2="&#x444;" k="39" />
    <hkern u1="&#x43a;" u2="&#x443;" k="-10" />
    <hkern u1="&#x43b;" u2="&#x454;" k="9" />
    <hkern u1="&#x43b;" u2="&#x44a;" k="29" />
    <hkern u1="&#x43b;" u2="&#x444;" k="19" />
    <hkern u1="&#x43b;" u2="&#x443;" k="-10" />
    <hkern u1="&#x43b;" u2="&#x441;" k="9" />
    <hkern u1="&#x43b;" u2="&#x43d;" k="9" />
    <hkern u1="&#x43b;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x43b;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x43c;" u2="&#x444;" k="4" />
    <hkern u1="&#x43c;" u2="&#x443;" k="-30" />
    <hkern u1="&#x43c;" u2="&#x442;" k="-10" />
    <hkern u1="&#x43c;" u2="&#x43e;" k="9" />
    <hkern u1="&#x43c;" u2="&#x43d;" k="-10" />
    <hkern u1="&#x43c;" u2="&#x43c;" k="-20" />
    <hkern u1="&#x43c;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x43c;" u2="&#x437;" k="-10" />
    <hkern u1="&#x43c;" u2="&#x430;" k="-20" />
    <hkern u1="&#x43d;" u2="&#x44a;" k="19" />
    <hkern u1="&#x43d;" u2="&#x447;" k="19" />
    <hkern u1="&#x43d;" u2="&#x444;" k="19" />
    <hkern u1="&#x43d;" u2="&#x442;" k="19" />
    <hkern u1="&#x43d;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x43e;" u2="&#x44e;" k="19" />
    <hkern u1="&#x43e;" u2="&#x443;" k="-10" />
    <hkern u1="&#x43e;" u2="&#x43b;" k="9" />
    <hkern u1="&#x43e;" u2="&#x436;" k="29" />
    <hkern u1="&#x43f;" u2="&#x444;" k="19" />
    <hkern u1="&#x43f;" u2="&#x443;" k="-20" />
    <hkern u1="&#x440;" u2="&#x456;" k="9" />
    <hkern u1="&#x440;" u2="&#x454;" k="-20" />
    <hkern u1="&#x440;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x440;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x440;" u2="&#x44a;" k="19" />
    <hkern u1="&#x440;" u2="&#x443;" k="-20" />
    <hkern u1="&#x440;" u2="&#x43b;" k="39" />
    <hkern u1="&#x440;" u2="&#x436;" k="19" />
    <hkern u1="&#x440;" u2="&#x434;" k="19" />
    <hkern u1="&#x441;" u2="&#x444;" k="9" />
    <hkern u1="&#x441;" u2="&#x443;" k="-30" />
    <hkern u1="&#x442;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x442;" u2="&#x44d;" k="-30" />
    <hkern u1="&#x442;" u2="&#x447;" k="-20" />
    <hkern u1="&#x442;" u2="&#x445;" k="-10" />
    <hkern u1="&#x442;" u2="&#x444;" k="9" />
    <hkern u1="&#x442;" u2="&#x443;" k="-30" />
    <hkern u1="&#x442;" u2="&#x442;" k="-30" />
    <hkern u1="&#x442;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x442;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x443;" u2="&#x491;" k="9" />
    <hkern u1="&#x443;" u2="&#x454;" k="19" />
    <hkern u1="&#x443;" u2="&#x44d;" k="9" />
    <hkern u1="&#x443;" u2="&#x449;" k="19" />
    <hkern u1="&#x443;" u2="&#x448;" k="39" />
    <hkern u1="&#x443;" u2="&#x446;" k="19" />
    <hkern u1="&#x443;" u2="&#x444;" k="19" />
    <hkern u1="&#x443;" u2="&#x443;" k="-20" />
    <hkern u1="&#x443;" u2="&#x442;" k="-10" />
    <hkern u1="&#x443;" u2="&#x440;" k="9" />
    <hkern u1="&#x443;" u2="&#x43e;" k="9" />
    <hkern u1="&#x443;" u2="&#x43d;" k="9" />
    <hkern u1="&#x443;" u2="&#x43c;" k="-10" />
    <hkern u1="&#x443;" u2="&#x437;" k="9" />
    <hkern u1="&#x443;" u2="&#x436;" k="29" />
    <hkern u1="&#x443;" u2="&#x434;" k="39" />
    <hkern u1="&#x443;" u2="&#x430;" k="19" />
    <hkern u1="&#x444;" u2="&#x456;" k="19" />
    <hkern u1="&#x444;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x444;" u2="&#x446;" k="9" />
    <hkern u1="&#x444;" u2="&#x445;" k="19" />
    <hkern u1="&#x444;" u2="&#x443;" k="19" />
    <hkern u1="&#x444;" u2="&#x442;" k="9" />
    <hkern u1="&#x444;" u2="&#x43f;" k="19" />
    <hkern u1="&#x444;" u2="&#x43d;" k="19" />
    <hkern u1="&#x444;" u2="&#x43c;" k="9" />
    <hkern u1="&#x444;" u2="&#x43b;" k="39" />
    <hkern u1="&#x444;" u2="&#x43a;" k="19" />
    <hkern u1="&#x444;" u2="&#x439;" k="19" />
    <hkern u1="&#x444;" u2="&#x438;" k="29" />
    <hkern u1="&#x444;" u2="&#x436;" k="29" />
    <hkern u1="&#x444;" u2="&#x434;" k="39" />
    <hkern u1="&#x444;" u2="&#x430;" k="29" />
    <hkern u1="&#x445;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x445;" u2="&#x444;" k="29" />
    <hkern u1="&#x445;" u2="&#x43d;" k="-10" />
    <hkern u1="&#x446;" u2="&#x442;" k="49" />
    <hkern u1="&#x447;" u2="&#x442;" k="9" />
    <hkern u1="&#x448;" u2="&#x444;" k="19" />
    <hkern u1="&#x449;" u2="&#x491;" k="9" />
    <hkern u1="&#x449;" u2="&#x454;" k="-10" />
    <hkern u1="&#x449;" u2="&#x44f;" k="-20" />
    <hkern u1="&#x449;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x449;" u2="&#x443;" k="-10" />
    <hkern u1="&#x449;" u2="&#x433;" k="9" />
    <hkern u1="&#x44a;" u2="&#x43b;" k="-10" />
    <hkern u1="&#x44a;" u2="&#x43a;" k="9" />
    <hkern u1="&#x44c;" u2="&#x454;" k="-30" />
    <hkern u1="&#x44c;" u2="&#x44f;" k="-30" />
    <hkern u1="&#x44c;" u2="&#x442;" k="29" />
    <hkern u1="&#x44c;" u2="&#x43d;" k="9" />
    <hkern u1="&#x44d;" u2="&#x44f;" k="-10" />
    <hkern u1="&#x44d;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x44d;" u2="&#x448;" k="19" />
    <hkern u1="&#x44d;" u2="&#x444;" k="-10" />
    <hkern u1="&#x44d;" u2="&#x436;" k="19" />
    <hkern u1="&#x44e;" u2="&#x457;" k="19" />
    <hkern u1="&#x44e;" u2="&#x449;" k="29" />
    <hkern u1="&#x44e;" u2="&#x442;" k="9" />
    <hkern u1="&#x44e;" u2="&#x43d;" k="19" />
    <hkern u1="&#x44e;" u2="&#x437;" k="19" />
    <hkern u1="&#x44e;" u2="&#x436;" k="29" />
    <hkern u1="&#x44e;" u2="&#x435;" k="19" />
    <hkern u1="&#x44e;" u2="&#x434;" k="9" />
    <hkern u1="&#x44f;" u2="&#x44f;" k="-10" />
    <hkern u1="&#x44f;" u2="&#x44d;" k="-10" />
    <hkern u1="&#x44f;" u2="&#x445;" k="-10" />
    <hkern u1="&#x44f;" u2="&#x443;" k="-10" />
    <hkern u1="&#x44f;" u2="&#x434;" k="-10" />
    <hkern u1="&#x451;" u2="&#x443;" k="-30" />
    <hkern u1="&#x451;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x451;" u2="&#x434;" k="-10" />
    <hkern u1="&#x454;" u2="&#x44f;" k="-40" />
    <hkern u1="&#x454;" u2="&#x443;" k="-40" />
    <hkern u1="&#x454;" u2="&#x43d;" k="-10" />
    <hkern u1="&#x454;" u2="&#x43b;" k="-20" />
    <hkern u1="&#x454;" u2="&#x436;" k="-10" />
    <hkern u1="&#x454;" u2="&#x430;" k="-20" />
    <hkern u1="&#x457;" u2="&#x457;" k="-30" />
    <hkern u1="&#x457;" u2="&#x454;" k="19" />
    <hkern u1="&#x457;" u2="&#x443;" k="-10" />
    <hkern u1="&#x457;" u2="&#x430;" k="19" />
    <hkern u1="&#x490;" u2="&#x491;" k="59" />
    <hkern u1="&#x490;" u2="&#x457;" k="-10" />
    <hkern u1="&#x490;" u2="&#x456;" k="99" />
    <hkern u1="&#x490;" u2="&#x454;" k="99" />
    <hkern u1="&#x490;" u2="&#x451;" k="69" />
    <hkern u1="&#x490;" u2="&#x44f;" k="59" />
    <hkern u1="&#x490;" u2="&#x44e;" k="99" />
    <hkern u1="&#x490;" u2="&#x44d;" k="89" />
    <hkern u1="&#x490;" u2="&#x44c;" k="49" />
    <hkern u1="&#x490;" u2="&#x44b;" k="29" />
    <hkern u1="&#x490;" u2="&#x44a;" k="69" />
    <hkern u1="&#x490;" u2="&#x449;" k="99" />
    <hkern u1="&#x490;" u2="&#x448;" k="99" />
    <hkern u1="&#x490;" u2="&#x447;" k="79" />
    <hkern u1="&#x490;" u2="&#x446;" k="99" />
    <hkern u1="&#x490;" u2="&#x445;" k="79" />
    <hkern u1="&#x490;" u2="&#x444;" k="99" />
    <hkern u1="&#x490;" u2="&#x443;" k="89" />
    <hkern u1="&#x490;" u2="&#x442;" k="79" />
    <hkern u1="&#x490;" u2="&#x441;" k="99" />
    <hkern u1="&#x490;" u2="&#x440;" k="99" />
    <hkern u1="&#x490;" u2="&#x43f;" k="99" />
    <hkern u1="&#x490;" u2="&#x43e;" k="99" />
    <hkern u1="&#x490;" u2="&#x43d;" k="49" />
    <hkern u1="&#x490;" u2="&#x43c;" k="79" />
    <hkern u1="&#x490;" u2="&#x43b;" k="59" />
    <hkern u1="&#x490;" u2="&#x43a;" k="69" />
    <hkern u1="&#x490;" u2="&#x439;" k="59" />
    <hkern u1="&#x490;" u2="&#x438;" k="79" />
    <hkern u1="&#x490;" u2="&#x437;" k="89" />
    <hkern u1="&#x490;" u2="&#x436;" k="99" />
    <hkern u1="&#x490;" u2="&#x435;" k="99" />
    <hkern u1="&#x490;" u2="&#x434;" k="109" />
    <hkern u1="&#x490;" u2="&#x433;" k="79" />
    <hkern u1="&#x490;" u2="&#x432;" k="99" />
    <hkern u1="&#x490;" u2="&#x431;" k="99" />
    <hkern u1="&#x490;" u2="&#x430;" k="139" />
    <hkern u1="&#x490;" u2="&#x42f;" k="39" />
    <hkern u1="&#x490;" u2="&#x425;" k="29" />
    <hkern u1="&#x490;" u2="&#x424;" k="29" />
    <hkern u1="&#x490;" u2="&#x421;" k="29" />
    <hkern u1="&#x490;" u2="&#x41e;" k="29" />
    <hkern u1="&#x490;" u2="&#x41b;" k="79" />
    <hkern u1="&#x490;" u2="&#x417;" k="29" />
    <hkern u1="&#x490;" u2="&#x416;" k="19" />
    <hkern u1="&#x490;" u2="&#x414;" k="99" />
    <hkern u1="&#x490;" u2="&#x410;" k="109" />
    <hkern u1="&#x491;" u2="&#x449;" k="19" />
    <hkern u1="&#x491;" u2="&#x444;" k="19" />
    <hkern u1="&#x491;" u2="&#x443;" k="-10" />
    <hkern u1="&#x491;" u2="&#x43e;" k="19" />
    <hkern u1="&#x491;" u2="&#x43a;" k="9" />
    <hkern u1="&#x491;" u2="&#x434;" k="49" />
    <hkern u1="&#x491;" u2="&#x432;" k="39" />
    <hkern u1="&#x491;" u2="&#x430;" k="59" />
  </font>
</defs></svg>
