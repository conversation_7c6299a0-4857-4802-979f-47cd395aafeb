/**
 * useRealtimeRefresh Hook
 *
 * This hook automatically refreshes data when relevant real-time events are received
 */

import { useEffect, useCallback, useState } from 'react';
import { useAppSelector } from '@/app/store/hooks';
import { selectRecentNotifications } from '@/app/store/slices/realtimeSlice';

interface RealtimeRefreshConfig {
  // Tables to listen for changes
  tables: string[];
  // Callback function to refresh data
  onRefresh: () => void;
  // Optional: debounce delay in milliseconds (default: 1000)
  debounceMs?: number;
  // Optional: enable/disable the hook
  enabled?: boolean;
}

export const useRealtimeRefresh = ({
  tables,
  onRefresh,
  debounceMs = 1000,
  enabled = true
}: RealtimeRefreshConfig) => {
  const recentNotifications = useAppSelector(state => selectRecentNotifications(state, 5));
  const [processedNotificationIds, setProcessedNotificationIds] = useState<Set<string>>(new Set());

  // Debug: Log the Redux state
  const allNotifications = useAppSelector(state => state.realtime.notifications);
  const realtimeState = useAppSelector(state => state.realtime);

  // Debounced refresh function
  const debouncedRefresh = useCallback(() => {
    const timeoutId = setTimeout(() => {
      onRefresh();
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [onRefresh, debounceMs]);

  useEffect(() => {
    if (!enabled || recentNotifications.length === 0) {
      return;
    }

    const latestNotification = recentNotifications[0];

    // Check if we've already processed this notification
    if (processedNotificationIds.has(latestNotification.id)) {
      return;
    }

    // Check if the notification is for a table we're interested in
    if (tables.includes(latestNotification.table)) {

      // Special handling for AnalyticsEvents to prevent infinite loops
      if (latestNotification.table === 'AnalyticsEvents') {
        const eventType = latestNotification.data?.event_type;

        // Ignore admin panel events that would cause infinite loops
        const adminEvents = [
          'dashboard_loading',
          'dashboard_loaded',
          'dashboard_data_loading',
          'dashboard_data_loaded',
          'admin_page_view',
          'admin_navigation'
        ];

        if (adminEvents.includes(eventType)) {
          setProcessedNotificationIds(prev => new Set([...prev, latestNotification.id]));
          return;
        }
      }

      // Mark this notification as processed
      setProcessedNotificationIds(prev => new Set([...prev, latestNotification.id]));

      // Call the debounced refresh
      const cleanup = debouncedRefresh();
      return cleanup;
    } else {
      // Still mark as processed to avoid checking again
      setProcessedNotificationIds(prev => new Set([...prev, latestNotification.id]));
    }
  }, [recentNotifications, tables, debouncedRefresh, enabled, processedNotificationIds]);
};

// Predefined configurations for common use cases
export const REALTIME_CONFIGS = {
  // For pages that show transaction data
  TRANSACTIONS: {
    tables: ['Orders', 'VendorStands', 'CharityDonations'],
  },

  // For pages that show analytics data
  ANALYTICS: {
    tables: ['AnalyticsEvents'],
  },

  // For pages that show user data
  USERS: {
    tables: ['AdminUsers', 'NewsletterSubscribers'],
  },

  // For pages that show booking data
  BOOKINGS: {
    tables: ['SantaBookings', 'VendorStands'],
  },

  // For dashboard that shows everything
  DASHBOARD: {
    tables: ['Orders', 'VendorStands', 'CharityDonations', 'AnalyticsEvents', 'SantaBookings'],
  },
};

// Convenience hooks for common use cases
export const useTransactionRefresh = (onRefresh: () => void, enabled = true) => {
  return useRealtimeRefresh({
    ...REALTIME_CONFIGS.TRANSACTIONS,
    onRefresh,
    enabled,
  });
};

export const useAnalyticsRefresh = (onRefresh: () => void, enabled = true) => {
  // Custom analytics refresh that filters out admin events
  const recentNotifications = useAppSelector(state => selectRecentNotifications(state, 5));
  const [processedNotificationIds, setProcessedNotificationIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!enabled || recentNotifications.length === 0) return;

    const latestNotification = recentNotifications[0];

    // Check if we've already processed this notification
    if (processedNotificationIds.has(latestNotification.id)) {
      return;
    }

    if (latestNotification.table === 'AnalyticsEvents') {
      const eventType = latestNotification.data?.event_type;

      // Only refresh for actual user events, not admin events
      // Only count actual page views, not load metrics or UI interactions
      const userEvents = [
        'page_view',
        'first_visit',
        'session_start'
      ];

      if (userEvents.includes(eventType)) {
        // Mark this notification as processed
        setProcessedNotificationIds(prev => new Set([...prev, latestNotification.id]));

        onRefresh();
      } else {
        // Still mark as processed to avoid checking again
        setProcessedNotificationIds(prev => new Set([...prev, latestNotification.id]));
      }
    }
  }, [recentNotifications, onRefresh, enabled, processedNotificationIds]);

  // Clean up old processed IDs to prevent memory leaks
  useEffect(() => {
    const cleanup = setInterval(() => {
      setProcessedNotificationIds(prev => {
        // Keep only the last 50 processed IDs
        const ids = Array.from(prev);
        if (ids.length > 50) {
          return new Set(ids.slice(-50));
        }
        return prev;
      });
    }, 60000); // Clean up every minute

    return () => clearInterval(cleanup);
  }, []);
};

export const useDashboardRefresh = (onRefresh: () => void, enabled = true) => {
  return useRealtimeRefresh({
    ...REALTIME_CONFIGS.DASHBOARD,
    onRefresh,
    enabled,
  });
};