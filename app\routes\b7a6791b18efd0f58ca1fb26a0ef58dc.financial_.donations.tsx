import { useState, useEffect, useMemo, useCallback } from 'react';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { DonationTrendsChart } from '@/app/components/admin/charts/DonationTrendsChart';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchDonationMetrics } from '@/app/store/slices/adminSlice';
import { format } from 'date-fns';
import { elFormatter } from '@/lib/utils';

interface Donation {
    id: string;
    donor_name: string;
    donor_email: string;
    amount: number;
    created_at: string;
    campaign: string;
    is_recurring: boolean;
    status: 'completed' | 'pending' | 'failed';
    payment_method: string;
    receipt_url?: string;
    metadata?: {
        multiplier_used?: number;
        order_id?: string;
        donor_name?: string;
        donor_email?: string;
    };
}

export default function DonationsDashboard() {
    const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
    const [filteredDonations, setFilteredDonations] = useState<Donation[]>([]);

    const dispatch = useAppDispatch();

    const { data: donationMetrics, loading } = useAppSelector(
        (state) => state.admin.donations
    );
    useEffect(() => {
        dispatch(fetchDonationMetrics({ timeRange }));
    }, [dispatch, timeRange]);

    useEffect(() => {
        if (donationMetrics?.recentDonations) {
            setFilteredDonations(donationMetrics.recentDonations);
        }
    }, [donationMetrics?.recentDonations]);

    // Transform donation metrics for the UI
    const stats = useMemo(() => {
        if (!donationMetrics) return null;

        return {
            totalDonations: donationMetrics.totalDonations,
            monthlyDonations: donationMetrics.monthlyDonations,
            recurringDonations: donationMetrics.recurringDonations,
            topDonors: donationMetrics.topDonors,
            campaignBreakdown: Object.entries(donationMetrics.campaignTotals || {}).map(([name, value]) => ({
                name,
                value
            })),
            dailyDonations: donationMetrics.dailyDonations,
            recentDonations: donationMetrics.recentDonations
        };
    }, [donationMetrics]);

    // Calculate month-over-month percentage change
    const calculateMonthOverMonthChange = useCallback((currentMonthTotal: number, previousMonthTotal: number) => {
        // If both are 0, there's no change
        if (currentMonthTotal === 0 && previousMonthTotal === 0) return 0;
        // If previous month was 0 but current has value, it's a 100% increase
        if (previousMonthTotal === 0) return 100;
        // Otherwise calculate the percentage change
        return Math.round(((currentMonthTotal - previousMonthTotal) / previousMonthTotal) * 100);
    }, []);

    // Get current and previous month totals
    const currentMonthTotal = stats?.monthlyDonations || 0;
    const previousMonthTotal = useMemo(() => {
        if (!stats?.dailyDonations) return 0;
        const now = new Date();
        const oneMonthAgo = new Date(now);
        oneMonthAgo.setMonth(now.getMonth() - 1);
        const twoMonthsAgo = new Date(now);
        twoMonthsAgo.setMonth(now.getMonth() - 2);

        return stats.dailyDonations
            .filter(({ date }) => {
                const donationDate = new Date(date);
                return donationDate >= twoMonthsAgo && donationDate < oneMonthAgo;
            })
            .reduce((sum, { amount }) => sum + (amount || 0), 0);
    }, [stats?.dailyDonations]);

    const monthOverMonthChange = calculateMonthOverMonthChange(currentMonthTotal, previousMonthTotal);

    const salesData = useMemo(() => {
        if (!stats?.dailyDonations) return [];
        return stats.dailyDonations.map(item => ({
            date: item.date,
            value: item.amount,
            revenue: item.amount,
            name: 'Donations'
        }));
    }, [stats?.dailyDonations]);

    return (
        <ProtectedRoute>
            <AdminLayout>
                <div className="space-y-6">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold text-white">Donations</h1>
                            <p className="text-amber-100/70">Track and manage donations and fundraising</p>
                        </div>
                        <div className="flex space-x-2">
                            <select
                                value={timeRange}
                                onChange={(e) => setTimeRange(e.target.value as 'week' | 'month' | 'year')}
                                className="bg-zinc-700 border border-amber-600/50 text-amber-100 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
                            >
                                <option value="week">Last 7 Days</option>
                                <option value="month">Last 30 Days</option>
                                <option value="year">Last 12 Months</option>
                            </select>
                            {/* <button className="bg-amber-500 hover:bg-amber-400 text-amber-900 font-medium px-4 py-1.5 rounded-md text-sm transition-colors">
                                + New Donation
                            </button> */}
                        </div>
                    </div>

                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
                        </div>
                    ) : (
                        <>
                            {/* Stats Cards */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">Total Donations</p>
                                    <p className="text-2xl font-bold text-white">
                                        €{elFormatter(stats?.totalDonations || 0, 0)}
                                    </p>
                                    <p className="text-xs text-amber-400 mt-1">All time</p>
                                </div>
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">This Month</p>
                                    <p className="text-2xl font-bold text-white">
                                        €{elFormatter(stats?.monthlyDonations || 0, 0)}
                                    </p>
                                    <p className={`text-xs flex items-center mt-1 ${monthOverMonthChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                        <span className="mr-1">{monthOverMonthChange >= 0 ? '↑' : '↓'}</span>
                                        {Math.abs(monthOverMonthChange)}% {monthOverMonthChange >= 0 ? 'increase' : 'decrease'} from last month
                                    </p>
                                </div>
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">Recurring Donations</p>
                                    <p className="text-2xl font-bold text-white">{stats?.recurringDonations}</p>
                                    <p className="text-xs text-amber-400 mt-1">Active monthly donors</p>
                                </div>
                            </div>

                            {/* Charts */}
                            <div className="grid grid-cols-1">
                                {/* Donation Trends Chart */}
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <h3 className="text-lg font-semibold text-white mb-4">Donation Trends</h3>
                                    <div className="h-96">
                                        <DonationTrendsChart
                                            data={salesData}
                                            tickFormat={(value) => `€${elFormatter(value || 0, 0)}`}
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Recent Donations */}
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-semibold text-white">Recent Donations</h3>
                                    <button className="text-amber-400 hover:text-amber-300 text-sm font-medium">
                                        View All
                                    </button>
                                </div>

                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-amber-600/30">
                                        <thead>
                                            <tr>
                                                <th scope="col" className="px-6 pl-0 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                    Donor
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                    Email
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                    Date
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                    Amount
                                                </th>
                                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                    Status
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-amber-600/30">
                                            {filteredDonations.length > 0 ? (
                                                filteredDonations.map((donation) => (
                                                    <tr key={donation.id} className="hover:bg-zinc-700/50">
                                                        <td className="px-6 pl-0 py-4 whitespace-nowrap text-sm font-medium text-white">
                                                            {donation?.metadata?.donor_name || 'Anonymous'}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                                            {donation?.metadata?.email || 'No email'}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                                            {format(new Date(donation.created_at), 'MMM d, yyyy')}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-amber-400 font-medium">
                                                            €{donation.amount.toFixed(2)}
                                                            {donation.metadata?.multiplier_used && (
                                                                <span className="ml-1 text-xs bg-amber-900/50 text-amber-200 px-1.5 py-0.5 rounded">
                                                                    ×{donation.metadata.multiplier_used}
                                                                </span>
                                                            )}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${donation.status === 'completed'
                                                                ? 'bg-green-900/20 text-green-400'
                                                                : donation.status === 'pending'
                                                                    ? 'bg-amber-900/20 text-amber-400'
                                                                    : 'bg-red-900/20 text-red-400'
                                                                }`}>
                                                                {donation.status?.charAt(0).toUpperCase() + donation.status?.slice(1) || 'Unknown'}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                ))
                                            ) : (
                                                <tr>
                                                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-400">
                                                        No donations found for the selected time period
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            {/* Top Donors */}
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <h3 className="text-lg font-semibold text-white mb-4">Top Donors</h3>
                                <div className="space-y-4">
                                    {stats?.topDonors && stats.topDonors.length > 0 ? (
                                        stats.topDonors.map((donor, index) => (
                                            <div key={index} className="flex items-center">
                                                <div className="w-1/4 text-sm font-medium text-amber-100">{donor.name}</div>
                                                <div className="flex-1 flex items-center">
                                                    <div className="w-full bg-zinc-700 rounded-full h-2.5 mr-2">
                                                        <div
                                                            className="bg-amber-500 h-2.5 rounded-full"
                                                            style={{
                                                                width: `${(donor.amount / (stats?.topDonors[0]?.amount || 1)) * 100}%`
                                                            }}
                                                        ></div>
                                                    </div>
                                                    <span className="text-sm font-medium text-white w-24 text-right">
                                                        €{donor.amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                                    </span>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-center py-4 text-gray-400">
                                            No donors found for the selected time period
                                        </div>
                                    )}
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </ProtectedRoute>
    );
};