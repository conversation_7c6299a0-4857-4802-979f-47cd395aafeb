/**
 * Simple Real-time Test Script
 *
 * This script tests basic real-time subscription functionality
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://trbcgeffweprfnovhggm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYmNnZWZmd2VwcmZub3ZoZ2dtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mjc1NjI0NSwiZXhwIjoyMDU4MzMyMjQ1fQ.ZkQQbOrwUN7Eqert9omwEPwLSj6itNr6vZB4dv1tXOI';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testRealtimeSubscription() {
  console.log('🚀 Testing Real-time Subscription\n');

  // Test subscription to Orders table
  const channel = supabase
    .channel('test-orders')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'Orders',
      },
      (payload) => {
        console.log('📨 Received Orders change:', payload);
      }
    )
    .subscribe((status, err) => {
      console.log(`📡 Orders subscription status: ${status}`);
      if (err) {
        console.error('❌ Subscription error:', err);
      }
      if (status === 'SUBSCRIBED') {
        console.log('✅ Successfully subscribed to Orders');

        // Test inserting a record after subscription is active
        setTimeout(async () => {
          console.log('\n🧪 Testing record insertion...');

          const testOrder = {
            name: 'Real-time Test Customer',
            email: '<EMAIL>',
            total: 99.99,
            status: 'pending',
            payment_intent_id: `realtime_test_${Date.now()}`
          };

          const { data, error } = await supabase
            .from('Orders')
            .insert([testOrder])
            .select()
            .single();

          if (error) {
            console.error('❌ Insert error:', error);
          } else {
            console.log('✅ Test order created:', data.id);

            // Clean up after 5 seconds
            setTimeout(async () => {
              await supabase.from('Orders').delete().eq('id', data.id);
              console.log('🧹 Cleaned up test order');

              // Unsubscribe and exit
              supabase.removeChannel(channel);
              console.log('👋 Test complete');
              process.exit(0);
            }, 5000);
          }
        }, 2000);
      } else if (status === 'CHANNEL_ERROR') {
        console.error('❌ Channel error occurred');
        process.exit(1);
      } else if (status === 'CLOSED') {
        console.warn('⚠️ Channel was closed');
      }
    });

  // Keep the script running
  console.log('⏳ Waiting for subscription to establish...');
}

testRealtimeSubscription().catch((error) => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});