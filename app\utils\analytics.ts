import { getSupabaseClient } from './supabaseClient';
import { getIpAddress } from '@/lib/utils';

// Event type definitions
export type AnalyticsEventType =
  | 'page_view'
  | 'first_visit'
  | 'device_detected'
  | 'user_demographics'
  | 'login_error'
  | 'login_attempt'
  | 'login_failed'
  | 'form_interaction'
  | 'ui_interaction'
  | 'error_occurred'
  | 'session_start'
  | 'session_end'
  // Navigation events
  | 'navigation_click'
  | 'external_link_click'
  | 'scroll_depth'
  // Ticket purchase journey
  | 'ticket_selection_started'
  | 'ticket_added_to_cart'
  | 'ticket_removed_from_cart'
  | 'cart_updated'
  | 'checkout_started'
  | 'payment_method_selected'
  | 'payment_initiated'
  | 'payment_processing'
  | 'payment_completed'
  | 'payment_failed'
  | 'payment_cancelled'
  | 'ticket_confirmation_viewed'
  // Vendor stand journey
  | 'vendor_map_viewed'
  | 'vendor_stand_selected'
  | 'vendor_form_started'
  | 'vendor_form_completed'
  | 'vendor_payment_initiated'
  | 'vendor_payment_completed'
  | 'vendor_payment_failed'
  | 'vendor_booking_confirmed'
  // Donation journey
  | 'donation_started'
  | 'donation_amount_selected'
  | 'donation_multiplier_applied'
  | 'donation_payment_initiated'
  | 'donation_payment_completed'
  | 'donation_payment_failed'
  | 'donation_confirmation_viewed'
  // Form interactions
  | 'newsletter_signup_started'
  | 'newsletter_signup_completed'
  | 'newsletter_signup_failed'
  | 'event_application_started'
  | 'event_application_completed'
  | 'event_application_failed'
  // Content engagement
  | 'hero_cta_clicked'
  | 'section_viewed'
  | 'image_gallery_viewed'
  | 'video_played'
  | 'social_media_clicked'
  // Search and filtering
  | 'search_performed'
  | 'filter_applied'
  | 'sort_changed'
  // Admin events
  | 'admin_login_attempt'
  | 'admin_login_success'
  | 'admin_action_performed';

interface PageViewMetadata {
  type?: string;
  metrics?: {
    load_time?: number;
    dom_content_loaded?: number;
    first_paint?: number;
    first_contentful_paint?: number;
  };
  [key: string]: any; // Allow additional properties
}

// Track a page view
export const trackPageView = async (path: string, metadata: PageViewMetadata = {}) => {
  if (typeof window === 'undefined') return;

  // Skip analytics tracking for admin routes to prevent infinite loops
  if (path.startsWith('/admin') || path.startsWith('/b7a6791b18efd0f58ca1fb26a0ef58dc')) {
    return;
  }

  // Deduplication: Check if we've already tracked this page view recently
  // Include metadata type in the deduplication key to allow different types
  const metadataType = metadata.type || 'page_view';
  const lastTrackedKey = `last_pageview_${path}_${metadataType}`;
  const lastTracked = sessionStorage.getItem(lastTrackedKey);
  const now = Date.now();

  // If we tracked this exact path+type within the last 3 seconds, skip it
  if (lastTracked && (now - parseInt(lastTracked)) < 3000) {
    return;
  }

  // Store the current timestamp for this path+type combination
  sessionStorage.setItem(lastTrackedKey, now.toString());

  // Check if this is the user's first visit
  if (!sessionStorage.getItem('has_visited')) {
    sessionStorage.setItem('has_visited', 'true');
    await trackFirstVisit();
  }

  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) {
      console.warn('Supabase client not available for tracking page view');
      return;
    }

    const { ipAddress, userAgent, sessionId, referrer } = await getIpAddress();

    // Get device and browser information
    const deviceType = getDeviceType();
    const browser = getBrowser();
    const os = getOS();
    const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
    const screenResolution = `${window.screen.width}x${window.screen.height}`;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Prepare the event data with additional device information
    const eventData = {
      ...metadata,
      device_info: {
        type: deviceType,
        browser,
        os,
        viewport_size: viewportSize,
        screen_resolution: screenResolution,
        language,
        timezone,
        is_touch_device: isTouchDevice,
        color_depth: window.screen.colorDepth,
        pixel_ratio: window.devicePixelRatio || 1,
        hardware_concurrency: navigator.hardwareConcurrency || null,
        device_memory: (navigator as any).deviceMemory || null,
      }
    };

    // Use different event types based on metadata to avoid conflicts
    const eventType = metadataType === 'page_view' ? 'page_view' :
                     metadataType === 'page_load' ? 'page_load_metrics' :
                     metadataType === 'click' ? 'ui_interaction' : 'page_view';

    // Insert the page view with all device information
    const { error } = await supabaseClient.from('AnalyticsEvents').insert({
      event_type: eventType,
      event_data: eventData,
      page_path: path,
      referrer: referrer || document.referrer || '',
      user_agent: userAgent,
      session_id: sessionId,
      user_id: null, // Will be set if user is authenticated
      ip_address: ipAddress,
      device_type: deviceType,
      browser: browser,
      os: os,
      viewport_size: viewportSize,
      screen_resolution: screenResolution,
      language: language,
      timezone: timezone,
      is_touch_device: isTouchDevice,
    });

    if (error) {
      console.error('Error tracking page view:', error);
    }
  } catch (error) {
    console.error('Error tracking page view:', error);
  }
};

// Track first visit event
const trackFirstVisit = async () => {
  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return;

    const { ipAddress, userAgent, sessionId, referrer } = await getIpAddress();

    // Get device and browser information
    const deviceType = getDeviceType();
    const browser = getBrowser();
    const os = getOS();
    const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
    const screenResolution = `${window.screen.width}x${window.screen.height}`;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Prepare the event data with additional device information
    const eventData = {
      device_info: {
        type: deviceType,
        browser,
        os,
        viewport_size: viewportSize,
        screen_resolution: screenResolution,
        language,
        timezone,
        is_touch_device: isTouchDevice,
        color_depth: window.screen.colorDepth,
        pixel_ratio: window.devicePixelRatio || 1,
        hardware_concurrency: navigator.hardwareConcurrency || null,
        device_memory: (navigator as any).deviceMemory || null,
      }
    };

    await supabaseClient.from('AnalyticsEvents').insert({
      event_type: 'first_visit',
      event_data: eventData,
      page_path: window.location.pathname,
      referrer: referrer || document.referrer || '',
      user_agent: userAgent,
      session_id: sessionId,
      user_id: null, // Will be set if user is authenticated
      ip_address: ipAddress,
      device_type: deviceType,
      browser: browser,
      os: os,
      viewport_size: viewportSize,
      screen_resolution: screenResolution,
      language: language,
      timezone: timezone,
      is_touch_device: isTouchDevice,
    });
  } catch (error) {
    console.error('Error tracking first visit:', error);
  }
};

// Track device detection
const trackDeviceDetection = async (detectedDeviceType: string) => {
  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return;

    const { ipAddress, userAgent, sessionId, referrer } = await getIpAddress();

    // Get comprehensive device information
    const deviceType = getDeviceType();
    const browser = getBrowser();
    const os = getOS();
    const viewportSize = `${window.innerWidth}x${window.innerHeight}`;
    const screenResolution = `${window.screen.width}x${window.screen.height}`;
    const language = navigator.language;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Prepare the event data with additional device information
    const eventData = {
      detected_device_type: detectedDeviceType,
      device_info: {
        type: deviceType,
        browser,
        os,
        viewport_size: viewportSize,
        screen_resolution: screenResolution,
        language,
        timezone,
        is_touch_device: isTouchDevice,
        color_depth: window.screen.colorDepth,
        pixel_ratio: window.devicePixelRatio || 1,
        hardware_concurrency: navigator.hardwareConcurrency || null,
        device_memory: (navigator as any).deviceMemory || null,
      }
    };

    await supabaseClient.from('AnalyticsEvents').insert({
      event_type: 'device_detected',
      event_data: eventData,
      page_path: window.location.pathname,
      referrer: referrer || document.referrer || '',
      user_agent: userAgent,
      session_id: sessionId,
      user_id: null,
      device_type: deviceType,
      browser: browser,
      os: os,
      viewport_size: viewportSize,
      screen_resolution: screenResolution,
      language: language,
      timezone: timezone,
      is_touch_device: isTouchDevice,
      ip_address: ipAddress,
    });
  } catch (error) {
    console.error('Error tracking device detection:', error);
  }
};

// Track a custom event
export const trackEvent = async (eventName: AnalyticsEventType, eventData: Record<string, any> = {}) => {
  if (typeof window === 'undefined') return;

  // Skip analytics tracking for admin-related events to prevent infinite loops
  const adminEvents = ['dashboard_loading', 'dashboard_loaded', 'dashboard_data_loading', 'dashboard_data_loaded'];
  if (adminEvents.includes(eventName)) {
    return;
  }

  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return;

    const ipData = await getIpAddress();

    const { ipAddress, userAgent, sessionId, referrer } = ipData;

    // Auto-detect device type if not provided
    const deviceType = getDeviceType();

    await supabaseClient
      .from('AnalyticsEvents')
      .insert({
        event_type: eventName,
        event_data: eventData,
        page_path: window.location.pathname,
        referrer: document.referrer || '',
        user_agent: userAgent,
        session_id: sessionId,
        user_id: null, // Will be set if user is authenticated
        ip_address: ipAddress,
        device_type: deviceType,
        browser: getBrowser(),
        os: getOS(),
        screen_resolution: `${window.screen.width}x${window.screen.height}`,
        viewport_size: `${window.innerWidth}x${window.innerHeight}`
      });

    // Track device type if this is a page view or session event
    if (['page_view', 'session_start'].includes(eventName)) {
      await trackDeviceDetection(deviceType);
    }
  } catch (error) {
    console.error('Error tracking event:', error);
  }
};

// Helper functions to extract device and browser info
const getDeviceType = (): string => {
  const ua = navigator.userAgent;
  if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(ua)) {
    return 'tablet';
  } else if (/mobile|android|iphone|ipod|phone|blackberry|iemobile|kindle|silk-accelerated|(android.*mobile)/i.test(ua)) {
    return 'mobile';
  }
  return 'desktop';
};

const getBrowser = (): string => {
  const ua = navigator.userAgent;
  let browser = 'Unknown';

  if (ua.indexOf('Firefox') > -1) browser = 'Firefox';
  else if (ua.indexOf('SamsungBrowser') > -1) browser = 'Samsung Browser';
  else if (ua.indexOf('Opera') > -1 || ua.indexOf('OPR') > -1) browser = 'Opera';
  else if (ua.indexOf('Trident') > -1) browser = 'Internet Explorer';
  else if (ua.indexOf('Edge') > -1) browser = 'Edge';
  else if (ua.indexOf('Chrome') > -1) browser = 'Chrome';
  else if (ua.indexOf('Safari') > -1) browser = 'Safari';

  return browser;
};

const getOS = (): string => {
  const ua = navigator.userAgent;
  if (/Windows/.test(ua)) return 'Windows';
  if (/Mac OS X/.test(ua)) return 'macOS';
  if (/Linux/.test(ua)) return 'Linux';
  if (/Android/.test(ua)) return 'Android';
  if (/iOS|iPhone|iPad|iPod/.test(ua)) return 'iOS';
  return 'Unknown';
};

// Track user demographics (to be called when user registers or updates profile)
export const trackUserDemographics = async (userId: string, demographics: {
  age?: number;
  gender?: string;
  country?: string;
  city?: string;
}) => {
  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return;

    const { ipAddress, userAgent, sessionId } = await getIpAddress();

    await supabaseClient
      .from('AnalyticsEvents')
      .insert({
        event_type: 'user_demographics',
        event_data: demographics,
        page_path: window.location.pathname,
        referrer: document.referrer || '',
        user_agent: userAgent,
        session_id: sessionId,
        user_id: userId,
        ip_address: ipAddress,
      });
  } catch (error) {
    console.error('Error tracking user demographics:', error);
  }
};

// Get active users count (users with events in the last X minutes)
export const getActiveUsersCount = async (minutes = 30): Promise<number> => {
  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return 0;

    const { data, error } = await supabaseClient
      .from('AnalyticsEvents')
      .select('session_id', { count: 'exact', head: true })
      .gte('created_at', new Date(Date.now() - minutes * 60 * 1000).toISOString())
      .not('session_id', 'is', null);

    if (error) throw error;
    return data?.length || 0;
  } catch (error) {
    console.error('Error getting active users count:', error);
    return 0;
  }
};

// Get conversion rate (sessions with purchases / total sessions)
export const getConversionRate = async (days = 30): Promise<number> => {
  try {
    const supabaseClient = getSupabaseClient();
    if (!supabaseClient) return 0;

    // Get total unique sessions
    const { data: sessionsData, error: sessionsError } = await supabaseClient
      .from('AnalyticsEvents')
      .select('session_id')
      .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .not('session_id', 'is', null);

    if (sessionsError) throw sessionsError;
    const totalSessions = new Set(sessionsData?.map(e => e.session_id)).size;
    if (totalSessions === 0) return 0;

    // Get sessions with purchases
    const { data: purchaseData, error: purchaseError } = await supabaseClient
      .from('AnalyticsEvents')
      .select('session_id')
      .eq('event_type', 'payment_completed')
      .gte('created_at', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .not('session_id', 'is', null);

    if (purchaseError) throw purchaseError;
    const purchaseSessions = new Set(purchaseData?.map(e => e.session_id)).size;

    return (purchaseSessions / totalSessions) * 100;
  } catch (error) {
    console.error('Error calculating conversion rate:', error);
    return 0;
  }
};

// Specialized tracking functions for different user journeys

// Track ticket purchase journey
export const trackTicketJourney = {
  selectionStarted: (ticketType?: string) => trackEvent('ticket_selection_started', { ticket_type: ticketType }),
  addedToCart: (ticketType: string, quantity: number, price: number) =>
    trackEvent('ticket_added_to_cart', { ticket_type: ticketType, quantity, price }),
  removedFromCart: (ticketType: string, quantity: number) =>
    trackEvent('ticket_removed_from_cart', { ticket_type: ticketType, quantity }),
  cartUpdated: (totalItems: number, totalValue: number) =>
    trackEvent('cart_updated', { total_items: totalItems, total_value: totalValue }),
  checkoutStarted: (totalValue: number, itemCount: number) =>
    trackEvent('checkout_started', { total_value: totalValue, item_count: itemCount }),
  paymentMethodSelected: (method: string) =>
    trackEvent('payment_method_selected', { payment_method: method }),
  paymentInitiated: (amount: number, paymentIntentId?: string) =>
    trackEvent('payment_initiated', { amount, payment_intent_id: paymentIntentId, transaction_type: 'ticket' }),
  paymentProcessing: (paymentIntentId: string) =>
    trackEvent('payment_processing', { payment_intent_id: paymentIntentId, transaction_type: 'ticket' }),
  paymentCompleted: (amount: number, paymentIntentId: string, orderId?: string) =>
    trackEvent('payment_completed', { amount, payment_intent_id: paymentIntentId, order_id: orderId, transaction_type: 'ticket' }),
  paymentFailed: (error: string, paymentIntentId?: string) =>
    trackEvent('payment_failed', { error, payment_intent_id: paymentIntentId, transaction_type: 'ticket' }),
  paymentCancelled: (paymentIntentId?: string) =>
    trackEvent('payment_cancelled', { payment_intent_id: paymentIntentId, transaction_type: 'ticket' }),
  confirmationViewed: (orderId: string) =>
    trackEvent('ticket_confirmation_viewed', { order_id: orderId })
};

// Track vendor stand journey
export const trackVendorJourney = {
  mapViewed: () => trackEvent('vendor_map_viewed'),
  standSelected: (standNumber: number, price: number, isArtist?: boolean) =>
    trackEvent('vendor_stand_selected', { stand_number: standNumber, price, is_artist: isArtist }),
  formStarted: (standNumber: number) =>
    trackEvent('vendor_form_started', { stand_number: standNumber }),
  formCompleted: (standNumber: number, businessName: string) =>
    trackEvent('vendor_form_completed', { stand_number: standNumber, business_name: businessName }),
  paymentInitiated: (amount: number, standNumber: number, paymentIntentId?: string) =>
    trackEvent('vendor_payment_initiated', { amount, stand_number: standNumber, payment_intent_id: paymentIntentId, transaction_type: 'vendor_stand' }),
  paymentCompleted: (amount: number, standNumber: number, paymentIntentId: string) =>
    trackEvent('vendor_payment_completed', { amount, stand_number: standNumber, payment_intent_id: paymentIntentId, transaction_type: 'vendor_stand' }),
  paymentFailed: (error: string, standNumber: number, paymentIntentId?: string) =>
    trackEvent('vendor_payment_failed', { error, stand_number: standNumber, payment_intent_id: paymentIntentId, transaction_type: 'vendor_stand' }),
  bookingConfirmed: (standNumber: number, businessName: string) =>
    trackEvent('vendor_booking_confirmed', { stand_number: standNumber, business_name: businessName })
};

// Track donation journey
export const trackDonationJourney = {
  started: () => trackEvent('donation_started'),
  amountSelected: (amount: number) =>
    trackEvent('donation_amount_selected', { amount }),
  multiplierApplied: (multiplier: number, originalAmount: number, finalAmount: number) =>
    trackEvent('donation_multiplier_applied', { multiplier, original_amount: originalAmount, final_amount: finalAmount }),
  paymentInitiated: (amount: number, paymentIntentId?: string) =>
    trackEvent('donation_payment_initiated', { amount, payment_intent_id: paymentIntentId, transaction_type: 'donation' }),
  paymentCompleted: (amount: number, paymentIntentId: string, multiplier?: number) =>
    trackEvent('donation_payment_completed', { amount, payment_intent_id: paymentIntentId, multiplier, transaction_type: 'donation' }),
  paymentFailed: (error: string, amount: number, paymentIntentId?: string) =>
    trackEvent('donation_payment_failed', { error, amount, payment_intent_id: paymentIntentId, transaction_type: 'donation' }),
  confirmationViewed: (amount: number, multiplier?: number) =>
    trackEvent('donation_confirmation_viewed', { amount, multiplier })
};

// Track form interactions
export const trackFormJourney = {
  newsletterStarted: () => trackEvent('newsletter_signup_started'),
  newsletterCompleted: (email: string) =>
    trackEvent('newsletter_signup_completed', { email_domain: email.split('@')[1] }),
  newsletterFailed: (error: string) =>
    trackEvent('newsletter_signup_failed', { error }),
  eventApplicationStarted: (applicationType: string) =>
    trackEvent('event_application_started', { application_type: applicationType }),
  eventApplicationCompleted: (applicationType: string, businessName?: string) =>
    trackEvent('event_application_completed', { application_type: applicationType, business_name: businessName }),
  eventApplicationFailed: (error: string, applicationType: string) =>
    trackEvent('event_application_failed', { error, application_type: applicationType })
};

// Track navigation and content engagement
export const trackNavigation = {
  menuClick: (menuItem: string, href: string) =>
    trackEvent('navigation_click', { menu_item: menuItem, href }),
  externalLinkClick: (url: string, context: string) =>
    trackEvent('external_link_click', { url, context }),
  heroCTAClick: (ctaText: string) =>
    trackEvent('hero_cta_clicked', { cta_text: ctaText }),
  sectionViewed: (sectionName: string, scrollDepth?: number) =>
    trackEvent('section_viewed', { section_name: sectionName, scroll_depth: scrollDepth }),
  socialMediaClick: (platform: string, url: string) =>
    trackEvent('social_media_clicked', { platform, url })
};