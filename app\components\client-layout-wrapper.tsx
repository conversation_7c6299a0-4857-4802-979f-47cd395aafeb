import React from "react";

import "@/app/tailwind.css";

import Header from "@/app/components/Layout/Header";
import Footer from "@/app/components/Layout/Footer";
//import { ReactScan } from './ReactScan';

import { ClientProviders } from "./client-providers";

export function ClientLayoutWrapper({ children }: { children: React.ReactNode }) {
  return (
    <ClientProviders>
      <Header />
      {children}
      <Footer />
    </ClientProviders>
  );
};