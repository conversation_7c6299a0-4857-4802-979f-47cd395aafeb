"use client";

import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';
import { format, parse } from 'date-fns';
import { toast } from 'react-toastify';
import { TicketType } from '@/types/ticket';

interface SantaSlot {
  id: number;
  day: string;
  startTime: string;
  endTime: string;
  available: number;
  isAvailable: boolean;
}

interface SantaSlotModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSlotSelect: (ticket: TicketType, slotId: number, day: string, timeRange: string, numChildren?: number) => void;
  selectedTicket: TicketType | null;
}

const SantaSlotModal: React.FC<SantaSlotModalProps> = ({ isOpen, onClose, onSlotSelect, selectedTicket }) => {
  const [slots, setSlots] = useState<SantaSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDay, setSelectedDay] = useState<string>('Saturday');
  const [selectedSlot, setSelectedSlot] = useState<SantaSlot | null>(null);
  const [additionalChildren, setAdditionalChildren] = useState<number>(0);

  const modalRef = useRef<HTMLDivElement>(null);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, () => {
    // Do nothing - disabled outside click closing
    // Only close via explicit buttons
  });

  useEffect(() => {
    if (isOpen) {
      fetchSlots();
      setAdditionalChildren(0); // Reset additional children count when modal opens
    }

    if (!isOpen) {
      setSelectedDay('Saturday');
      setSelectedSlot(null);
    }
  }, [isOpen]);

  const fetchSlots = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/santa-slots');

      if (!response.ok) {
        throw new Error('Failed to fetch slots');
      }

      const data = await response.json();

      // Adjust availability for lunch breaks (12-2 PM)
      const adjustedSlots = data.slots.map((slot: SantaSlot) => {
        // Parse the time to check if it's between 12-2 PM
        try {
          const startTime = parse(slot.startTime, 'HH:mm:ss', new Date());
          const startHour = startTime.getHours();

          // If the slot is between 12-2 PM, adjust availability to 1
          if (startHour >= 12 && startHour < 14) {
            return {
              ...slot,
              available: Math.min(slot.available, 1), // Limit to max 1 available
              isAvailable: slot.available > 0 // Still available if at least 1 slot
            };
          }

          return slot;
        } catch (error) {
          console.error('Error parsing time:', error);
          return slot;
        }
      });

      setSlots(adjustedSlots);
    } catch (error: any) {
      console.error('Error fetching slots:', error.message);
      toast.error('Failed to load Santa time slots');
    } finally {
      setLoading(false);
    }
  };

  const formatTimeSlot = (startTime: string, endTime: string) => {
    try {
      const start = parse(startTime, 'HH:mm:ss', new Date());
      const end = parse(endTime, 'HH:mm:ss', new Date());
      return `${format(start, 'h:mm a')} - ${format(end, 'h:mm a')}`;
    } catch (error: any) {
      console.error('Error formatting time slot:', error.message);
      return `${startTime} - ${endTime}`;
    }
  };

  const handleSlotSelect = (slot: SantaSlot) => {
    if (!slot.isAvailable) {
      toast.error('This slot is fully booked');
      return;
    }

    setSelectedSlot(slot);
  };

  const handleConfirm = () => {
    if (!selectedSlot) {
      toast.error('Please select a time slot');
      return;
    }

    const timeRange = formatTimeSlot(selectedSlot.startTime, selectedSlot.endTime);
    onSlotSelect(selectedTicket!, selectedSlot.id, selectedSlot.day, timeRange, additionalChildren + 1);
    onClose();
  };

  const incrementChildren = () => {
    if (additionalChildren < 3) { // Maximum 3 additional children (4 total)
      setAdditionalChildren(prev => prev + 1);
    } else {
      toast.error('Maximum 4 children per slot');
    }
  };

  const decrementChildren = () => {
    if (additionalChildren > 0) {
      setAdditionalChildren(prev => prev - 1);
    }
  };

  const filteredSlots = slots.filter(slot => slot.day === selectedDay);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            ref={modalRef}
            className="bg-[var(--background)] rounded-xl p-6 w-full max-w-lg max-h-[90vh] overflow-hidden flex flex-col"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-white">Select a time slot that you would like to meet Santa Clause</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="flex-grow overflow-y-auto custom-scrollbar">
              <div className="mb-4">
                <div className="flex space-x-2 mb-4 w-full">
                  <button
                    onClick={() => setSelectedDay('Saturday')}
                    className={`w-full px-4 py-2 rounded-lg ${selectedDay === 'Saturday'
                      ? 'bg-[var(--darkGold)] text-white'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                      }`}
                  >
                    Saturday
                  </button>
                  <button
                    onClick={() => setSelectedDay('Sunday')}
                    className={`w-full px-4 py-2 rounded-lg ${selectedDay === 'Sunday'
                      ? 'bg-[var(--darkGold)] text-white'
                      : 'bg-gray-700 text-white hover:bg-gray-600'
                      }`}
                  >
                    Sunday
                  </button>
                </div>

                <p className="text-white mb-2">Available time slots for <span className="text-red-500">{selectedDay}</span>:</p>

                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--darkGold)]"></div>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {filteredSlots.length > 0 ? (
                      filteredSlots.map(slot => (
                        <button
                          key={slot.id}
                          onClick={() => handleSlotSelect(slot)}
                          disabled={!slot.isAvailable}
                          className={`p-3 rounded-lg text-sm transition-colors ${selectedSlot?.id === slot.id
                            ? 'bg-[var(--darkGold)] text-white'
                            : slot.isAvailable
                              ? 'bg-gray-700 text-white hover:bg-gray-600'
                              : 'bg-gray-800 text-gray-500 cursor-not-allowed'
                            }`}
                        >
                          <div className="font-medium">
                            {formatTimeSlot(slot.startTime, slot.endTime)}
                          </div>
                          <div className={`text-xs ${slot.isAvailable ? 'text-green-400' : 'text-red-400'}`}>
                            {slot.isAvailable
                              ? `${slot.available} available`
                              : 'Fully booked'}
                          </div>
                        </button>
                      ))
                    ) : (
                      <div className="col-span-full text-center py-4 text-gray-400">
                        No slots available for this day
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4">
              {selectedSlot && (
                <div className="mb-4 p-4 bg-gray-800 rounded-lg">
                  <h4 className="text-white font-medium mb-2">Number of Children</h4>
                  <p className="text-gray-400 text-sm mb-3">First child is included in the price. Additional children: €6 each (max 4 total).</p>
                  <div className="flex items-center justify-between">
                    <div className="text-white">
                      <span className="text-sm">Total children:</span>
                      <span className="ml-2 font-bold">{additionalChildren + 1}</span>
                      {additionalChildren > 0 && (
                        <span className="ml-2 text-xs text-[var(--darkGold)]">
                          (€{additionalChildren * 6} extra)
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={decrementChildren}
                        disabled={additionalChildren === 0}
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${additionalChildren === 0 ? 'bg-gray-700 text-gray-500 cursor-not-allowed' : 'bg-[var(--darkGold)] text-white hover:bg-[#95784a]'}`}
                      >
                        -
                      </button>
                      <button
                        onClick={incrementChildren}
                        disabled={additionalChildren >= 3}
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${additionalChildren >= 3 ? 'bg-gray-700 text-gray-500 cursor-not-allowed' : 'bg-[var(--darkGold)] text-white hover:bg-[#95784a]'}`}
                      >
                        +
                      </button>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-center">
                <button
                  onClick={handleConfirm}
                  disabled={!selectedSlot}
                  className={`w-full px-4 py-2 rounded-lg ${selectedSlot
                    ? 'bg-[var(--darkGold)] text-white hover:bg-[#95784a]'
                    : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                    }`}
                >
                  Confirm Selection
                </button>
                <button
                  onClick={onClose}
                  className="w-full px-4 py-2 bg-gray-700 text-white rounded-lg ml-2 hover:bg-gray-600"
                >
                  Cancel
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default SantaSlotModal;