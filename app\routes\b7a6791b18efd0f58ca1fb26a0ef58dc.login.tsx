import { useState, useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { useAdminAuth } from '@/app/context/AdminAuthContext';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Lock, Mail } from 'lucide-react';
import { trackEvent } from '@/app/utils/analytics';

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters")
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function AdminLogin() {
  const { login, isAuthenticated, loading: authLoading } = useAdminAuth();
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const navigate = useNavigate();

  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema)
  });

  // Handle redirection after successful authentication
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      navigate('/b7a6791b18efd0f58ca1fb26a0ef58dc');
    }
  }, [isAuthenticated, authLoading, navigate]);

  // Show loading state while checking auth status
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-zinc-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
      </div>
    );
  }

  const onSubmit = async (data: LoginFormValues) => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      setError(null);

      // Track login attempt
      const attemptNumber = loginAttempts + 1;
      setLoginAttempts(attemptNumber);

      trackEvent('login_attempt', {
        method: 'email_password',
        email: data.email,
        attempt_number: attemptNumber,
        timestamp: new Date().toISOString(),
      });

      const result = await login(data.email, data.password);

      if (!result.success) {
        const errorMessage = result.error || 'An unknown error occurred';
        setError(errorMessage);
        // Track login error
        trackEvent('login_error', {
          error: errorMessage,
          email: data.email,
          timestamp: new Date().toISOString(),
        });
        // Track failed login attempt
        trackEvent('login_failed', {
          reason: errorMessage,
          email: data.email,
          attempt_number: attemptNumber,
          timestamp: new Date().toISOString(),
        });
      }
      // Navigation will be handled by the useEffect hook
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Login error:', err);
      // Track login error
      trackEvent('login_error', {
        error: errorMessage,
        email: data.email,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-zinc-900 p-4">
      <div className="w-full max-w-md space-y-8 bg-zinc-800 rounded-lg shadow-lg p-8 border border-amber-600/30">
        <div className="text-center">
          <h2 className="mt-6 text-center text-3xl font-extrabold text-amber-100">
            Admin Sign In
          </h2>
          <p className="mt-2 text-center text-sm text-amber-100/70">
            Enter your credentials to access the admin dashboard
          </p>
        </div>

        {error && (
          <div className="bg-amber-900/30 text-amber-100 p-4 rounded-md text-sm border border-amber-600/50">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-amber-400" />
                </div>
                <input
                  id="email"
                  type="email"
                  {...register('email')}
                  className="block w-full pl-10 pr-3 py-2 border border-amber-600/50 bg-zinc-700 rounded-md shadow-sm placeholder-amber-400/50 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-100"
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-amber-400">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-amber-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  {...register('password')}
                  className="block w-full pl-10 pr-10 py-2 border border-amber-600/50 bg-zinc-700 rounded-md shadow-sm placeholder-amber-400/50 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 text-amber-100"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-amber-400 hover:text-amber-300"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-amber-400">{errors.password.message}</p>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={isSubmitting || authLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-amber-900 bg-amber-400 hover:bg-amber-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>

          <div className="text-center text-sm">
            <p className="text-amber-200/70">
              This is a secure area. Unauthorized access is prohibited.
            </p>
          </div>
      </div>
    </div>
  );
};