import { configureStore } from '@reduxjs/toolkit';
import adminReducer, { initialState as adminInitialState } from './slices/adminSlice';
import auditLogsReducer, { initialState as auditLogsInitialState } from './slices/auditLogsSlice';
import bookingReducer, { initialState as bookingInitialState } from './slices/bookingSlice';
import realtimeReducer, { initialState as realtimeInitialState } from './slices/realtimeSlice';
import userReducer, { initialState as usersInitialState } from './slices/userSlice';

// Import types
import type { AdminState } from './slices/adminSlice';
import type { AuditLogsState } from './slices/auditLogsSlice';
import type { BookingState } from './slices/bookingSlice';
import type { RealtimeState } from './slices/realtimeSlice';
import type { UsersState } from './slices/userSlice';

// Export types
export type { AdminState, AuditLogsState, BookingState, RealtimeState };

// Export initial states for testing and hydration
export const initialState = {
  admin: adminInitialState,
  auditLogs: auditLogsInitialState,
  booking: bookingInitialState,
  realtime: realtimeInitialState,
  users: usersInitialState,
};

// Define the root state type
export interface RootState {
  admin: AdminState;
  auditLogs: AuditLogsState;
  booking: BookingState;
  realtime: RealtimeState;
  users: UsersState;
}

// Create a function to create the store with preloaded state
export const createStore = (preloadedState: Partial<RootState> = {}) => {
  return configureStore({
    reducer: {
      admin: adminReducer,
      auditLogs: auditLogsReducer,
      booking: bookingReducer,
      realtime: realtimeReducer,
      users: userReducer,
    },
    preloadedState: {
      admin: { ...adminInitialState, ...preloadedState?.admin },
      auditLogs: { ...auditLogsInitialState, ...preloadedState?.auditLogs },
      booking: { ...bookingInitialState, ...preloadedState?.booking },
      realtime: { ...realtimeInitialState, ...preloadedState?.realtime },
      users: { ...usersInitialState, ...preloadedState?.users },
    },
    // Add middleware or other store enhancers here
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
        thunk: true,
      }),
  });
};

// Create a default store instance for type inference
export const store = createStore();

// Infer the `AppDispatch` type from the store itself
export type AppDispatch = typeof store.dispatch;

export default createStore;