import React, { useState } from 'react';
import { Alert<PERSON>riangle, X, Trash2 } from 'lucide-react';
import { useSupabase } from '@/app/context/AdminAuthContext';
import { useAppSelector } from '@/app/store/hooks';
import { showLoadingToast, updateToastToSuccess, updateToastToError } from '@/app/utils/toast';
import type { User as UserType } from '@/app/models/user';

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: UserType | null;
  onUserDeleted?: () => void;
}

export const DeleteUserModal = ({ isOpen, onClose, user, onUserDeleted }: DeleteUserModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const supabase = useSupabase();

  // Get the current user from Redux instead of calling Supabase auth
  const currentUser = useAppSelector((state: any) => state.users?.currentUser);

  const handleDelete = async () => {
    if (!supabase || !user) {
      return;
    }

    // Require user to type "DELETE" to confirm
    if (confirmText !== 'DELETE') {
      return;
    }

    const toastId = showLoadingToast('Deleting user...');
    setIsDeleting(true);

    try {
      // Log the admin action in AdminAuditLogs
      // Note: We need the current admin user's ID, not the deleted user's ID
      // For now, we'll use a placeholder or skip audit logging if we can't get current user
      const auditLogId = crypto.randomUUID();

      // Use current user from Redux (already loaded at app startup)
      console.log({ currentUser })

      if (currentUser && currentUser.id) {
        // Delete user from AdminUsers table
        const { error: deleteError } = await supabase
          .from('AdminUsers')
          .delete()
          .eq('id', user.id);

        if (deleteError) {
          throw new Error(deleteError.message);
        }

        const { error: auditError } = await supabase
          .from('AdminAuditLogs')
          .insert({
            id: auditLogId,
            user_id: currentUser.id, // Current admin user performing the action
            action: 'DELETE_ADMIN_USER',
            details: {
              deleted_user_id: user.id,
              deleted_user_email: user.email,
              deleted_user_name: user.name,
              deleted_user_role: user.role,
              deletion_reason: 'Manual deletion by admin'
            },
            severity: 'WARNING',
            created_at: new Date().toISOString()
          });

        // Don't fail the user deletion if audit log fails, just log the error
        if (auditError) {
          console.warn('Failed to create audit log:', auditError);
          updateToastToError(toastId, 'Failed to create audit log');
        }

        updateToastToSuccess(toastId, `User ${user.name} deleted successfully!`);
        setConfirmText('');
        onClose();

        // Trigger user list refresh
        if (onUserDeleted) {
          onUserDeleted();
        }
      } else {
        console.warn('Could not get current user for audit logging');
        updateToastToError(toastId, 'Could not get current user for audit logging');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      updateToastToError(toastId, `Failed to delete user: ${(error as any)?.message || 'Unknown error'}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClose = () => {
    setConfirmText('');
    onClose();
  };

  if (!isOpen || !user) return null;

  const isConfirmValid = confirmText === 'DELETE';

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="inline-block align-bottom bg-white dark:bg-zinc-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-white dark:bg-zinc-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30">
                  <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                    Delete Admin User
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    This action cannot be undone
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Warning Content */}
            <div className="space-y-4">
              {/* User Info */}
              <div className="bg-gray-50 dark:bg-zinc-700/50 rounded-lg p-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center">
                    <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                      {user.name?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {user.name || 'Unknown User'}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {user.email}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      Role: {user.role?.replace('_', ' ') || 'Unknown'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Warning Text */}
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex">
                  <AlertTriangle className="h-5 w-5 text-red-400 mt-0.5" />
                  <div className="ml-3">
                    <h4 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Warning: This action is permanent
                    </h4>
                    <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                      <ul className="list-disc list-inside space-y-1">
                        <li>The user will be permanently deleted from the system</li>
                        <li>They will lose access to all admin features immediately</li>
                        <li>This action cannot be undone</li>
                        <li>All audit logs will be preserved for compliance</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Confirmation Input */}
              <div>
                <label htmlFor="confirmText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  To confirm deletion, type <span className="font-mono bg-gray-100 dark:bg-gray-700 px-1 rounded">DELETE</span> in the box below:
                </label>
                <input
                  type="text"
                  id="confirmText"
                  value={confirmText}
                  onChange={(e) => setConfirmText(e.target.value)}
                  className="block w-full h-10 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 bg-white dark:bg-zinc-700 text-gray-900 dark:text-white"
                  placeholder="Type DELETE to confirm"
                  disabled={isDeleting}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-gray-50 dark:bg-zinc-700/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse -mx-4 -mb-4 mt-6">
              <button
                type="button"
                onClick={handleDelete}
                disabled={!isConfirmValid || isDeleting}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? 'Deleting...' : 'Delete User'}
              </button>
              <button
                type="button"
                onClick={handleClose}
                disabled={isDeleting}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-zinc-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
