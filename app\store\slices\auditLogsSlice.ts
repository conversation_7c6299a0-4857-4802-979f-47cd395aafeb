import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { getSupabaseClient } from '@/app/utils/supabaseClient';

export interface AuditLog {
  id: string;
  user_id: string | null;
  action: string;
  details: any;
  created_at: string;
  severity?: 'info' | 'warning' | 'error';
  resolved?: boolean;
}

export interface AuditLogsState {
  logs: AuditLog[];
  loading: boolean;
  error: string | null;
  lastFetched: number | null;
}

export const initialState: AuditLogsState = {
  logs: [],
  loading: false,
  error: null,
  lastFetched: null,
};

// Thunk to fetch recent audit logs
export const fetchRecentAuditLogs = createAsyncThunk<
  AuditLog[],
  { limit?: number; severity?: string[] },
  { rejectValue: string }
>('auditLogs/fetchRecent', async ({ limit = 10, severity }, { rejectWithValue }) => {
  try {
    const supabase = getSupabaseClient();
    if (!supabase) {
      return rejectWithValue('Supabase client not available (SSR or missing ENV)');
    }

    let query = supabase
      .from('AdminAuditLogs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (severity && severity.length > 0) {
      query = query.in('severity', severity);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  } catch (err) {
    console.error('Error fetching audit logs:', err);
    return rejectWithValue(err instanceof Error ? err.message : 'Failed to fetch audit logs');
  }
});

const auditLogsSlice = createSlice({
  name: 'auditLogs',
  initialState,
  reducers: {
    clearAuditLogs: (state) => {
      state.logs = [];
      state.loading = false;
      state.error = null;
    },
    markAsResolved: (state, action: PayloadAction<string>) => {
      const log = state.logs.find(log => log.id === action.payload);
      if (log) {
        log.resolved = true;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRecentAuditLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRecentAuditLogs.fulfilled, (state, action) => {
        state.loading = false;
        state.logs = action.payload;
        state.lastFetched = Date.now();
      })
      .addCase(fetchRecentAuditLogs.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch audit logs';
      });
  },
});

export const { clearAuditLogs, markAsResolved } = auditLogsSlice.actions;
export default auditLogsSlice.reducer;