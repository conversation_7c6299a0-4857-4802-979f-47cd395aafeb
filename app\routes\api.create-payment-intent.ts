import { json, type ActionFunctionArgs } from "@remix-run/node";
import Stripe from 'stripe';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-05-28.basil',
});

const supabaseUrl = process.env.SUPABASE_URL! as string;
const supabaseKey = process.env.SUPABASE_KEY! as string;
const supabase = createClient(supabaseUrl, supabaseKey);

interface TicketAvailability {
  [key: string]: number;
  "adult-1day-saturday": number;
  "adult-1day-sunday": number;
  "child-1day-saturday": number;
  "child-1day-sunday": number;
  "adult-2day": number;
  "child-2day": number;
  "family-1day-saturday": number;
  "family-1day-sunday": number;
  "santa-option-saturday": number;
  "santa-option-sunday": number;
}

export async function action({ request }: ActionFunctionArgs) {
  console.log('API: create-payment-intent called');
  try {
    const { amount, orderDetails } = await request.json();
    console.log('API: create-payment-intent - Received orderDetails:', orderDetails);

    // Define hardcoded availability values
    const hardcodedAvailability: TicketAvailability = {
      "adult-1day-saturday": 1900,
      "adult-1day-sunday": 1900,
      "child-1day-saturday": 1300,
      "child-1day-sunday": 1300,
      "adult-2day": 3000,
      "child-2day": 2500,
      "family-1day-saturday": 350,
      "family-1day-sunday": 350,
      "santa-option-saturday": 162,
      "santa-option-sunday": 162
    };

    // Check ticket availability before proceeding
    for (const item of orderDetails.tickets) {
      // Skip availability check for charity donations
      if (item.ticketType.id === 'charity-donation') {
        continue;
      }

      // Special handling for Santa option
      if (item.ticketType.id.includes('santa-option') && item.santaSlot) {
        // Check if the Santa slot is booked
        const { data: slot, error } = await supabase
          .from('SantaTimeSlots')
          .select('booked, capacity')
          .eq('id', item.santaSlot.id);

        if (error) throw error;

        if (!slot?.length || slot[0].booked >= slot[0].capacity) {
          return json(
            {
              error: 'The selected Santa slot is already booked. Please choose another slot.'
            },
            { status: 400 }
          );
        }
        continue; // Skip regular availability check for Santa slots
      }

      // Get sales to calculate current availability
      const { data: sales, error } = await supabase
        .from('TicketSales')
        .select('quantity')
        .eq('ticket_id', item.ticketType.id);

      if (error) throw error;

      const soldQuantity = (sales?.reduce((sum: number, sale: { quantity: number }) => sum + sale.quantity, 0) || 0);

      // Determine which availability value to use
      const ticketId = item.ticketType.id;
      const totalAvailable = hardcodedAvailability[ticketId] || 0;
      const availableQuantity = totalAvailable - soldQuantity;

      if (item.quantity > availableQuantity) {
        return json(
          {
            error: `Sorry, only ${availableQuantity} tickets available for ${item.ticketType.name}`
          },
          { status: 400 }
        );
      }
    }

    // Check if there are any Santa bookings in the order
    for (const item of orderDetails.tickets) {
      if (item.ticketType.id.includes('santa-option') && item.santaSlot) {
        // Check if the Santa slot is booked
        const { data: slot, error } = await supabase
          .from('SantaTimeSlots')
          .select('booked, capacity')
          .eq('id', item.santaSlot.id);

        if (error) throw error;

        if (!slot?.length || slot[0].booked >= slot[0].capacity) {
          return json(
            {
              error: 'The selected Santa slot is already booked. Please choose another slot.'
            },
            { status: 400 }
          );
        }
      }
    }

    // Create a payment intent
    const metadata: Record<string, any> = {
      total: amount.toString(),
      orderId: uuidv4(),
      hasDonation: orderDetails.tickets.some((item: any) => item.ticketType.id === 'charity-donation') ? 'true' : 'false',
      donationAmount: '0',
      charityMultiplier: '',
      hasSantaVisit: orderDetails.tickets.some((item: any) => item.santaSlot) ? 'true' : 'false'
    };

    // Calculate donation details if charity donation exists
    const charityDonationItem = orderDetails.tickets.find((item: any) => item.ticketType.id === 'charity-donation');
    if (charityDonationItem) {
        const basePrice = charityDonationItem.ticketType.originalPrice || charityDonationItem.ticketType.price;
        const multiplier = orderDetails.charityMultiplier || 1; // Use orderDetails.charityMultiplier if it's a number
        const appliedAmount = basePrice * multiplier;

        metadata.donationAmount = appliedAmount.toString();
        metadata.charityMultiplier = multiplier.toString(); // Store as string
    }

    // Add individual ticket information and new metadata keys
    let ticketIndex = 1;
    orderDetails.tickets.forEach((item: any) => {
      if (item.ticketType.id.includes('santa-option') && item.santaSlot) {
        // Format Santa option tickets and add specific Santa keys
        metadata[`meet_santa`] = `${item.santaSlot.day} - ${item.santaSlot.timeRange} (${item.santaSlot.numChildren} children)`;
        metadata[`santa_date`] = item.santaSlot.day;
        metadata[`santa_time`] = item.santaSlot.timeRange;
        metadata[`santa_amount_paid`] = (item.ticketType.price * item.quantity).toString();
      } else if (item.ticketType.id === 'charity-donation') {
        // Charity donation details are handled above
      } else {
        // Format regular tickets and add specific date/amount keys
        const ticketKey = `ticket_${ticketIndex}`;
        metadata[ticketKey] = `${item.ticketType.name}: ${item.selectedDay || item.ticketType.selectedDay} - ${item.quantity}x`;
        // Add date and amount for this regular ticket item
        metadata[`${ticketKey}_date`] = item.selectedDay || item.ticketType.selectedDay;
        metadata[`${ticketKey}_amount`] = (item.ticketType.price * item.quantity).toString();
        ticketIndex++;
      }
    });

    console.log('API: create-payment-intent - Final metadata sent to Stripe:', metadata);

    // First create or retrieve the customer
    const customer = await stripe.customers.create({
      email: orderDetails.email,
      name: orderDetails.name,
      phone: orderDetails.phoneNumber,
      metadata: {
        orderId: metadata.orderId
      }
    });

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'eur',
      customer: customer.id,
      metadata
    });

    return json({
      clientSecret: paymentIntent.client_secret,
      orderId: metadata.orderId,
      paymentIntentId: paymentIntent.id,
      customerId: customer.id,
      metadata: {
        ...metadata,
        // Convert string 'true'/'false' to boolean
        hasDonation: metadata.hasDonation === 'true',
        // Convert string to number
        donationAmount: parseFloat(metadata.donationAmount),
        charityMultiplier: parseFloat(metadata.charityMultiplier),
        total: parseFloat(metadata.total)
      }
    });
  } catch (error) {
    console.error('Error:', error);
    return json(
      { error: 'Failed to create payment intent' },
      { status: 500 }
    );
  }
};