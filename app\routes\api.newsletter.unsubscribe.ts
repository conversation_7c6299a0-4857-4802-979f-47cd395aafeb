import { json, type ActionFunctionArgs } from "@remix-run/node";
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function action({ request }: ActionFunctionArgs) {
  try {
    const { email } = await request.json();

    if (!email) {
      return json({ error: 'Email is required' }, { status: 400 });
    }

    // Update subscriber status in Supabase
    const { error: updateError } = await supabase
      .from('NewsletterSubscribers')
      .update({ is_subscribed: false })
      .eq('email', email.toLowerCase());

    if (updateError) {
      console.error('Error updating subscriber status:', updateError);
      return json({ error: 'Failed to unsubscribe' }, { status: 500 });
    }

    return json({ success: true, message: 'Successfully unsubscribed' });
  } catch (error) {
    console.error('Error processing unsubscribe request:', error);
    return json(
      { error: 'Failed to process unsubscribe request' },
      { status: 500 }
    );
  }
}