// app/components/admin/AdminRealtimeProvider.tsx
import React, { useEffect } from 'react';
import { useAppDispatch } from '@/app/store/hooks';
import { startRealtimeMonitoring, stopRealtimeMonitoring } from '@/app/store/slices/realtimeSlice';
import { useRealtimeEvents } from '@/app/hooks/useRealtimeEvents';

export function AdminRealtimeProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(startRealtimeMonitoring());
    return () => {
      dispatch(stopRealtimeMonitoring());
    };
  }, [dispatch]);

  // Hook in your one-time event listener
  useRealtimeEvents();

  return <>{children}</>;
}