import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';
import { HeaderItem } from "@/types/menu";
import { useAnalytics } from '@/app/hooks/useAnalytics';

interface HeaderLinkProps {
  item: HeaderItem;
  onClick: () => void;
  isButton?: boolean;
  isBookTickets?: boolean;
  className?: string;
}

const HeaderLink = ({
  item,
  onClick,
  isButton = false,
  isBookTickets = false,
  className = "",
}: HeaderLinkProps) => {
  const [isButtonHighlighted, setIsButtonHighlighted] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Analytics tracking
  const { trackNavigation } = useAnalytics();

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (isBookTickets) {
      if (audioRef.current) {
        audioRef.current.currentTime = 0;
        audioRef.current.play().catch(console.error);
      }
      // Track book tickets button click
      trackNavigation.menuClick(item.label, '/book-tickets');
      onClick();
      navigate('/book-tickets');
      return;
    }

    // If it's a hash-link (e.g. "/#about" or "#about")...
    if (item.href.includes('#')) {
      const [path, hash] = item.href.split('#');
      const elementId = hash ? `#${hash}` : null;

      if (elementId) {
        const element = document.querySelector(elementId) as any;

        if (element) {
          // If on the same page, prevent default and scroll
          if (location.pathname === path || (location.pathname === '/' && path === '')) {
            e.preventDefault(); // Prevent default link behavior
            element.scrollIntoView({ behavior: "smooth" });
            // Track section navigation
            trackNavigation.sectionViewed(hash || '', item.label);
            onClick();
            return;
          }
        }
      }
      // If element not found or on a different page, navigate normally
      trackNavigation.menuClick(item.label, item.href);
      onClick();
      navigate(item.href);
      return;
    }

    // For regular links (no hash)
    trackNavigation.menuClick(item.label, item.href);
    onClick();
    navigate(item.href);
  };

  useEffect(() => {
    if (item.button) {
      const interval = setInterval(() => {
        setIsButtonHighlighted(true);

        setTimeout(() => {
          setIsButtonHighlighted(false);
        }, 1000);
      }, 5000);
      return () => clearInterval(interval);
    }
  }, [item]);

  return (
    <>
      {isBookTickets && (
        <audio ref={audioRef} preload="auto">
          <source src="/media/santa.mp3" type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
      )}
      <button
        onClick={handleClick}
        className={`
          ${isButton ?
            `button-glare overflow-hidden bg-[var(--darkGold)] text-white dark:bg-[var(--darkGold)] dark:text-white hover:bg-[#A67C00] px-2 py-2 rounded-md ${isButtonHighlighted ? 'hover' : ''}`
            :
            'text-white hover:text-[var(--darkGold)] transition-colors duration-300'
          }
          ${className}
        `}
      >
        {item.label}
      </button>
    </>
  );
};
export default HeaderLink;