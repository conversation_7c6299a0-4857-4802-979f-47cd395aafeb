export function generateMetaDescriptors() {
  const baseUrl = 'https://theconventionbeforechristmas.ie';

  return [
    { title: 'Convention Before Christmas 2025 | Dublin Christmas Event' },
    { name: 'description', content: 'Join Dublin’s 2025 Convention Before Christmas for fandom, crafts, comics, live shows, and a festive market at The National Show Center, Swords Co. Dublin!' },
    { name: 'keywords', content: 'Christmas convention Dublin, Dublin Christmas event 2025, Comic Con Dublin, The National Show Center, Swords Co. Dublin Christmas market, family events Ireland' },
    { name: 'author', content: 'Hidden Dublin Ltd' },
    { name: 'creator', content: 'Hidden Dublin Ltd' },
    { name: 'publisher', content: 'Hidden Dublin Ltd' },

    // OpenGraph
    { property: 'og:title', content: 'Convention Before Christmas 2025' },
    { property: 'og:description', content: 'Join <PERSON>’s magical 2025 Christmas convention at The National Show Center, Swords Co. Dublin, with crafts, comics, live shows, fandom, and a festive market!' },
    { property: 'og:url', content: baseUrl },
    { property: 'og:site_name', content: 'Convention Before Christmas' },
    { property: 'og:image', content: `${baseUrl}/images/convention.png` },
    { property: 'og:image:width', content: '1200' },
    { property: 'og:image:height', content: '630' },
    { property: 'og:image:alt', content: 'Convention Before Christmas 2025 Event' },
    { property: 'og:locale', content: 'en_IE' },
    { property: 'og:type', content: 'website' },

    // Twitter
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Convention Before Christmas 2025' },
    { name: 'twitter:description', content: 'Join Dublin’s magical 2025 Christmas convention at The National Show Center, Swords Co. Dublin with crafts, comics, live shows, and more!' },
    { name: 'twitter:image', content: `${baseUrl}/images/convention.png` },
    { name: 'twitter:creator', content: '@ConBeforeXmas' },

    // Robots
    { name: 'robots', content: 'index, follow' },
    { name: 'googlebot', content: 'index, follow, max-image-preview:large' },

    // Icons
    { rel: 'icon', href: `${baseUrl}/images/grinch-hand.png` },
    { rel: 'shortcut icon', href: `${baseUrl}/images/grinch-hand.png` },
    { rel: 'apple-touch-icon', href: `${baseUrl}/images/grinch-hand.png` },
    { rel: 'apple-touch-icon-precomposed', href: `${baseUrl}/images/grinch-hand.png` },

    // Manifest
    { rel: 'manifest', href: `${baseUrl}/site.webmanifest` },

    // Viewport
    { name: 'viewport', content: 'width=device-width, initial-scale=1, maximum-scale=1' },

    // Verification (replace with your actual code from Google Search Console)
    { name: 'google-site-verification', content: 'H3AoSBTZYBokI-KdlciuNIohjgsvuGuKB6uGfcxOSTce' },

    // Theme Color
    { name: 'theme-color', content: '#ffffff' },

    // Canonical
    { rel: 'canonical', href: baseUrl },

    // Category
    { name: 'category', content: 'entertainment' },
  ];
};