import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CartItem, OrderDetails, TicketType } from '@/types/ticket';

interface BookingState {
  cart: CartItem[];
  orderDetails: OrderDetails;
  clientSecret: string | null;
}

const initialState: BookingState = {
  cart: [],
  orderDetails: {
    email: '',
    name: '',
    phoneNumber: '',
    tickets: [],
    total: 0,
    orderId: '',
    paymentIntentId: ''
  },
  clientSecret: null,
};

// Helper function to calculate total
const calculateTotal = (cart: CartItem[]): number => {
  return cart.reduce((total, item) => {
    const itemTotal = item.ticketType.price * item.quantity;
    return total + itemTotal;
  }, 0);
};

const bookingSlice = createSlice({
  name: 'booking',
  initialState,
  reducers: {
    setCart: (state, action: PayloadAction<CartItem[]>) => {
      state.cart = action.payload;
      state.orderDetails.total = calculateTotal(action.payload);
      state.orderDetails.tickets = action.payload;
    },
    setOrderDetails: (state, action: PayloadAction<OrderDetails>) => {
      state.orderDetails = action.payload;
    },
    addToCart: (state, action: PayloadAction<{ ticketType: TicketType; quantity: number; selectedDay?: 'Saturday' | 'Sunday'; santaSlot?: { id: number; day: string; timeRange: string; numChildren: number; }; }>) => {
      const { ticketType, quantity, selectedDay, santaSlot } = action.payload;
      const existingItem = state.cart.find(item => item.ticketType.id === ticketType.id);

      if (existingItem) {
        state.cart = state.cart.map(item =>
          item.ticketType.id === ticketType.id
            ? { ...item, quantity: item.quantity + quantity, selectedDay: selectedDay || item.selectedDay, santaSlot: santaSlot || item.santaSlot }
            : item
        );
      } else {
        const newItem = { ticketType, quantity, selectedDay, santaSlot };
        state.cart.push(newItem);
      }
      state.orderDetails.total = calculateTotal(state.cart);
      state.orderDetails.tickets = state.cart;
    },
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.cart = state.cart.filter(item => item.ticketType.id !== action.payload);
      state.orderDetails.total = calculateTotal(state.cart);
      state.orderDetails.tickets = state.cart;
    },
    updateCartItemQuantity: (state, action: PayloadAction<{ ticketId: string; quantity: number; selectedDay?: 'Saturday' | 'Sunday' }>) => {
      const { ticketId, quantity, selectedDay } = action.payload;
      state.cart = state.cart.map(item =>
        item.ticketType.id === ticketId
          ? { ...item, quantity, selectedDay: selectedDay || item.selectedDay, santaSlot: item.santaSlot }
          : item
      ).filter(item => item.quantity > 0);
      state.orderDetails.total = calculateTotal(state.cart);
      state.orderDetails.tickets = state.cart;
    },
    clearCart: (state) => {
      state.cart = [];
      state.orderDetails.total = 0;
    },
    setClientSecret: (state, action: PayloadAction<string | null>) => {
      state.clientSecret = action.payload;
    },
  }
});

export const {
  setCart,
  setOrderDetails,
  addToCart,
  removeFromCart,
  updateCartItemQuantity,
  clearCart,
  setClientSecret,
} = bookingSlice.actions;

export default bookingSlice.reducer;