// Real data is sourced from Redux userBehavior state
import { useNavigate } from "@remix-run/react";
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchUserBehavior, setUserBehaviorTimeRange } from "@/app/store/slices/adminSlice";
import { useAnalyticsRefresh } from '@/app/hooks/useRealtimeRefresh';
import {
  Users,
  Activity,
  Clock,
  BarChart2,
  TrendingUp,
  RefreshC<PERSON>,
  ArrowLeft,
  Eye,
  MousePointerClick,
  BarChart3,
  Smartphone
} from "lucide-react";
import { useState, useEffect } from "react";
import { PageViewsChart } from "@/app/components/admin/charts/PageViewsChart";
import { UserActivityChart } from "@/app/components/admin/charts/UserActivityChart";
import { StatCard } from "@/app/components/admin/charts/StatCard";
import { Button } from "@/app/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card";
import { AdminLayout } from "@/app/components/admin/layout/AdminLayout";
import { ProtectedRoute } from "@/app/components/admin/ProtectedRoute";
import { DeviceDistributionChart } from "@/app/components/admin/charts/DeviceDistributionChart";
import { EventsByTypeChart } from "@/app/components/admin/charts/EventsByTypeChart";

export default function UserBehaviorPage() {
  const dispatch = useAppDispatch();
  const { data: behaviorData, loading: behaviorLoading, error: behaviorError } = useAppSelector(state => state.admin.userBehavior);
  const pageViews = behaviorData?.pageViews || [];
  // Fix: always provide all keys for userActivity
  const userActivity = {
    labels: behaviorData?.userActivity?.labels ?? [],
    activeUsers: behaviorData?.userActivity?.activeUsers ?? [],
    newUsers: behaviorData?.userActivity?.newUsers ?? [],
    sessions: behaviorData?.userActivity?.sessions ?? [],
    returningUsers: behaviorData?.userActivity?.returningUsers ?? [],
  };
  const navigate = useNavigate();
  const [dateRange, setDateRange] = useState('7days');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const handleRefresh = async () => {
    setIsLoading(true);
    await dispatch(fetchUserBehavior({ timeRange: dateRange as any }));
    setIsLoading(false);
  };

  const handleDateRangeChange = (range: string) => {
    setDateRange(range);
    dispatch(setUserBehaviorTimeRange(range as any));
    dispatch(fetchUserBehavior({ timeRange: range as any }));
  };

  const navigateToOverview = () => navigate('/b7a6791b18efd0f58ca1fb26a0ef58dc/users');

  // Initial fetch
  useEffect(() => {
    if (!behaviorData && !behaviorLoading) {
      dispatch(fetchUserBehavior({ timeRange: dateRange as any }));
    }
  }, [dispatch, behaviorData, behaviorLoading, dateRange]);

  // Set up real-time refresh for analytics events
  useAnalyticsRefresh(() => {
    console.log('[UserBehavior] Real-time refresh triggered!');
    dispatch(fetchUserBehavior({ timeRange: dateRange as any }));
  });

  // Debug: Log when component mounts
  useEffect(() => {
    console.log('[UserBehavior] Component mounted, setting up real-time refresh');
  }, []);

  return (
    <ProtectedRoute requiredRole={['super_admin', 'support_admin']}>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateToOverview}
                  className="text-amber-100 hover:bg-amber-900/30 h-8 w-8 p-0"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-2xl font-bold text-white">User Behavior Analytics</h1>
              </div>
              <p className="mt-1 text-sm text-amber-100/70 ml-10">
                Analyze user engagement and interaction patterns
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
                className="border-amber-600/30 text-amber-100 bg-zinc-800 hover:bg-amber-900/30"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <div className="relative">
                <select
                  value={dateRange}
                  onChange={(e) => handleDateRangeChange(e.target.value)}
                  className="appearance-none bg-zinc-800 border border-amber-600/30 rounded-md pl-3 pr-8 py-2 text-sm text-amber-100 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                >
                  <option value="7days">Last 7 days</option>
                  <option value="30days">Last 30 days</option>
                  <option value="90days">Last 90 days</option>
                  <option value="year">This year</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-amber-100">
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Behavior Tabs */}
          <div className="border-b border-amber-600/30 mb-6">
            <nav className="-mb-px flex space-x-8">
              {['overview', 'sessions', 'events', 'devices'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`${activeTab === tab
                    ? 'border-amber-500 text-amber-100'
                    : 'border-transparent text-amber-100/70 hover:text-amber-100 hover:border-amber-300'
                    } whitespace-nowrap border-b-2 py-4 px-1 text-sm font-medium transition-colors`}
                >
                  {tab === 'overview' && 'Overview'}
                  {tab === 'sessions' && 'Sessions'}
                  {tab === 'events' && 'Events'}
                  {tab === 'devices' && 'Devices'}
                </button>
              ))}
            </nav>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Page Views Stat */}
            <StatCard
              title="Page Views"
              value={behaviorLoading ? '...' : behaviorError ? 'Error' : (behaviorData?.pageViews?.reduce((sum, day) => sum + day.views, 0).toLocaleString() || '0')}
              change=""
              icon={<BarChart2 className="h-5 w-5 text-amber-400" />}
              color="amber"
              description="Total views across all pages"
              tooltip="Total views across all pages"
              loading={behaviorLoading}
            />
            {/* Avg. Session Stat */}
            <StatCard
              title="Avg. Session"
              value={behaviorLoading ? '...' : behaviorError ? 'Error' : (
                behaviorData?.sessionMetrics
                  ? `${Math.floor(behaviorData.sessionMetrics.avgSessionDuration / 60)}m ${behaviorData.sessionMetrics.avgSessionDuration % 60}s`
                  : '0s')}
              change=""
              icon={<Clock className="h-5 w-5 text-blue-400" />}
              color="blue"
              description="Average session duration"
              tooltip="Average session duration"
              loading={behaviorLoading}
            />
            {/* Bounce Rate Stat */}
            <StatCard
              title="Bounce Rate"
              value={behaviorLoading ? '...' : behaviorError ? 'Error' : (
                behaviorData?.sessionMetrics ? `${behaviorData.sessionMetrics.bounceRate}%` : '0%')}
              change=""
              icon={<TrendingUp className="h-5 w-5 text-green-400" />}
              color="green"
              description="Percentage of single-page sessions"
              tooltip="Percentage of single-page sessions"
              loading={behaviorLoading}
            />
            {/* Active Users Stat */}
            <StatCard
              title="Active Users"
              value={behaviorLoading ? '...' : behaviorError ? 'Error' : (
                behaviorData?.userActivity?.activeUsers
                  ? behaviorData.userActivity.activeUsers[behaviorData.userActivity.activeUsers.length - 1]?.toLocaleString() || '0'
                  : '0')}
              change=""
              icon={<Users className="h-5 w-5 text-purple-400" />}
              color="purple"
              description="Users active in the last 30 minutes"
              tooltip="Users active in the last 30 minutes"
              loading={behaviorLoading}
            />
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {activeTab === 'overview' && (
              <>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card className="bg-zinc-800/50 border border-amber-600/30">
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold text-white">
                        <BarChart3 className="inline mr-2 h-5 w-5 text-amber-400" />
                        Page Views
                      </CardTitle>
                      <p className="text-sm text-amber-100/70">Page views over time</p>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <PageViewsChart data={pageViews} />
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="bg-zinc-800/50 border border-amber-600/30">
                    <CardHeader>
                      <CardTitle className="text-lg font-semibold text-white">
                        <Activity className="inline mr-2 h-5 w-5 text-blue-400" />
                        User Activity
                      </CardTitle>
                      <p className="text-sm text-amber-100/70">Active vs. new users</p>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <UserActivityChart data={userActivity} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-zinc-800/50 border border-amber-600/30">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-white">
                      <Eye className="inline mr-2 h-5 w-5 text-purple-400" />
                      Top Pages
                    </CardTitle>
                    <p className="text-sm text-amber-100/70">Most visited pages on your site</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {behaviorLoading ? (
                        <div className="text-amber-100/50">Loading...</div>
                      ) : behaviorError ? (
                        <div className="text-red-400">{behaviorError}</div>
                      ) : behaviorData?.topPages?.length ? (
                        <ul>
                          {behaviorData.topPages.map((page, idx) => (
                            <li key={page.path} className="flex justify-between text-sm">
                              <span className="text-amber-100/90">{page.title || page.path}</span>
                              <span className="text-amber-400">{page.views.toLocaleString()} views</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <div className="text-amber-100/50">No top pages data available</div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            {activeTab === 'sessions' && (
              <Card className="bg-zinc-800/50 border border-amber-600/30">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-white">
                    <Clock className="inline mr-2 h-5 w-5 text-blue-400" />
                    Session Analytics
                  </CardTitle>
                  <p className="text-sm text-amber-100/70">Sessions over time</p>
                </CardHeader>
                <CardContent>
                  {behaviorLoading ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-amber-100/50">Loading...</span>
                    </div>
                  ) : behaviorError ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-red-400">{behaviorError}</span>
                  </div>
                  ) : (
                    <UserActivityChart
                      data={{
                        labels: behaviorData?.userActivity?.labels ?? [],
                        sessions: behaviorData?.userActivity?.sessions ?? [],
                        activeUsers: [],
                        newUsers: [],
                        returningUsers: [],
                      }}
                      height={350}
                      activeColor="#ccc"
                      newColor="#ccc"
                      returningColor="#ccc"
                      sessionsColor="#10b981"
                    />
                  )}
                </CardContent>
              </Card>
            )}

            {activeTab === 'events' && (
              <Card className="bg-zinc-800/50 border border-amber-600/30">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-white">
                    <MousePointerClick className="inline mr-2 h-5 w-5 text-green-400" />
                    Event Tracking
                  </CardTitle>
                  <p className="text-sm text-amber-100/70">All tracked event types over time</p>
                </CardHeader>
                <CardContent>
                  {behaviorLoading ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-amber-100/50">Loading...</span>
                    </div>
                  ) : behaviorError ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-red-400">{behaviorError}</span>
                    </div>
                  ) : behaviorData?.eventsByType?.length ? (
                    <div className="w-full">
                      <EventsByTypeChart data={behaviorData.eventsByType} height={350} />
                  </div>
                  ) : (
                    <div className="text-amber-100/50">No event data available</div>
                  )}
                </CardContent>
              </Card>
            )}

            {activeTab === 'devices' && (
              <Card className="bg-zinc-800/50 border border-amber-600/30">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-white">
                    <Smartphone className="inline mr-2 h-5 w-5 text-purple-400" />
                    Device & Browser Analytics
                  </CardTitle>
                  <p className="text-sm text-amber-100/70">Device, browser, OS, and screen size distribution</p>
                </CardHeader>
                <CardContent>
                  {behaviorLoading ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-amber-100/50">Loading...</span>
                    </div>
                  ) : behaviorError ? (
                    <div className="flex items-center justify-center h-96">
                      <span className="text-red-400">{behaviorError}</span>
                    </div>
                  ) : (
                    <div className="w-full">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 mb-6">
                        <div className="flex items-center justify-center h-full border border-solid border-amber-600/30 rounded-lg p-2 px-4image.png">
                          <div className="font-semibold text-amber-100 mb-2">Device Type</div>
                          <DeviceDistributionChart
                            data={behaviorData?.deviceTypes ?? []}
                            height={250}
                          />
                        </div>
                        <div className="flex items-center justify-center h-full border border-solid border-amber-600/30 rounded-lg p-2 px-4image.png">
                          <div className="font-semibold text-amber-100 mb-2">Browser</div>
                          <DeviceDistributionChart
                            data={behaviorData?.deviceAnalytics?.browsers ?? []}
                            height={250}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 mb-6 ">
                        <div className="flex items-center justify-center h-full border border-solid border-amber-600/30 rounded-lg p-2 px-4image.png">
                          <div className="font-semibold text-amber-100 mb-2">Operating System</div>
                          <DeviceDistributionChart
                            data={behaviorData?.deviceAnalytics?.operatingSystems ?? []}
                            height={250}
                          />
                        </div>
                        <div className="flex items-center justify-center h-full border border-solid border-amber-600/30 rounded-lg p-2 px-4image.png">
                          <div className="font-semibold text-amber-100 mb-2">Screen Size</div>
                          <DeviceDistributionChart
                            data={behaviorData?.deviceAnalytics?.screenSizes ?? []}
                            height={250}
                          />
                        </div>
                    </div>
                  </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </AdminLayout >
    </ProtectedRoute >
  );
};