import { Server as NetServer } from 'http';
import { NextApiResponse } from 'next';
import { Server as ServerIO } from 'socket.io';

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: ServerIO;
    };
  };
};

export interface ServerToClientEvents {
  tableBooked: (data: { tableNumber: number }) => void;
  tableUnbooked: (data: { tableNumber: number }) => void;
}

export interface ClientToServerEvents {
  bookTable: (data: { tableNumber: number }) => void;
  unbookTable: (data: { tableNumber: number }) => void;
}