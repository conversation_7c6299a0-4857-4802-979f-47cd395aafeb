import type { SupabaseClient } from '@supabase/supabase-js';
import { userService } from "@/app/services/userService";

export interface AppSettings {
  siteName: string;
  siteDescription: string;
  adminEmail: string;
  itemsPerPage: number;
  enableRegistration: boolean;
  maintenanceMode: boolean;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

export interface SettingRecord {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object';
  created_at: string;
  updated_at: string;
}

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  siteName: 'The Convention Before Christmas',
  siteDescription: 'Ireland\'s Premier Christmas Convention',
  adminEmail: '<EMAIL>',
  itemsPerPage: 25,
  enableRegistration: true,
  maintenanceMode: false,
  timezone: 'Europe/Dublin',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
};

// Simulate API delay for consistent UX
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const settingsService = {
  async getSettings(supabase: SupabaseClient): Promise<AppSettings> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // First, check if AppSettings table exists, if not, we'll use AdminAuditLogs to store settings
      // This is a workaround since there's no dedicated settings table
      const { data: settingsData, error } = await supabase
        .from('AdminAuditLogs')
        .select('*')
        .eq('action', 'APP_SETTINGS')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching settings:', error);
        // Return default settings if there's an error
        return DEFAULT_SETTINGS;
      }

      if (settingsData && settingsData.length > 0) {
        // Parse settings from the details field
        const latestSettings = settingsData[0];
        const settings = latestSettings.details as AppSettings;

        // Merge with defaults to ensure all required fields exist
        return {
          ...DEFAULT_SETTINGS,
          ...settings
        };
      }

      // Return default settings if no settings found
      return DEFAULT_SETTINGS;

    } catch (error) {
      console.error('Error fetching settings:', error);
      return DEFAULT_SETTINGS;
    }
  },

  async updateSettings(
    supabase: SupabaseClient,
    settings: AppSettings,
    userId?: string
  ): Promise<any> {
    // simulate network delay
    await new Promise((r) => setTimeout(r, 500));

    if (!supabase) {
      return { success: false, message: 'Supabase client is required' };
    }

    try {
      // figure out which user performed this
      let currentUserId = await userService.getCurrentUserFromStorage(supabase) as any;
      
      if (!currentUserId && userId) {
        currentUserId = { ...currentUserId, id: userId };
      }

      // at this point, we have a valid user ID—proceed with the audit log
      const auditLogId = crypto.randomUUID();
      const { error: insertError } = await supabase
        .from('AdminAuditLogs')
        .insert({
          id: auditLogId,
          user_id: currentUserId?.id,
          action: 'APP_SETTINGS',
          details: settings,
          severity: 'INFO',
          created_at: new Date().toISOString(),
        });

      if (insertError) {
        return { success: false, message: `Failed to write audit log: ${insertError.message}` };
      }

      // everything went fine
      return {
        success: true,
        message: 'Settings updated successfully',
        data: settings,
      };
    } catch (err: any) {
      console.error('Error updating settings:', err);
      return { success: false, message: err.message || 'Unknown error updating settings' };
    }
  },

  async resetToDefaults(supabase: SupabaseClient, userId?: string): Promise<AppSettings> {
    await this.updateSettings(supabase, DEFAULT_SETTINGS, userId);
    return DEFAULT_SETTINGS;
  },

  // Utility method to validate settings
  validateSettings(settings: Partial<AppSettings>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (settings.siteName && settings.siteName.length < 3) {
      errors.push('Site name must be at least 3 characters');
    }

    if (settings.adminEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.adminEmail)) {
      errors.push('Please enter a valid email address');
    }

    if (settings.itemsPerPage && (settings.itemsPerPage < 5 || settings.itemsPerPage > 100)) {
      errors.push('Items per page must be between 5 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Get default settings
  getDefaults(): AppSettings {
    return { ...DEFAULT_SETTINGS };
  }
};