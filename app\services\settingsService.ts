import type { SupabaseClient } from '@supabase/supabase-js';

export interface AppSettings {
  siteName: string;
  siteDescription: string;
  adminEmail: string;
  itemsPerPage: number;
  enableRegistration: boolean;
  maintenanceMode: boolean;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

export interface SettingRecord {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object';
  created_at: string;
  updated_at: string;
}

// Default settings
const DEFAULT_SETTINGS: AppSettings = {
  siteName: 'The Convention Before Christmas',
  siteDescription: 'Ireland\'s Premier Christmas Convention',
  adminEmail: '<EMAIL>',
  itemsPerPage: 25,
  enableRegistration: true,
  maintenanceMode: false,
  timezone: 'Europe/Dublin',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
};

// Simulate API delay for consistent UX
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const settingsService = {
  async getSettings(supabase: SupabaseClient): Promise<AppSettings> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // First, check if AppSettings table exists, if not, we'll use AdminAuditLogs to store settings
      // This is a workaround since there's no dedicated settings table
      const { data: settingsData, error } = await supabase
        .from('AdminAuditLogs')
        .select('*')
        .eq('action', 'APP_SETTINGS')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching settings:', error);
        // Return default settings if there's an error
        return DEFAULT_SETTINGS;
      }

      if (settingsData && settingsData.length > 0) {
        // Parse settings from the details field
        const latestSettings = settingsData[0];
        const settings = latestSettings.details as AppSettings;

        // Merge with defaults to ensure all required fields exist
        return {
          ...DEFAULT_SETTINGS,
          ...settings
        };
      }

      // Return default settings if no settings found
      return DEFAULT_SETTINGS;

    } catch (error) {
      console.error('Error fetching settings:', error);
      return DEFAULT_SETTINGS;
    }
  },

  async updateSettings(supabase: SupabaseClient, settings: AppSettings, userId?: string): Promise<void> {
    await delay(500);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // Store settings in AdminAuditLogs as a workaround
      const auditLogId = crypto.randomUUID();

      // Get current user ID from Supabase auth if not provided
      let currentUserId = userId;
      if (!currentUserId) {
        const { data: { user: currentUser } } = await supabase.auth.getUser();
        if (currentUser) {
          currentUserId = currentUser.id;
        } else {
          console.warn('Could not get current user for settings audit log');
          return; // Skip audit logging if we can't get current user
        }
      }

      const { error } = await supabase
        .from('AdminAuditLogs')
        .insert({
          id: auditLogId,
          user_id: currentUserId,
          action: 'APP_SETTINGS',
          details: settings,
          severity: 'INFO',
          created_at: new Date().toISOString()
        });

      if (error) {
        throw new Error(error.message);
      }

      console.log('Settings updated successfully');

    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  },

  async resetToDefaults(supabase: SupabaseClient, userId?: string): Promise<AppSettings> {
    await this.updateSettings(supabase, DEFAULT_SETTINGS, userId);
    return DEFAULT_SETTINGS;
  },

  // Utility method to validate settings
  validateSettings(settings: Partial<AppSettings>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (settings.siteName && settings.siteName.length < 3) {
      errors.push('Site name must be at least 3 characters');
    }

    if (settings.adminEmail && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(settings.adminEmail)) {
      errors.push('Please enter a valid email address');
    }

    if (settings.itemsPerPage && (settings.itemsPerPage < 5 || settings.itemsPerPage > 100)) {
      errors.push('Items per page must be between 5 and 100');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Get default settings
  getDefaults(): AppSettings {
    return { ...DEFAULT_SETTINGS };
  }
};
