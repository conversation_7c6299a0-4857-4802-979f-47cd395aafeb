export interface TicketType {
  id: string;
  name: string;
  description: string;
  price: number;
  type: 'regular' | 'option';
  maxQuantity?: number;
  originalId?: string;
  selectedDay?: 'Saturday' | 'Sunday';
  multiplier?: number;
}

export interface TicketAvailability {
  ticketId: string;
  day: string;
  sold: number;
  available: number;
}

export interface CartItem {
  ticketType: TicketType;
  quantity: number;
  selectedDay?: 'Saturday' | 'Sunday';
  santaSlot?: {
    id: number;
    day: string;
    timeRange: string;
    numChildren: number;
  };
}

export interface CharityMultiplier {
  multiplier: number;
  appliedAmount: number;
  originalAmount: number;
}

export interface OrderDetails {
  email: string;
  name: string;
  phoneNumber: string;
  tickets: CartItem[];
  total: number;
  orderId: string;
  paymentIntentId: string;
  charityMultiplier?: CharityMultiplier | null;
  hasDonation?: boolean;
  donationAmount?: number;
}