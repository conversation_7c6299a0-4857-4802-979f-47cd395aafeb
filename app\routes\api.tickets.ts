import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Get all tickets
    const { data: tickets, error } = await supabase
      .from('Tickets')
      .select('*');

    if (error) throw error;

    // Get current sales to calculate availability
    const { data: sales, error: salesError } = await supabase
      .from('TicketSales')
      .select('ticket_id, quantity');

    if (salesError) throw salesError;

    // Calculate availability for each ticket
    const ticketsWithAvailability = tickets.map(ticket => {
      const sold = sales
        .filter(sale => sale.ticket_id === ticket.id)
        .reduce((sum, sale) => sum + sale.quantity, 0);

      return {
        ...ticket,
        available: ticket.max_quantity - sold
      };
    });

    return json(ticketsWithAvailability);
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return json(
      { message: 'Failed to fetch tickets' },
      { status: 500 }
    );
  }
}