import React from 'react';
import { CartItem } from '@/types/ticket';

interface TicketConfirmationStepProps {
  cart: CartItem[];
  onClose: () => void;
}

const TicketConfirmationStep: React.FC<TicketConfirmationStepProps> = ({
  cart,
  onClose,
}) => {
  const s = cart.length > 1;
  return (
    <div className="text-center">
      <h3 className="text-xl font-semibold text-white mb-4">
        Thank You!
      </h3>
      <p className="text-gray-300">
        {s ? `Your tickets have been confirmed and sent to your email.` : `Your ticket has been confirmed and sent to your email.`}
      </p>
      <button
        onClick={onClose}
        className="mt-4 px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg"
      >
        Close
      </button>
    </div>
  );
};
export default TicketConfirmationStep;