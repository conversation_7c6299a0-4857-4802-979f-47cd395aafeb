import { useEffect, useState, useRef } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { PaymentForm } from './payment-form';
import { setOrderDetails, setClientSecret } from '@/store/slices/booking-slice';
import type { Stripe } from '@stripe/stripe-js';

interface TicketPaymentStepProps {
  onSuccess: () => void;
  stripePromise: Promise<Stripe | null> | null;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
}

export function TicketPaymentStep({
  onSuccess,
  stripePromise,
  isProcessing,
  setIsProcessing,
}: TicketPaymentStepProps) {
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isFormReady, setIsFormReady] = useState(false);
  const [hasPaymentIntentBeenCreated, setHasPaymentIntentBeenCreated] = useState(false);
  const orderDetails = useSelector((state: RootState) => state.booking.orderDetails);
  const cart = useSelector((state: RootState) => state.booking.cart);
  const clientSecret = useSelector((state: RootState) => state.booking.clientSecret);
  const dispatch = useDispatch();
  const isCreatingPaymentIntent = useRef(false); // New ref to prevent duplicate API calls

  useEffect(() => {
    const createPaymentIntent = async () => {
      if (isCreatingPaymentIntent.current) {
        console.log('Skipping createPaymentIntent: already in progress.');
        return; // Prevent duplicate calls if already in progress
      }
      isCreatingPaymentIntent.current = true; // Set flag to true

      try {
        const response = await fetch('/api/create-payment-intent', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            amount: orderDetails.total,
            orderDetails,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to create payment intent');
        }

        const data = await response.json();

        if (!data.clientSecret || !data.orderId || !data.paymentIntentId) {
          throw new Error('No client secret, order ID, or payment intent ID received from server');
        }

        dispatch(setClientSecret(data.clientSecret));

        const updatedOrderDetails = {
          ...orderDetails,
          orderId: data.orderId,
          paymentIntentId: data.paymentIntentId,
        };

        if (data.metadata) {
          if (data.metadata.charityMultiplier) {
            updatedOrderDetails.charityMultiplier = data.metadata.charityMultiplier;
          }
          updatedOrderDetails.hasDonation = data.metadata.hasDonation;
          if (typeof data.metadata.donationAmount !== 'undefined') {
            updatedOrderDetails.donationAmount = data.metadata.donationAmount;
          }
        }

        dispatch(setOrderDetails(updatedOrderDetails));
        setHasPaymentIntentBeenCreated(true); // Set flag to true after successful creation
      } catch (error) {
        console.error('TicketPaymentStep: Error creating payment intent:', error);
        setErrorMessage('Failed to initialize payment. Please try again.');
      } finally {
        isCreatingPaymentIntent.current = false; // Reset flag after success or error
      }
    };

    // Only create payment intent if total > 0, no clientSecret exists, it hasn't been created yet, and not currently in progress
    if (orderDetails.total > 0 && !clientSecret && !hasPaymentIntentBeenCreated && !isCreatingPaymentIntent.current) {
      createPaymentIntent();
    } else if (orderDetails.total === 0 && (clientSecret || hasPaymentIntentBeenCreated)) {
      // Clear order details and client secret if total is 0 and intent was created
      dispatch(setClientSecret(null));
      dispatch(setOrderDetails({
        ...orderDetails,
        orderId: '',
        paymentIntentId: ''
      }));
      setHasPaymentIntentBeenCreated(false); // Reset flag if total is 0
    }
  }, [orderDetails.total, clientSecret, dispatch, hasPaymentIntentBeenCreated]);

  const appearance = {
    theme: 'night' as const,
    variables: {
      colorPrimary: '#ab8e56',
      colorBackground: '#1e1e1e',
      colorText: '#ffffff',
      colorDanger: '#ff5555',
      fontFamily: 'Arkhip, Arial, sans-serif',
      borderRadius: '8px',
    },
    rules: {
      '.Input': { border: '1px solid #ab8e56' },
      '.Label': { color: '#c6b375' },
      '.Tab': { border: '1px solid #ab8e56', backgroundColor: '#1e1e1e' },
      '.Tab--selected': { backgroundColor: '#ab8e56', color: '#ffffff' },
    },
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white mb-4">Payment Details</h2>

      {clientSecret && stripePromise ? (
        <Elements stripe={stripePromise} options={{ clientSecret, appearance }}>
          <PaymentForm
            onSuccess={() => {
              setIsProcessing(true);
              onSuccess();
              setIsProcessing(false);
            }}
            isProcessing={isProcessing}
            setIsProcessing={setIsProcessing}
            onReady={() => {
              setIsFormReady(true);
            }}
            clientSecret={clientSecret}
          />
        </Elements>
      ) : (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--darkGold)] mx-auto"></div>
          <p className="text-white mt-4">Loading payment form...</p>
        </div>
      )}

      {errorMessage && (
        <div className="mt-1 mb-2 p-2 bg-red-700/50 border border-red-500 rounded-lg text-white text-center">
          {errorMessage}
        </div>
      )}
    </div>
  );
};