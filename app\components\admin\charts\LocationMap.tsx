import { useEffect, useRef, useState } from 'react';

interface LocationData {
  id: string;
  name: string;
  value: number;
}

interface LocationMapProps {
  data: LocationData[];
  height?: number;
  highlightColor?: string;
}

export function LocationMap({ 
  data, 
  height = 300,
  highlightColor = '#f59e0b' // amber-500
}: LocationMapProps) {
  const [hoveredLocation, setHoveredLocation] = useState<LocationData | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [isClient, setIsClient] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500 dark:text-gray-400">No location data available</p>
      </div>
    );
  }

  // Normalize values for color intensity
  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue || 1; // Avoid division by zero

  const getFillColor = (value: number) => {
    // Calculate intensity based on value (0.2 to 1.0)
    const intensity = 0.2 + (0.8 * (value - minValue) / valueRange);
    return `rgba(245, 158, 11, ${intensity})`; // amber with variable opacity
  };

  const handleMouseEnter = (location: LocationData, event: React.MouseEvent) => {
    setHoveredLocation(location);
    
    // Position tooltip near cursor
    if (svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect();
      setTooltipPosition({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top - 40, // Position above cursor
      });
    }
  };

  // Simple world map data (simplified for demonstration)
  // In a real app, you might want to use a proper map library
  const mapData = [
    { id: 'US', path: 'M100,100 L150,120 L140,180 L90,160 Z', name: 'United States' },
    { id: 'CA', path: 'M90,80 L120,90 L110,150 L80,140 Z', name: 'Canada' },
    { id: 'GB', path: 'M400,80 L420,90 L410,110 L390,100 Z', name: 'United Kingdom' },
    { id: 'DE', path: 'M430,100 L450,110 L440,130 L420,120 Z', name: 'Germany' },
    { id: 'AU', path: 'M600,300 L650,320 L640,380 L590,360 Z', name: 'Australia' },
    // Add more countries as needed
  ];

  // Merge with actual data
  const countries = mapData.map(country => {
    const dataPoint = data.find(d => d.id === country.id);
    return {
      ...country,
      value: dataPoint?.value || 0,
      visible: !!dataPoint,
    };
  });

  return (
    <div className="relative" style={{ height: `${height}px` }}>
      <svg 
        ref={svgRef}
        viewBox="0 0 800 400" 
        className="w-full h-full"
        onMouseLeave={() => setHoveredLocation(null)}
      >
        {/* Background */}
        <rect width="100%" height="100%" fill="#1f2937" />
        
        {/* Countries */}
        {countries.map(country => {
          const isHovered = hoveredLocation?.id === country.id;
          const fillColor = country.visible 
            ? (isHovered ? highlightColor : getFillColor(country.value))
            : '#374151'; // gray-700 for countries without data
            
          return (
            <path
              key={country.id}
              d={country.path}
              fill={fillColor}
              stroke="#4b5563" // gray-600
              strokeWidth="0.5"
              className={`transition-all duration-200 ${country.visible ? 'cursor-pointer' : ''}`}
              onMouseEnter={(e) => {
                if (country.visible) {
                  const dataPoint = data.find(d => d.id === country.id);
                  if (dataPoint) {
                    handleMouseEnter(dataPoint, e);
                  }
                }
              }}
            />
          );
        })}
        
        {/* Tooltip */}
        {hoveredLocation && (
          <g transform={`translate(${tooltipPosition.x}, ${tooltipPosition.y})`}>
            <rect 
              x="10" 
              y="-30" 
              width="180" 
              height="40" 
              rx="4" 
              fill="#111827" 
              stroke="#374151"
              className="shadow-lg"
            />
            <text 
              x="20" 
              y="-15" 
              fill="white" 
              fontSize="12"
              fontWeight="500"
            >
              {hoveredLocation.name}: {hoveredLocation.value.toLocaleString()} users
            </text>
            <polygon 
              points="10,-10 0,0 10,10" 
              fill="#111827" 
              transform="translate(100, 10)"
              stroke="#374151"
              strokeWidth="1"
            />
          </g>
        )}
      </svg>
      
      {/* Legend */}
      <div className="absolute bottom-4 right-4 bg-zinc-800 bg-opacity-80 p-3 rounded-md border border-amber-600/30">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-300">Low</span>
          <span className="text-xs text-gray-300 ml-4">High</span>
        </div>
        <div className="h-3 w-full bg-gradient-to-r from-amber-500/20 via-amber-500/60 to-amber-500 rounded-full"></div>
        <div className="mt-1 text-xs text-gray-400 text-center">User Count</div>
      </div>
    </div>
  );
}
