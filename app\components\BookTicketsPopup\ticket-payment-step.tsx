"use client";

import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import PaymentForm from './PaymentForm';

interface TicketPaymentStepProps {
  stripePromise: any;
  stripeOptions: any;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  handlePaymentSuccess: () => void;
}

const TicketPaymentStep: React.FC<TicketPaymentStepProps> = ({
  stripePromise,
  stripeOptions,
  isProcessing,
  setIsProcessing,
  handlePaymentSuccess,
}) => {
  return (
    <div className="p-4">
      <div className="flex items-center mb-4 w-full overflow-x-hidden">
        <h3 className="text-xl font-semibold text-white ml-4">
          Payment Details
        </h3>
      </div>
      <div id="payment-form-container">
        {stripeOptions ? (
          <div className="space-y-4">
            <Elements stripe={stripePromise} options={stripeOptions}>
              <PaymentForm
                onSuccess={handlePaymentSuccess}
                isProcessing={isProcessing}
                setIsProcessing={setIsProcessing}
                clientSecret={stripeOptions.clientSecret}
              />
            </Elements>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--darkGold)] mx-auto"></div>
            <div className="text-white text-center mt-4">Loading payment form...</div>
          </div>
        )}
      </div>
    </div>
  );
};
export default TicketPaymentStep;