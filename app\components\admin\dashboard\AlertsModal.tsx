import React from 'react';
import { Modal } from '@/app/components/shared/Modal';
import { AuditLog } from '@/app/store/slices/auditLogsSlice';
import {
  AlertTriangle,
  Info,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';

interface AlertsModalProps {
  isOpen: boolean;
  onClose: () => void;
  alerts: AuditLog[];
}

export const AlertsModal: React.FC<AlertsModalProps> = ({ isOpen, onClose, alerts }) => {

  // Get alert styles based on severity (duplicated from DashboardOverview for now)
  const getAlertStyles = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'error':
        return {
          bg: 'bg-red-900/20',
          border: 'border-red-800',
          icon: <AlertCircle className="w-5 h-5 text-red-500" />,
          iconBg: 'bg-red-900'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-900/20',
          border: 'border-yellow-800',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-500" />,
          iconBg: 'bg-yellow-900'
        };
      case 'success':
        return {
          bg: 'bg-green-900/20',
          border: 'border-green-800',
          icon: <CheckCircle2 className="w-5 h-5 text-green-500" />,
          iconBg: 'bg-green-900'
        };
      default: // info
        return {
          bg: 'bg-blue-900/20',
          border: 'border-blue-800',
          icon: <Info className="w-5 h-5 text-blue-500" />,
          iconBg: 'bg-blue-900'
        };
    }
  };

  // Format date to relative time (duplicated from DashboardOverview for now)
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="All System Alerts" className="max-w-2xl">
      <div className="max-h-[70vh] overflow-y-auto pr-2">
        {alerts.length > 0 ? (
          alerts.map((log) => {
            const styles = getAlertStyles(log.severity || 'info');
            const details = typeof log.details === 'object'
              ? log.details.message || JSON.stringify(log.details, null, 2)
              : log.details;

            return (
              <div
                key={log.id}
                className={`flex items-start p-3 rounded-md border mb-3 ${styles.border} ${styles.bg}`}
              >
                <div className={`p-2 rounded-full ${styles.iconBg} mt-1 mr-3 flex-shrink-0`}>
                  {styles.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-100 truncate">
                      {log.action}
                    </h3>
                    <span className="text-xs text-gray-400 ml-2 whitespace-nowrap">
                      {formatRelativeTime(log.created_at)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-400 mt-1 break-words">
                    {details}
                  </p>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-4">
            <p className="text-gray-400">No alerts to display.</p>
            <p className="text-sm text-gray-500 mt-1">The system is running smoothly.</p>
          </div>
        )}
      </div>
    </Modal>
  );
};