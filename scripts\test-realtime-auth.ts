/**
 * Real-time Authentication Test Script
 *
 * This script tests real-time subscriptions with anonymous authentication
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables directly from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://trbcgeffweprfnovhggm.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYmNnZWZmd2VwcmZub3ZoZ2dtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mjc1NjI0NSwiZXhwIjoyMDU4MzMyMjQ1fQ.ZkQQbOrwUN7Eqert9omwEPwLSj6itNr6vZB4dv1tXOI';

console.log('🔐 Testing Real-time with Anonymous Authentication\n');

const supabase = createClient(supabaseUrl, supabaseKey);

async function testRealtimeWithAuth() {
  try {
    // Step 1: Sign in anonymously
    console.log('1️⃣ Signing in anonymously...');
    const { data: authData, error: authError } = await supabase.auth.signInAnonymously();

    if (authError) {
      console.error('❌ Anonymous auth failed:', authError);
      return;
    }

    console.log('✅ Anonymous authentication successful');
    console.log('   User ID:', authData.user?.id);
    console.log('   Role:', authData.user?.role);

    // Step 2: Test table access
    console.log('\n2️⃣ Testing table access...');
    const { data: orders, error: ordersError } = await supabase
      .from('Orders')
      .select('*')
      .limit(1);

    if (ordersError) {
      console.error('❌ Orders access failed:', ordersError);
    } else {
      console.log('✅ Orders table accessible');
    }

    // Step 3: Test real-time subscription
    console.log('\n3️⃣ Testing real-time subscription...');

    const channel = supabase
      .channel('test-orders-auth')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Orders',
        },
        (payload) => {
          console.log('📨 Received Orders change:', payload);
        }
      )
      .subscribe((status, err) => {
        console.log(`📡 Subscription status: ${status}`);

        if (err) {
          console.error('❌ Subscription error:', err);
        } else if (status === 'SUBSCRIBED') {
          console.log('✅ Successfully subscribed to Orders');

          // Test inserting a record
          setTimeout(async () => {
            console.log('\n4️⃣ Testing record insertion...');

            const testOrder = {
              name: 'Auth Test Customer',
              email: '<EMAIL>',
              total: 123.45,
              status: 'pending',
              payment_intent_id: `auth_test_${Date.now()}`
            };

            const { data, error } = await supabase
              .from('Orders')
              .insert([testOrder])
              .select()
              .single();

            if (error) {
              console.error('❌ Insert error:', error);
            } else {
              console.log('✅ Test order created:', data.id);

              // Clean up after 3 seconds
              setTimeout(async () => {
                await supabase.from('Orders').delete().eq('id', data.id);
                console.log('🧹 Cleaned up test order');

                // Unsubscribe and exit
                supabase.removeChannel(channel);
                console.log('\n🎉 Test completed successfully!');
                process.exit(0);
              }, 3000);
            }
          }, 2000);
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Channel error - check RLS policies');
          process.exit(1);
        } else if (status === 'CLOSED') {
          console.warn('⚠️ Channel closed - likely RLS issue');
          process.exit(1);
        }
      });

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testRealtimeWithAuth();