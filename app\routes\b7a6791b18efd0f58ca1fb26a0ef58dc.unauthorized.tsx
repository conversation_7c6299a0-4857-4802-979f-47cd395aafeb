import { Link } from '@remix-run/react';
import { Shield, ArrowLeft } from 'lucide-react';

export default function Unauthorized() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-zinc-900 p-4">
      <div className="w-full max-w-md">
        <div className="bg-zinc-800 rounded-lg shadow-lg p-8 text-center border border-amber-600/30">
          <div className="flex justify-center mb-6">
            <div className="p-3 bg-amber-900/30 rounded-full">
              <Shield className="w-12 h-12 text-amber-400" />
            </div>
          </div>

          <h1 className="text-2xl font-bold text-white mb-2">
            Access Denied
          </h1>

          <p className="text-white/80 mb-6">
            You don't have permission to access this page. Please contact an administrator if you believe this is an error.
          </p>

          <div className="flex justify-center">
            <Link
              to="/b7a6791b18efd0f58ca1fb26a0ef58dc"
              className="flex items-center space-x-2 px-4 py-2 bg-white hover:bg-amber-500 text-white rounded-md transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}