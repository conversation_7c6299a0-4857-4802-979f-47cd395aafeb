import { useState, useEffect } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';

interface PaymentFormProps {
  onSuccess: () => void;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  onReady: () => void;
  clientSecret: string;
}

export function PaymentForm({
  onSuccess,
  isProcessing,
  setIsProcessing,
  onReady,
  clientSecret,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');

    if (!stripe || !elements || !clientSecret) {
      setErrorMessage('Payment system not initialized');
      return;
    }

    setIsProcessing(true);

    try {
      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw submitError;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        confirmParams: {
          return_url: `${window.location.origin}/book-tickets?payment_intent=${clientSecret.split('_secret_')[0]}`,
        },
        redirect: 'if_required',
      });

      if (error) {
        throw error;
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        onSuccess();
      } else {
        throw new Error('Payment failed. Please try again.');
      }
    } catch (err: any) {
      console.error('PaymentForm: Payment error:', err);
      setErrorMessage(err.message || 'Payment failed');
      setIsProcessing(false);
    }
  };

  const isButtonDisabled = isLoading || isProcessing || !stripe || !elements || !clientSecret;

  return (
    <div>
      {isLoading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--darkGold)] mx-auto"></div>
          <p className="text-white mt-4">Loading payment form...</p>
        </div>
      )}
      
      <form
        onSubmit={handleSubmit}
        className={`space-y-6 ${isLoading ? 'hidden' : ''}`}
      >
        <PaymentElement
          id="payment-element"
          options={{
            layout: 'accordion',
            paymentMethodOrder: ['card'],
            defaultValues: {
              billingDetails: {
                email: '',
              },
            },
          }}
          onReady={() => {
            setIsLoading(false);
            onReady();
          }}
          onChange={(data: any) => {
            //console.log('PaymentForm: Payment element changed:', data);
          }}
          onLoaderStart={() => {
            //console.log('PaymentForm: Payment element loader started');
            setIsLoading(true);
          }}
        />

        {errorMessage && (
          <div className="p-4 bg-red-700/50 border border-red-500 rounded-lg text-white">
            {errorMessage}
          </div>
        )}

        <button
          type="submit"
          disabled={isButtonDisabled}
          className={`w-full py-2 px-4 rounded-lg bg-[var(--darkGold)] text-white font-semibold hover:bg-[#95784a] transition-colors ${isButtonDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isProcessing ? 'Processing...' : isLoading ? 'Loading...' : 'Pay Now'}
        </button>
      </form>
    </div>
  );
};