/* eslint-disable @typescript-eslint/no-explicit-any */
import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Example query to get multiplier usage statistics
    const { data, error } = await supabase
      .from('CharityDonations')
      .select('metadata')
      .not('metadata->multiplier_info', 'is', null);

    if (error) throw error;

    // Process the data to get multiplier statistics
    const stats = data.reduce((acc: any, donation) => {
      const multiplier = donation.metadata.multiplier_info?.multiplier_used;
      if (multiplier) {
        acc[multiplier] = (acc[multiplier] || 0) + 1;
      }
      return acc;
    }, {});

    return json({
      multiplierStats: stats,
      totalDonationsWithMultipliers: data.length
    });
  } catch (error) {
    console.error('Error fetching multiplier statistics:', error);
    return json(
      { error: 'Failed to fetch multiplier statistics' },
      { status: 500 }
    );
  }
}