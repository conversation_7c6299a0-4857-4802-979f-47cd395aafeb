import { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '@/app/store/hooks';
import { selectRecentNotifications, removeNotification } from '@/app/store/slices/realtimeSlice';
import { X, CheckCircle, AlertCircle, Info, Trash2 } from 'lucide-react';

interface ToastProps {
  id: string;
  type: 'insert' | 'update' | 'delete';
  message: string;
  table: string;
  timestamp: string; // ISO string instead of Date
  onClose: () => void;
}

const Toast = ({ id, type, message, table, timestamp, onClose }: ToastProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Auto-dismiss after 5 seconds
    const timer = setTimeout(() => {
      handleClose();
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case 'insert':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'update':
        return <Info className="h-5 w-5 text-blue-400" />;
      case 'delete':
        return <Trash2 className="h-5 w-5 text-red-400" />;
      default:
        return <AlertCircle className="h-5 w-5 text-yellow-400" />;
    }
  };

  const getBorderColor = () => {
    switch (type) {
      case 'insert':
        return 'border-green-500/50';
      case 'update':
        return 'border-blue-500/50';
      case 'delete':
        return 'border-red-500/50';
      default:
        return 'border-yellow-500/50';
    }
  };

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isExiting
          ? 'translate-x-0 opacity-100'
          : 'translate-x-full opacity-0'
        }
        max-w-sm w-full bg-zinc-800 border ${getBorderColor()} rounded-lg shadow-lg pointer-events-auto
      `}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 w-0 flex-1">
            <p className="text-sm font-medium text-amber-100">
              {message}
            </p>
            <p className="mt-1 text-xs text-amber-100/60">
              {new Date(timestamp).toLocaleTimeString()}
            </p>
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              className="bg-zinc-800 rounded-md inline-flex text-amber-100/60 hover:text-amber-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
              onClick={handleClose}
            >
              <span className="sr-only">Close</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const RealtimeToastContainer = () => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector(state => selectRecentNotifications(state, 3));

  // Only show notifications for transaction-related tables
  const transactionNotifications = notifications.filter(notification =>
    ['Orders', 'VendorStands', 'CharityDonations'].includes(notification.table)
  );

  const handleRemoveNotification = (id: string) => {
    dispatch(removeNotification(id));
  };

  if (transactionNotifications.length === 0) {
    return null;
  }

  return (
    <div
      aria-live="assertive"
      className="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start z-50"
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        {transactionNotifications.map((notification) => (
          <Toast
            key={notification.id}
            id={notification.id}
            type={notification.type}
            message={notification.message}
            table={notification.table}
            timestamp={notification.timestamp}
            onClose={() => handleRemoveNotification(notification.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default RealtimeToastContainer;
