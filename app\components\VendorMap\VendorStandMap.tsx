import { useState, useEffect, useCallback } from 'react';
import StandIndicators from "@/app/components/VendorMap/StandIndicators";
import BookingModal from "@/app/components/VendorMap/BookingModal";
import Portal from '@/app/components/Portal';
import { useSocket } from '@/hooks/useSocket';

interface StandPosition {
    id: number;
    x: number;
    y: number;
    isAvailable: boolean;
    price: number;
    isArtist: boolean;
    tableSize: string;
    reserved: boolean; // Added to match the required interface
}

interface VendorStandMapProps {
    standPositions: StandPosition[];
    bookedStands: number[];
}

export default function VendorStandMap({ standPositions, bookedStands: initialBookedStands }: VendorStandMapProps) {
    const [stands, setStands] = useState(standPositions);
    const [bookedStands, setBookedStands] = useState<number[]>(initialBookedStands);
    const [selectedStand, setSelectedStand] = useState<number | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const socket = useSocket();

    const updateStandAvailability = useCallback((standNumber: number) => {
        setStands(prevStands => {
            const newStands = prevStands.map(stand => {
                if (stand.id === standNumber) {
                    return { ...stand, isAvailable: false };
                }
                return stand;
            });
            return newStands;
        });

        // Update local bookedStands state
        setBookedStands(prev => [...prev, standNumber]);
    }, []);

    const handleStandClick = (standNumber: number) => {
        setSelectedStand(standNumber);
        setIsModalOpen(true);
    };

    const handleBookingSuccess = (standNumber: number) => {
        updateStandAvailability(standNumber);
        //setIsModalOpen(false);
    };

    useEffect(() => {
        if (!socket) return;

        socket.on('standBooked', ({ standNumber }) => {
            updateStandAvailability(standNumber);
        });

        return () => {
            if (socket) {
                socket.off('standBooked');
            }
        };
    }, [socket, updateStandAvailability]);

    return (
        <>
            <div className="relative w-full">
                <StandIndicators
                    onStandClick={handleStandClick}
                    stands={stands}
                />
            </div>

            {isModalOpen && selectedStand && (
                <Portal>
                    <BookingModal
                        standNumber={selectedStand}
                        onClose={() => setIsModalOpen(false)}
                        onBookingSuccess={handleBookingSuccess}
                        updateStandAvailability={updateStandAvailability}
                        bookedStands={bookedStands}
                        tableSize={stands.find(stand => stand.id === selectedStand)?.tableSize || ''}
                        price={stands.find(stand => stand.id === selectedStand)?.price || 0}
                        isArtist={stands.find(stand => stand.id === selectedStand)?.isArtist || false}
                    />
                </Portal>
            )}
        </>
    );
};
