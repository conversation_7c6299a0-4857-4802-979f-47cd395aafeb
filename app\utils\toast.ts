import { toast, ToastOptions } from 'react-toastify';

// Default toast configuration
const defaultOptions: ToastOptions = {
  position: "top-right",
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: "dark", // Matches admin dashboard theme
};

// Success toast
export const showSuccessToast = (message: string, options?: Partial<ToastOptions>) => {
  toast.success(message, {
    ...defaultOptions,
    ...options,
  });
};

// Error toast
export const showErrorToast = (message: string, options?: Partial<ToastOptions>) => {
  toast.error(message, {
    ...defaultOptions,
    ...options,
  });
};

// Info toast
export const showInfoToast = (message: string, options?: Partial<ToastOptions>) => {
  toast.info(message, {
    ...defaultOptions,
    ...options,
  });
};

// Warning toast
export const showWarningToast = (message: string, options?: Partial<ToastOptions>) => {
  toast.warn(message, {
    ...defaultOptions,
    ...options,
  });
};

// Loading toast (returns toast ID for updating)
export const showLoadingToast = (message: string) => {
  return toast.loading(message, {
    ...defaultOptions,
    autoClose: false,
  });
};

// Update loading toast to success
export const updateToastToSuccess = (toastId: any, message: string) => {
  toast.update(toastId, {
    render: message,
    type: "success",
    isLoading: false,
    autoClose: 5000,
  });
};

// Update loading toast to error
export const updateToastToError = (toastId: any, message: string) => {
  toast.update(toastId, {
    render: message,
    type: "error",
    isLoading: false,
    autoClose: 5000,
  });
};

// Export-specific toasts
export const showExportStartToast = (format: string) => {
  return showLoadingToast(`Preparing ${format.toUpperCase()} export...`);
};

export const showExportSuccessToast = (format: string, recordCount: number) => {
  showSuccessToast(`Successfully exported ${recordCount} records as ${format.toUpperCase()}`);
};

export const showExportErrorToast = (format: string, error?: string) => {
  showErrorToast(`Failed to export ${format.toUpperCase()}${error ? `: ${error}` : ''}`);
};
