import React, { useEffect, useRef, useState } from 'react';
import { useOutsideClick } from '@/hooks/useOutsideClick';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeftIcon, InfoIcon } from 'lucide-react';
import { loadStripe } from '@stripe/stripe-js';
import { TicketType, CartItem, OrderDetails } from '@/types/ticket';
import { toast } from 'react-toastify';
import 'tippy.js/dist/tippy.css';
import 'tippy.js/themes/light.css';
import FamilyPassInfoModal from '@/app/components/BookTicketsPopup/FamilyPassInfoModal';
import SantaInfoModal from '@/app/components/BookTicketsPopup/SantaInfoModal';
import SantaSlotModal from '@/app/components/BookTicketsPopup/SantaSlotModal';
import DaySelectionModal from '@/app/components/BookTicketsPopup/DaySelectionModal';
import CharityInfoModal from '@/app/components/BookTicketsPopup/CharityInfoModal';
import TicketSelectStep from './ticket-select-step';
import TicketDetailsStep from './ticket-details-step';
import TicketPaymentStep from './ticket-payment-step';
import TicketConfirmationStep from './ticket-confirmation-step';

// Move stripePromise inside a function to safely access window
const getStripePromise = () => {
  if (typeof window !== 'undefined') {
    return loadStripe(window.ENV.STRIPE_PUBLISHABLE_KEY);
  }
  return null;
};

interface TicketModalProps {
  isOpen: boolean;
  onClose: () => void;
}

type Step = 'select' | 'details' | 'payment' | 'confirmation';

interface CharityMultiplier {
  multiplier: number;
  appliedAmount: number;
  originalAmount: number;
}

const TicketModal: React.FC<TicketModalProps> = ({ isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [currentStep, setCurrentStep] = useState<Step>('select');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [orderDetails, setOrderDetails] = useState<OrderDetails>({
    email: '',
    name: '',
    phoneNumber: '',
    tickets: [],
    total: 0,
    orderId: '',
    paymentIntentId: ''
  });
  const [clientSecret, setClientSecret] = useState<string>('');
  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([]);
  const [loading, setLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [stripeOptions, setStripeOptions] = useState<any>(null);
  const [preloadStripe, setPreloadStripe] = useState(false);
  const [isFamilyInfoOpen, setIsFamilyInfoOpen] = useState(false);
  const [isSantaInfoOpen, setIsSantaInfoOpen] = useState(false);
  const [isSantaSlotModalOpen, setIsSantaSlotModalOpen] = useState(false);
  const [selectedSantaTicket, setSelectedSantaTicket] = useState<TicketType | null>(null);
  const [isDaySelectionModalOpen, setIsDaySelectionModalOpen] = useState(false);
  const [selectedTicketForDay, setSelectedTicketForDay] = useState<TicketType | null>(null);
  const [selectedDays, setSelectedDays] = useState<Record<string, 'Saturday' | 'Sunday'>>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCharityInfoOpen, setIsCharityInfoOpen] = useState(false);
  const [isRemoveDayModalOpen, setIsRemoveDayModalOpen] = useState(false);
  const [ticketToRemove, setTicketToRemove] = useState<TicketType | null>(null);
  const [charityMultiplier, setCharityMultiplier] = useState<CharityMultiplier | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [stripePromise, setStripePromise] = useState<any>(null);

  const Toast = () => toast('');

  const getPreviousStep = (currentStep: Step): Step => {
    switch (currentStep) {
      case 'details':
        return 'select';
      case 'payment':
        return 'details';
      default:
        return 'select';
    }
  };

  const updateQuantity = (ticketType: TicketType, newQuantity: number) => {
    // Prevent negative quantities
    if (newQuantity < 0) return;

    // Special handling for Santa option
    if (isSantaOption(ticketType.id) && newQuantity > 0) {
      setSelectedSantaTicket(ticketType);
      setIsSantaSlotModalOpen(true);
      return;
    }

    // For 1-day tickets
    if (isOneDayTicket(ticketType.id)) {
      const currentQuantity = getTicketQuantity(ticketType.id);

      // Check if we're incrementing (pressing the + button)
      if (newQuantity > currentQuantity) {
        // Always show day selection modal when adding a ticket
        setSelectedTicketForDay(ticketType);
        setIsDaySelectionModalOpen(true);
        return;
      }

      // For decrement (removing a ticket)
      if (newQuantity < currentQuantity) {
        // Find all day variants of this ticket
        const variants = cart.filter(item =>
          (item.ticketType.originalId === ticketType.id ||
            item.ticketType.id === ticketType.id) &&
          item.quantity > 0
        );

        // If there's only one variant, decrement it
        if (variants.length === 1) {
          const variant = variants[0];
          setCart(prev => {
            const newCart = prev.map(item =>
              item.ticketType.id === variant.ticketType.id
                ? { ...item, quantity: Math.max(0, item.quantity - 1) }
                : item
            ).filter(item => item.quantity > 0);

            // Check if we need to remove Santa options
            if (!hasRegularTickets(newCart)) {
              return newCart.filter(item => !isSantaOption(item.ticketType.id));
            }
            return newCart;
          });
        }
        // If there are multiple variants, show a modal to select which one to decrement
        else if (variants.length > 1) {
          setTicketToRemove(ticketType);
          setIsRemoveDayModalOpen(true);
        }
        return;
      }
    }

    // For tickets that already have a day assigned or for 2-day tickets
    setCart(prev => {
      let newCart;
      if (newQuantity === 0) {
        newCart = prev.filter((item) => item.ticketType.id !== ticketType.id);
      } else {
        const existingItem = prev.find((item) => item.ticketType.id === ticketType.id);
        if (existingItem) {
          newCart = prev.map((item) =>
            item.ticketType.id === ticketType.id ? { ...item, quantity: newQuantity } : item
          );
        } else {
          newCart = [...prev, { ticketType, quantity: newQuantity }];
        }
      }

      // Check if we need to remove Santa options
      if (!hasRegularTickets(newCart)) {
        return newCart.filter(item => !isSantaOption(item.ticketType.id));
      }
      return newCart;
    });
  };

  const handlePlusClick = (ticketType: TicketType) => {
    // Always trigger modal on plus button click.
    setSelectedTicketForDay(ticketType);
    setIsDaySelectionModalOpen(true);

    // Increment the ticket quantity.
    const currentQuantity = getTicketQuantity(ticketType.id);
    updateQuantity(ticketType, currentQuantity + 1);
  };

  const calculateTotal = () => {
    return cart.reduce(
      (total, item) => total + item.ticketType.price * item.quantity,
      0
    );
  };

  const applyCharityMultiplier = (multiplier: number) => {
    const charityItem = cart.find(item => item.ticketType.id === 'charity-donation');
    if (!charityItem) return;

    const originalAmount = charityItem.quantity * charityItem.ticketType.price;
    const newAmount = originalAmount * multiplier;

    // Update the cart with the new price
    setCart(prev => prev.map(item => {
      if (item.ticketType.id === 'charity-donation') {
        return {
          ...item,
          ticketType: {
            ...item.ticketType,
            price: newAmount / item.quantity,
            multiplier: multiplier
          }
        };
      }
      return item;
    }));

    // Store multiplier information
    setCharityMultiplier({
      multiplier,
      appliedAmount: newAmount,
      originalAmount
    });
  };

  const handleSubmitDetails = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSending(true);

    // Validate form
    if (!orderDetails.email || !orderDetails.name || !orderDetails.phoneNumber) {
      toast.error('Please fill in all fields');
      setIsSending(false);
      return;
    }

    // Validate email format
    if (!/\S+@\S+\.\S+/.test(orderDetails.email)) {
      toast.error('Please enter a valid email address');
      setIsSending(false);
      return;
    }

    const totalAmount = calculateTotal();

    // Include total in orderDetailsWithMultiplier
    const orderDetailsWithMultiplier = {
      ...orderDetails,
      tickets: cart.map(item => {
        if (isOneDayTicket(item.ticketType.id) && selectedDays[item.ticketType.id] && !item.selectedDay) {
          return {
            ...item,
            selectedDay: selectedDays[item.ticketType.id]
          };
        }
        return item;
      }),
      total: totalAmount, // Add this line
      charityMultiplier: charityMultiplier
    };

    try {
      const response = await fetch('/api/create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: calculateTotal(), // Still send the correct amount separately
          orderDetails: { // Include the calculated total in orderDetails as well
            ...orderDetailsWithMultiplier,
            total: calculateTotal(),
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      const data = await response.json();
      if (!data.clientSecret || !data.orderId || !data.paymentIntentId) {
        console.error({ data });
        throw new Error('No client secret, order ID, or payment intent ID received from server');
      }

      setClientSecret(data.clientSecret);

      // Update order details with the response data
      const updatedOrderDetails = {
        ...orderDetailsWithMultiplier,
        orderId: data.orderId,
        paymentIntentId: data.paymentIntentId
      };

      // Add all metadata fields if they exist in the response
      if (data.metadata) {
        // Handle charity multiplier data
        if (data.metadata.charityMultiplier) {
          updatedOrderDetails.charityMultiplier = data.metadata.charityMultiplier;
        }

        // Add hasDonation flag
        updatedOrderDetails.hasDonation = data.metadata.hasDonation;

        // Add donationAmount
        if (typeof data.metadata.donationAmount !== 'undefined') {
          updatedOrderDetails.donationAmount = data.metadata.donationAmount;
        }
      }

      setOrderDetails(updatedOrderDetails);

      // Add a small delay to ensure the client secret is set before moving to payment step
      setTimeout(() => {
        setCurrentStep('payment');
        setIsSending(false);
      }, 100);
    } catch (error: any) {
      console.error('Error creating payment intent:', error.message);
      toast.error(error.message);
      setIsSending(false);
    }
  };

  const handlePaymentSuccess = async () => {
    console.log('handlePaymentSuccess called. Current orderDetails:', orderDetails);
    try {
      const response = await fetch('/api/send-ticket-confirmation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderDetails),
      });

      const data = await response.json();

      // Check for error message even if response status is 200
      if (!response.ok || data.message?.includes('Failed to send confirmation email')) {
        setErrorMessage(data.message || 'Failed to send confirmation');
        setIsProcessing(false);
        return;
      }

      setCurrentStep('confirmation');
    } catch (error: any) {
      console.error('Error sending confirmation:', error.message);
      setErrorMessage(error.message || 'Failed to send confirmation');
      setIsProcessing(false);
    }
  };

  // Function to get ticket quantity including day variants
  const getTicketQuantity = (ticketId: string) => {
    return cart.reduce((total, item) => {
      // Match either exact ID or day variants of this ticket
      if (item.ticketType.id === ticketId ||
        (item.ticketType.originalId && item.ticketType.originalId === ticketId)) {
        return total + item.quantity;
      }
      return total;
    }, 0);
  };

  // Add this function to check if regular tickets are in cart
  const hasRegularTickets = (cartToCheck = cart): boolean => {
    return cartToCheck.some(item =>
      item.ticketType.type !== 'option' &&
      item.ticketType.id !== 'charity-donation' &&
      !isSantaOption(item.ticketType.id)
    );
  };

  // Add this function to check if a ticket is a family pass
  const isFamilyPass = (ticketId: string) => {
    return ticketId.includes('family');
  };

  // Add function to check if a ticket is the Santa option
  const isSantaOption = (ticketId: string) => {
    return ticketId.includes('santa-option');
  };

  // Add function to check if a ticket is a 1-day ticket
  const isOneDayTicket = (ticketId: string) => {
    return ticketId.includes('1day');
  };

  // Handle day selection for 1-day tickets
  const handleDaySelect = (day: 'Saturday' | 'Sunday') => {
    if (selectedTicketForDay) {
      // Create a unique ID for this ticket+day combination
      const ticketDayId = `${selectedTicketForDay.id}-${day.toLowerCase()}`;

      // Store the selected day for this ticket
      setSelectedDays(prev => ({
        ...prev,
        [ticketDayId]: day
      }));

      // Create a modified ticket type with day information
      // Don't include the day in the name, we'll display it separately
      const ticketWithDay = {
        ...selectedTicketForDay,
        id: ticketDayId,
        originalId: selectedTicketForDay.id, // Keep track of the original ID
        selectedDay: day // Store the day directly in the ticket object
      };

      // Now add the ticket to the cart
      setCart((prev) => {
        const existingItem = prev.find(item => item.ticketType.id === ticketDayId);
        if (existingItem) {
          // If this exact day+ticket combination already exists, increment its quantity
          return prev.map(item =>
            item.ticketType.id === ticketDayId
              ? { ...item, quantity: item.quantity + 1 }
              : item
          );
        }
        // Otherwise add a new entry for this day+ticket combination
        return [...prev, { ticketType: ticketWithDay, quantity: 1, selectedDay: day }];
      });

      // Make sure to close the modal
      setIsDaySelectionModalOpen(false);
    }
  };

  const handleSantaSlotSelect = (slotId: number, day: string, timeRange: string, numChildren: number = 1) => {
    if (selectedSantaTicket) {
      // Calculate the total price based on number of children
      const additionalChildrenCount = numChildren - 1;
      const additionalCost = additionalChildrenCount * 6;
      const totalPrice = selectedSantaTicket.price + additionalCost;

      const modifiedTicketType = {
        ...selectedSantaTicket,
        price: totalPrice,
        description: `10-minute slot with Santa (includes ${numChildren} ${numChildren === 1 ? 'child' : 'children'})`
      };

      const santaSlotDetails = {
        id: slotId,
        day,
        timeRange,
        numChildren
      };

      setCart((prev) => {
        const existingItem = prev.find(item => item.ticketType.id === selectedSantaTicket.id);
        if (existingItem) {
          return prev.map(item =>
            item.ticketType.id === selectedSantaTicket.id
              ? {
                ...item,
                ticketType: modifiedTicketType,
                quantity: 1,
                santaSlot: santaSlotDetails
              }
              : item
          );
        }
        return [...prev, {
          ticketType: modifiedTicketType,
          quantity: 1,
          santaSlot: santaSlotDetails
        }];
      });
    }
  };

  const handleDayRemoval = (day: 'Saturday' | 'Sunday') => {
    if (ticketToRemove) {
      const ticketDayId = `${ticketToRemove.id}-${day.toLowerCase()}`;

      setCart(prev =>
        prev.map(item =>
          item.ticketType.id === ticketDayId
            ? { ...item, quantity: Math.max(0, item.quantity - 1) }
            : item
        ).filter(item => item.quantity > 0)
      );

      setIsRemoveDayModalOpen(false);
    }
  };

  const renderCharityMultipliers = (ticket: TicketType) => {
    if (ticket.id !== 'charity-donation') return null;

    const multipliers = [10, 100, 1000];

    return (
      <div className="flex gap-2 mt-4 items-center justify-between">
        {multipliers.map((multiplier) => (
          <button
            key={multiplier}
            onClick={() => applyCharityMultiplier(multiplier)}
            className="px-3 py-1 text-sm bg-[var(--darkGold)] text-white rounded hover:bg-[var(--darkGold)] transition-colors"
          >
            {multiplier}x
          </button>
        ))}
      </div>
    );
  };

  const handleSantaOptionClick = (ticket: TicketType) => {
    setSelectedSantaTicket(ticket);
    setIsSantaSlotModalOpen(true);
  };

  // Update stripeOptions configuration
  useEffect(() => {
    if (!clientSecret && !preloadStripe) {
      return;
    }

    const baseAppearance = {
      theme: 'night' as const,
      variables: {
        colorPrimary: '#ab8e56',
        colorBackground: '#1e1e1e',
        colorText: '#ffffff',
        colorDanger: '#ff5555',
        fontFamily: 'Arkhip, Arial, sans-serif',
        borderRadius: '8px',
      },
      rules: {
        '.Input': { border: '1px solid #ab8e56' },
        '.Label': { color: '#c6b375' },
        '.Tab': { border: '1px solid #ab8e56', backgroundColor: '#1e1e1e' },
        '.Tab--selected': { backgroundColor: '#ab8e56', color: '#ffffff' },
      }
    };

    // Set options based on what we have
    if (clientSecret) {
      setStripeOptions({
        clientSecret,
        appearance: baseAppearance,
      });
      setPreloadStripe(false);
    } else if (preloadStripe) {
      setStripeOptions({
        mode: 'setup',
        currency: 'eur',
        appearance: baseAppearance,
      });
    }
  }, [clientSecret, preloadStripe]);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, () => { });

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    async function fetchTickets() {
      setLoading(true);
      try {
        const response = await fetch('/api/tickets/get-tickets');

        if (!response.ok) {
          throw new Error('Failed to fetch tickets');
        }

        const data = await response.json();

        if (!data.tickets || !Array.isArray(data.tickets)) {
          console.error('Invalid ticket data format:', data);
          toast.error('Invalid ticket data received');
          setTicketTypes([]);
        } else {
          setTicketTypes(data.tickets);
        }
      } catch (err) {
        console.error('Error fetching tickets:', err);
        toast.error('Failed to load ticket information');
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      // Prevent body scrolling
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.top = `-${window.scrollY}px`;

      window.addEventListener('keydown', handleEsc);
      fetchTickets();
    }

    if (!isOpen) {
      // Restore body scrolling
      const scrollY = document.body.style.top;
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      window.scrollTo(0, parseInt(scrollY || '0') * -1);

      setCart([]);
      setOrderDetails({
        email: '',
        name: '',
        phoneNumber: '',
        tickets: [],
        total: 0,
        orderId: '',
        paymentIntentId: ''
      });
      setClientSecret('');
      setIsProcessing(false);
      setCurrentStep('select');
      setLoading(true);
    }

    return () => {
      // Cleanup
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.top = '';
      window.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen, onClose]);

  // Initialize stripe in useEffect
  useEffect(() => {
    const promise = getStripePromise();
    if (promise) {
      setStripePromise(promise);
    }
  }, []);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
            style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
          />
          <div
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
            style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
          >
            <motion.div
              ref={modalRef}
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", damping: 20, stiffness: 300 }}
              className="bg-[#121212] border border-[var(--darkGold)] rounded-lg w-full max-w-2xl p-6 shadow-xl max-h-[80vh] flex flex-col"
              style={{ position: 'relative' }}
            >
              <div className="flex justify-between items-center mb-4">
                {/* Back button */}
                {(currentStep !== 'select' && currentStep !== 'confirmation')
                  && (
                    <button
                      onClick={() => setCurrentStep(getPreviousStep(currentStep))}
                      className="flex items-center text-white hover:text-[var(--darkGold)] transition-colors duration-300"
                    >
                      <ArrowLeftIcon className="w-4 h-4 mr-1" /> Back
                    </button>
                  )}
                <h2 className="text-2xl font-bold text-white w-full text-center">Book Tickets</h2>
                {/* Close button */}
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-white"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>

              <div className="overflow-y-auto overflow-hidden flex-grow custom-scrollbar">
                {currentStep === 'select' && (
                  <TicketSelectStep
                    ticketTypes={ticketTypes}
                    cart={cart}
                    updateQuantity={updateQuantity}
                    handlePlusClick={handlePlusClick}
                    loading={loading}
                    onContinue={() => setCurrentStep('details')}
                    onOpenFamilyInfo={() => setIsFamilyInfoOpen(true)}
                    onOpenSantaInfo={() => setIsSantaInfoOpen(true)}
                    onOpenCharityInfo={() => setIsCharityInfoOpen(true)}
                    hasRegularTickets={hasRegularTickets}
                    renderCharityMultipliers={renderCharityMultipliers}
                    calculateTotal={calculateTotal}
                    setCart={setCart}
                    onSantaOptionClick={handleSantaOptionClick}
                  />
                )}
                {currentStep === 'details' && (
                  <TicketDetailsStep
                    orderDetails={orderDetails}
                    setOrderDetails={setOrderDetails}
                    isSending={isSending}
                    handleSubmitDetails={handleSubmitDetails}
                  />
                )}
                {currentStep === 'payment' && (
                  <>
                    <TicketPaymentStep
                      stripePromise={stripePromise}
                      stripeOptions={stripeOptions}
                      isProcessing={isProcessing}
                      setIsProcessing={setIsProcessing}
                      handlePaymentSuccess={handlePaymentSuccess}
                    />
                    {errorMessage && (
                      <div className="mt-1 mb-2 p-2 bg-red-700/50 border border-red-500 rounded-lg text-white text-center">
                        {errorMessage}
                      </div>
                    )}
                  </>
                )}
                {currentStep === 'confirmation' && (
                  <TicketConfirmationStep
                    cart={cart}
                    onClose={onClose}
                  />
                )}
              </div>
            </motion.div>
          </div>

          {/* Add the Family Pass Info Modal */}
          <FamilyPassInfoModal
            isOpen={isFamilyInfoOpen}
            onClose={() => setIsFamilyInfoOpen(false)}
          />
          {/* Add the Santa Info Modal */}
          <SantaInfoModal
            isOpen={isSantaInfoOpen}
            onClose={() => setIsSantaInfoOpen(false)}
          />
          {/* Add the Santa Slot Modal */}
          <SantaSlotModal
            isOpen={isSantaSlotModalOpen}
            onClose={() => setIsSantaSlotModalOpen(false)}
            onSlotSelect={handleSantaSlotSelect}
          />
          {/* Add the Day Selection Modal */}
          <DaySelectionModal
            isOpen={isDaySelectionModalOpen}
            onClose={() => setIsDaySelectionModalOpen(false)}
            onDaySelect={handleDaySelect}
          />
          {/* Add the Charity Info Modal */}
          <CharityInfoModal
            isOpen={isCharityInfoOpen}
            onClose={() => setIsCharityInfoOpen(false)}
          />
          {/* Add the Day Removal Modal */}
          {isRemoveDayModalOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
              <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-xl font-semibold text-white mb-4">
                  Select Day to Remove
                </h3>
                <p className="text-white mb-4">
                  Please select which day&apos;s ticket you want to remove:
                </p>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => handleDayRemoval('Saturday')}
                    className="px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
                  >
                    Saturday
                  </button>
                  <button
                    onClick={() => handleDayRemoval('Sunday')}
                    className="px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
                  >
                    Sunday
                  </button>
                </div>
                <button
                  onClick={() => setIsRemoveDayModalOpen(false)}
                  className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors w-full"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </>
      )}
    </AnimatePresence>
  );
};
export default TicketModal;