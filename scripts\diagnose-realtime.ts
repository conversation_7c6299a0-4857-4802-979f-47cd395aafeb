/**
 * Real-time Diagnostic Script
 * 
 * This script diagnoses real-time subscription issues
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables from process.env
const supabaseUrl = process.env.SUPABASE_URL || 'https://trbcgeffweprfnovhggm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRyYmNnZWZmd2VwcmZub3ZoZ2dtIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Mjc1NjI0NSwiZXhwIjoyMDU4MzMyMjQ1fQ.ZkQQbOrwUN7Eqert9omwEPwLSj6itNr6vZB4dv1tXOI';

console.log('🔧 Real-time Diagnostic Tool\n');

// Test 1: Basic Supabase connection
console.log('1️⃣ Testing Supabase connection...');
console.log(`   URL: ${supabaseUrl}`);
console.log(`   Service Key: ${supabaseServiceKey.substring(0, 20)}...`);

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test 2: Basic table access
async function testTableAccess() {
  console.log('\n2️⃣ Testing table access...');
  
  const tables = ['Orders', 'VendorStands', 'CharityDonations', 'NewsletterSubscribers', 'SantaBookings'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`   ❌ ${table}: ${error.message}`);
      } else {
        console.log(`   ✅ ${table}: Accessible (${data?.length || 0} records)`);
      }
    } catch (err) {
      console.log(`   ❌ ${table}: ${err}`);
    }
  }
}

// Test 3: Real-time subscription test
async function testRealtimeSubscription() {
  console.log('\n3️⃣ Testing real-time subscriptions...');
  
  const tables = ['Orders', 'VendorStands', 'CharityDonations'];
  const results: Record<string, string> = {};
  
  for (const table of tables) {
    console.log(`\n   Testing ${table} subscription...`);
    
    await new Promise((resolve) => {
      const channel = supabase
        .channel(`test-${table.toLowerCase()}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table,
          },
          (payload) => {
            console.log(`   📨 ${table} change received:`, payload);
          }
        )
        .subscribe((status, err) => {
          console.log(`   📡 ${table} status: ${status}`);
          
          if (err) {
            console.log(`   ❌ ${table} error:`, err);
            results[table] = `ERROR: ${err.message || err}`;
          } else if (status === 'SUBSCRIBED') {
            console.log(`   ✅ ${table}: Successfully subscribed`);
            results[table] = 'SUCCESS';
          } else if (status === 'CHANNEL_ERROR') {
            console.log(`   ❌ ${table}: Channel error`);
            results[table] = 'CHANNEL_ERROR';
          } else if (status === 'CLOSED') {
            console.log(`   ⚠️ ${table}: Channel closed`);
            results[table] = 'CLOSED';
          }
          
          // Clean up and resolve after 3 seconds
          setTimeout(() => {
            supabase.removeChannel(channel);
            resolve(void 0);
          }, 3000);
        });
    });
  }
  
  console.log('\n📊 Subscription Results:');
  for (const [table, result] of Object.entries(results)) {
    console.log(`   ${table}: ${result}`);
  }
}

// Test 4: Check RLS policies
async function checkRLSPolicies() {
  console.log('\n4️⃣ Checking RLS policies...');
  
  try {
    // This query checks if RLS is enabled on tables
    const { data, error } = await supabase
      .rpc('check_table_rls', {});
    
    if (error) {
      console.log('   ⚠️ Cannot check RLS policies (function may not exist)');
      console.log('   💡 This is normal - RLS check requires custom function');
    } else {
      console.log('   ✅ RLS policy check:', data);
    }
  } catch (err) {
    console.log('   ⚠️ RLS check not available');
  }
}

// Main diagnostic function
async function runDiagnostics() {
  try {
    await testTableAccess();
    await testRealtimeSubscription();
    await checkRLSPolicies();
    
    console.log('\n🎯 Diagnostic Summary:');
    console.log('   If you see CHANNEL_ERROR or subscription failures:');
    console.log('   1. Check Supabase Dashboard → Database → Replication');
    console.log('   2. Enable real-time for each table');
    console.log('   3. Check RLS policies if enabled');
    console.log('   4. Verify service role permissions');
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  } finally {
    console.log('\n👋 Diagnostic complete');
    process.exit(0);
  }
}

runDiagnostics();
