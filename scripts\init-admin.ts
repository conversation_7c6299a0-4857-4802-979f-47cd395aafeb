#!/usr/bin/env node

/**
 * <PERSON>ript to initialize the admin user in the database
 * Usage: npx ts-node scripts/init-admin.ts
 *
 * Environment variables required:
 * - SUPABASE_URL
 * - SUPABASE_SERVICE_KEY (must have service role access)
 * - ADMIN_EMAIL (optional, <NAME_EMAIL>)
 * - ADMIN_PASSWORD (optional, defaults to admin123)
 */

import { createClient } from '@supabase/supabase-js';
import * as bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Get environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
const adminName = process.env.ADMIN_NAME || 'Admin User';
const adminRole = process.env.ADMIN_ROLE || 'super_admin';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_SERVICE_KEY must be set in environment variables');
  process.exit(1);
}

// Initialize Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function initAdmin() {
  try {
    console.log('🚀 Starting admin initialization...');

    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);

    console.log('🔍 Checking if admin user already exists...');

    // Check if admin user already exists
    const { data: existingAdmin, error: fetchError } = await supabase
      .from('AdminUsers')
      .select('id')
      .eq('email', adminEmail)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "not found" error
      throw fetchError;
    }

    if (existingAdmin) {
      console.log('ℹ️  Admin user already exists. Updating password...');

      // Update existing admin user
      const { data: updatedAdmin, error: updateError } = await supabase
        .from('AdminUsers')
        .update({
          password: hashedPassword,
          name: adminName,
          role: adminRole,
          updated_at: new Date().toISOString(),
        })
        .eq('email', adminEmail)
        .select()
        .single();

      if (updateError) throw updateError;

      console.log('✅ Admin user updated successfully!');
      console.log('📋 Admin details:');
      console.log(`   Email: ${updatedAdmin.email}`);
      console.log(`   Name: ${updatedAdmin.name}`);
      console.log(`   Role: ${updatedAdmin.role}`);
      console.log('\n🔑 You can now log in with the provided credentials.');
    } else {
      // Create new admin user
      console.log('🆕 Creating new admin user...');

      const { data: newAdmin, error: createError } = await supabase
        .from('AdminUsers')
        .insert([
          {
            email: adminEmail,
            password: hashedPassword,
            name: adminName,
            role: adminRole,
          },
        ])
        .select()
        .single();

      if (createError) throw createError;

      console.log('✅ Admin user created successfully!');
      console.log('📋 Admin details:');
      console.log(`   Email: ${newAdmin.email}`);
      console.log(`   Name: ${newAdmin.name}`);
      console.log(`   Role: ${newAdmin.role}`);
      console.log('\n🔑 You can now log in with the following credentials:');
      console.log(`   Email: ${adminEmail}`);
      console.log(`   Password: ${adminPassword}`);
      console.log('\n⚠️  IMPORTANT: Change the default password after first login!');
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ Error initializing admin user:');
    console.error(error);
    process.exit(1);
  }
}

// Run the script
initAdmin();
