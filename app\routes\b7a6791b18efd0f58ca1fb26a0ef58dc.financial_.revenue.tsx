import { useState, useEffect, useCallback } from "react";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store/store";
import { fetchDashboardMetrics } from "@/app/store/slices/adminSlice";
import { AdminLayout } from "@/app/components/admin/layout/AdminLayout";
import { ProtectedRoute } from "@/app/components/admin/ProtectedRoute";
import { RevenueChart } from "@/app/components/admin/charts/RevenueChart";
import { TicketDistributionChart } from "@/app/components/admin/charts/TicketDistributionChart";
import { subDays, subMonths, subYears } from "date-fns";

// Type for revenue data item
interface RevenueByDay {
    date: string;
    revenue: number;
}

// Type for ticket distribution data
interface TicketTypeData {
    type: 'ticket' | 'vendor' | 'santa' | 'donation' | 'other';
    name: string;
    count: number;
    revenue: number;
    averagePrice?: number;
}

// Type for dashboard metrics
interface DashboardMetrics {
    totalRevenue: number;
    ticketsSold: number;
    activeUsers: number;
    conversionRate: number;
    revenueByDay: RevenueByDay[];
    ticketsByType: TicketTypeData[];
    averageOrderValue: number;
    dateRange: {
        start: Date;
        end: Date;
    };
    revenueByCategory?: Array<{
        category: string;
        revenue: number;
        percentage: number;
    }>;
    revenueByProductType?: ProductRevenue[];
}

// Type for product revenue
interface ProductRevenue {
    name: string;
    revenue: number;
}

// Type for revenue stats
interface RevenueStats {
    totalRevenue: number;
    monthlyRevenue: number;
    growthRate: number;
    topProducts: ProductRevenue[];
}

type RevenueData = {
    date: string;
    revenue: number;
};

export default function RevenueDashboard() {
    const dispatch = useDispatch<AppDispatch>();
    const [timeRange, setTimeRange] = useState<"week" | "month" | "year">("month");
    const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
    const [stats, setStats] = useState<RevenueStats | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);

    const fetchRevenueData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch metrics from Redux
            const resultAction = await dispatch(fetchDashboardMetrics());

            if (fetchDashboardMetrics.fulfilled.match(resultAction)) {
                const metricsData = resultAction.payload as unknown as DashboardMetrics;

                setMetrics(metricsData);

                // Transform revenue by day data
                const dailyRevenue = metricsData.revenueByDay || [];

                // Filter data based on selected time range
                const now = new Date();
                let filteredData = [...dailyRevenue];

                if (timeRange === "week") {
                    const oneWeekAgo = subDays(now, 7);
                    filteredData = dailyRevenue.filter((item) =>
                        new Date(item.date) >= oneWeekAgo
                    );
                } else if (timeRange === "month") {
                    const oneMonthAgo = subMonths(now, 1);
                    filteredData = dailyRevenue.filter((item) =>
                        new Date(item.date) >= oneMonthAgo
                    );
                } else if (timeRange === "year") {
                    const oneYearAgo = subYears(now, 1);
                    filteredData = dailyRevenue.filter((item) =>
                        new Date(item.date) >= oneYearAgo
                    );
                }

                // Sort by date
                filteredData.sort((a, b) =>
                    new Date(a.date).getTime() - new Date(b.date).getTime()
                );

                // Group revenue by category
                const categories: Record<string, { type: 'ticket' | 'vendor' | 'santa' | 'donation' | 'other', name: string, count: number, revenue: number }> = {
                    'adult-1day': { type: 'ticket', name: '1 Day Adult', count: 0, revenue: 0 },
                    'child-1day': { type: 'ticket', name: '1 Day Child', count: 0, revenue: 0 },
                    'family-1day': { type: 'ticket', name: '1 Day Family', count: 0, revenue: 0 },
                    'adult-2day': { type: 'ticket', name: '2 Day Adult', count: 0, revenue: 0 },
                    'child-2day': { type: 'ticket', name: '2 Day Child', count: 0, revenue: 0 },
                    'santa-option': { type: 'santa', name: 'Santa Photos', count: 0, revenue: 0 },
                    'vendor-stall': { type: 'vendor', name: 'Vendor Stalls', count: 0, revenue: 0 },
                    'charity-donation': { type: 'donation', name: 'Donations', count: 0, revenue: 0 }
                };

                // Initialize with default values if metrics is null
                if (!metricsData) {
                    throw new Error('Failed to load metrics data');
                }

                // Process ticket sales data with safe access
                const ticketSales = Array.isArray(metricsData.ticketsByType) ? metricsData.ticketsByType : [];
                if (ticketSales.length > 0) {
                    ticketSales.forEach((sale: any) => {
                        const category = categories[sale.ticket_id] || {
                            ...categories['other'],
                            name: sale.ticket_id || 'Other'
                        };
                        const quantity = Number(sale.quantity) || 0;
                        const price = Number(sale.price) || 0;

                        category.count += quantity;
                        category.revenue += quantity * price;
                    });
                }

                // Process vendor sales with safe access
                const vendorSales = Array.isArray(metricsData.ticketsByType) ? metricsData.ticketsByType : [];
                if (vendorSales.length > 0) {
                    vendorSales.forEach((sale: any) => {
                        categories['vendor-stall'].count += 1;
                        categories['vendor-stall'].revenue += Number(sale.amount) || 0;
                    });
                }

                // Process donations with safe access
                const donations = Array.isArray(metricsData.ticketsByType) ? metricsData.ticketsByType : [];
                if (donations.length > 0) {
                    donations.forEach((donation: any) => {
                        categories['charity-donation'].count += 1;
                        categories['charity-donation'].revenue += Number(donation.amount) || 0;
                    });
                }

                // Convert to array and calculate total revenue
                const allCategories = Object.values(categories);
                const totalRevenue = allCategories.reduce((sum, cat) => sum + cat.revenue, 0);

                // Prepare revenue by category for the chart
                const revenueByCategory = allCategories
                    .filter(cat => cat.revenue > 0)
                    .map(cat => ({
                        category: cat.name,
                        revenue: cat.revenue,
                        percentage: totalRevenue > 0 ? Math.round((cat.revenue / totalRevenue) * 100) : 0
                    }));

                // Prepare top products (top 5 by revenue)
                const topProducts = allCategories
                    .filter(cat => cat.revenue > 0)
                    .sort((a, b) => b.revenue - a.revenue)
                    .slice(0, 5)
                    .map(cat => ({
                        name: cat.name,
                        revenue: cat.revenue,
                        type: cat.type
                    }));

                // Calculate monthly revenue (approximation)
                const monthlyRevenue =
                    metricsData.averageOrderValue * (metricsData.ticketsSold || 0) / 12;

                setRevenueData(filteredData);
                setStats({
                    totalRevenue: metricsData.totalRevenue || 0,
                    monthlyRevenue: monthlyRevenue || 0,
                    growthRate: 0, // Placeholder; requires historical data for accurate calculation
                    topProducts,
                });
            } else {
                throw new Error(resultAction.payload as string || "Failed to fetch revenue data");
            }
        } catch (err) {
            console.error("Error fetching revenue data:", err);
            setError(err instanceof Error ? err.message : "Failed to load revenue data");
        } finally {
            setLoading(false);
        }
    }, [dispatch, timeRange]);

    useEffect(() => {
        fetchRevenueData();
    }, [fetchRevenueData]);

    return (
        <ProtectedRoute requiredRole={["super_admin", "support_admin"]}>
            <AdminLayout>
                <div className="space-y-6">
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold text-white">Revenue Analytics</h1>
                            <p className="text-amber-100/70">Track and analyze your revenue streams</p>
                        </div>
                        <div className="flex space-x-2">
                            <select
                                value={timeRange}
                                onChange={(e) =>
                                    setTimeRange(e.target.value as "week" | "month" | "year")
                                }
                                className="bg-zinc-700 border border-amber-600/50 text-amber-100 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
                            >
                                <option value="week">Last 7 Days</option>
                                <option value="month">Last 30 Days</option>
                                <option value="year">Last 12 Months</option>
                            </select>
                        </div>
                    </div>

                    {loading ? (
                        <div className="flex justify-center items-center h-64">
                            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
                        </div>
                    ) : error ? (
                        <div className="bg-red-900/30 border border-red-700 rounded-lg p-4 text-red-200">
                            <p className="font-medium">Error loading revenue data</p>
                            <p className="text-sm mt-1">{error}</p>
                            <button
                                onClick={fetchRevenueData}
                                className="mt-2 px-3 py-1 bg-red-800/50 hover:bg-red-800/70 text-sm rounded transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    ) : (
                        <>
                            {/* Stats Cards */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">Total Revenue</p>
                                    <p className="text-2xl font-bold text-white">
                                        €
                                        {stats?.totalRevenue.toLocaleString(undefined, {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}
                                    </p>
                                    <p className="text-xs text-amber-400 mt-1">All time</p>
                                </div>
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">Estimated Monthly</p>
                                    <p className="text-2xl font-bold text-white">
                                        €
                                        {stats?.monthlyRevenue.toLocaleString(undefined, {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}
                                    </p>
                                    {stats?.growthRate !== undefined && stats.growthRate > 0 ? (
                                        <p className="text-xs text-green-400 flex items-center mt-1">
                                            <span className="mr-1">↑</span>
                                            {stats.growthRate.toFixed(1)}% from last period
                                        </p>
                                    ) : stats?.growthRate !== undefined ? (
                                        <p className="text-xs text-red-400 flex items-center mt-1">
                                            <span className="mr-1">↓</span>
                                            {Math.abs(stats.growthRate).toFixed(1)}% from last period
                                        </p>
                                    ) : null}
                                </div>
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <p className="text-sm text-amber-100/70">Avg. Daily Revenue</p>
                                    <p className="text-2xl font-bold text-white">
                                        €
                                        {((stats?.monthlyRevenue || 0) / 30).toLocaleString(undefined, {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        })}
                                    </p>
                                    <p className="text-xs text-amber-400 mt-1">Based on current month</p>
                                </div>
                            </div>

                            {/* Revenue Distribution */}
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* Revenue Over Time */}
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <h3 className="text-lg font-semibold text-white mb-4">
                                        Revenue Over Time
                                        <span className="text-sm font-normal text-amber-100/70 ml-2">
                                            ({timeRange === "week"
                                                ? "Last 7 Days"
                                                : timeRange === "month"
                                                    ? "Last 30 Days"
                                                    : "Last 12 Months"})
                                        </span>
                                    </h3>
                                    <div className="h-80">
                                        {revenueData.length > 0 ? (
                                            <RevenueChart data={revenueData} title="Daily Revenue" />
                                        ) : (
                                            <div className="h-full flex items-center justify-center text-amber-100/50">
                                                No revenue data available for the selected period
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Revenue by Product */}
                                <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                    <h3 className="text-lg font-semibold text-white mb-4">
                                        Revenue by Product
                                    </h3>
                                    <div className="h-80">
                                        {stats?.topProducts && stats.topProducts.length > 0 ? (
                                            <TicketDistributionChart
                                                data={stats.topProducts.map((product, index) => ({
                                                    type: product.name,
                                                    count: product.revenue,
                                                    label: product.name,
                                                    color: `hsl(${(index * 72) % 360}, 70%, 50%)`,
                                                }))}
                                                title=""
                                            />
                                        ) : (
                                            <div className="h-full flex items-center justify-center text-amber-100/50">
                                                No product revenue data available
                                            </div>
                                        )}
                                    </div>
                                    {stats?.topProducts && stats.topProducts.length > 0 && (
                                        <div className="mt-4 space-y-3">
                                            {stats.topProducts.map((product, index) => (
                                                <div key={product.name} className="flex items-center text-sm">
                                                    <div
                                                        className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
                                                        style={{
                                                            backgroundColor: `hsl(${(index * 72) % 360}, 70%, 50%)`,
                                                        }}
                                                    />
                                                    <span className="text-amber-100 flex-1 truncate mr-2">
                                                        {product.name}
                                                    </span>
                                                    <span className="text-white font-medium whitespace-nowrap">
                                                        €
                                                        {product.revenue.toLocaleString(undefined, {
                                                            minimumFractionDigits: 2,
                                                            maximumFractionDigits: 2,
                                                        })}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </AdminLayout>
        </ProtectedRoute>
    );
};