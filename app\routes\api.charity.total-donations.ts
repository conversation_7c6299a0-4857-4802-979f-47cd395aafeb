import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

// Cache this data for 1 hour (3600 seconds)
// export const revalidate = 3600; // Remove Next.js revalidate

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { data, error } = await supabase
      .from('totaldonations')
      .select('total_amount')
      .single();

    if (error) throw error;

    return json({
      totalAmount: data?.total_amount || 0
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400'
      }
    });
  } catch (error) {
    console.error('Error fetching total donations:', error);
    return json(
      { error: 'Failed to fetch donation data' },
      { status: 500 }
    );
  }
}
