/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useRef, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';
import { toast } from 'react-toastify';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { elFormatter } from '@/lib/utils';
import PaymentForm from './PaymentForm';
import { earlyBirdDiscount } from '@/data/discountConfig';
import { useAnalytics } from '@/app/hooks/useAnalytics';

// Add the getStripePromise function
const getStripePromise = () => {
  if (typeof window !== 'undefined') {
    return loadStripe(window.ENV.STRIPE_PUBLISHABLE_KEY);
  }
  return null;
};

interface BookingModalProps {
  standNumber: number;
  onClose: () => void;
  onBookingSuccess: (standNumber: number) => void;
  updateStandAvailability: (standNumber: number) => void;
  bookedStands: number[];
  tableSize: string;
  price: number;
  isArtist: boolean;
}

type Step = 'select' | 'details' | 'payment' | 'confirmation';

interface Addon {
  id: string;
  name: string;
  description: string;
  price: number;
}

interface CartItem {
  addon: Addon;
  quantity: number;
}

const BookingModal: React.FC<BookingModalProps> = ({ standNumber, onClose, onBookingSuccess, updateStandAvailability, bookedStands, tableSize, price, isArtist }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [currentStep, setCurrentStep] = useState<Step>('select');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [stripeOptions, setStripeOptions] = useState<any>(null);
  const [formData, setFormData] = useState({
    businessName: '',
    contactName: '',
    email: '',
    phone: ''
  });
  const [cart, setCart] = useState<CartItem[]>([]);
  const [discountedPrice, setDiscountedPrice] = useState<number | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [stripePromise, setStripePromise] = useState<any>(null);

  // Analytics tracking
  const { trackVendorJourney } = useAnalytics();

  // Define available addons
  const addons: Addon[] = [
    {
      id: 'chair',
      name: 'Chair',
      description: 'Additional chair for your stand',
      price: 5
    },
    {
      id: 'table',
      name: 'Table',
      description: 'Additional table for your stand',
      price: 25
    },
    {
      id: 'electrical_outlet',
      name: 'Electrical Outlet',
      description: 'Electrical outlet access for your stand',
      price: 60
    }
  ];

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, () => { });

  // Add useEffect for stripe initialization
  useEffect(() => {
    const promise = getStripePromise();
    if (promise) {
      setStripePromise(promise);
    }
  }, []);

  // Track vendor stand selection on modal open
  useEffect(() => {
    trackVendorJourney.standSelected(standNumber, price, isArtist);
  }, [standNumber, price, isArtist, trackVendorJourney]);

  const checkStandAvailability = async (): Promise<boolean> => {
    try {
      setIsChecking(true);
      const response = await fetch(`/api/vendor/check-stand?standNumber=${standNumber}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to check stand availability');
      }

      if (bookedStands.includes(standNumber)) {
        toast.error('This stand has just been booked. Please select another stand.');
        onClose();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking stand availability:', error);
      toast.error('Failed to verify stand availability. Please try again.');
      return false;
    } finally {
      setIsChecking(false);
    }
  };

  // Calculate total price including addons
  const calculateTotal = () => {
    const basePrice = discountedPrice !== null ? discountedPrice : price;
    const addonTotal = cart.reduce((total, item) => total + (item.addon.price * item.quantity), 0);
    return basePrice + addonTotal;
  };

  // Update cart with addon
  const updateCart = (addon: Addon, quantity: number) => {
    if (quantity < 0) return;

    setCart(prev => {
      if (quantity === 0) {
        // Remove item if quantity is 0
        return prev.filter(item => item.addon.id !== addon.id);
      } else {
        const existingItem = prev.find(item => item.addon.id === addon.id);
        if (existingItem) {
          // Update quantity if item exists
          return prev.map(item =>
            item.addon.id === addon.id ? { ...item, quantity } : item
          );
        } else {
          // Add new item
          return [...prev, { addon, quantity }];
        }
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    if (!formData.businessName || !formData.contactName || !formData.email || !formData.phone) {
      toast.error('Please fill in all fields');
      return;
    }

    // Check email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    try {
      setIsSubmitting(true);

      // Track form completion
      trackVendorJourney.formCompleted(standNumber, formData.businessName);

      // Check stand availability before proceeding
      const isAvailable = await checkStandAvailability();
      if (!isAvailable) {
        updateStandAvailability(standNumber);
        return;
      }

      // Create payment intent with total price including addons
      const response = await fetch('/api/vendor/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: calculateTotal(),
          standNumber,
          businessName: formData.businessName,
          contactName: formData.contactName,
          email: formData.email,
          addons: cart.map(item => ({
            id: item.addon.id,
            name: item.addon.name,
            quantity: item.quantity,
            price: item.addon.price
          })),
          discountApplied: discountedPrice !== null,
          discountPercentage: discountedPrice !== null ? earlyBirdDiscount.discountPercentage : 0,
          originalPrice: price,
          price: discountedPrice !== null ? discountedPrice : price
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to initialize payment');
      }

      const { clientSecret } = await response.json();

      // Set Stripe options with the received client secret
      setStripeOptions({
        clientSecret,
        appearance: {
          theme: 'night',
          variables: {
            colorPrimary: '#ab8e56',
            colorBackground: '#1e1e1e',
            colorText: '#ffffff',
            colorDanger: '#ff5555',
            fontFamily: 'Arkhip, Arial, sans-serif',
            borderRadius: '8px',
          },
        },
      });

      // Track payment initiation
      trackVendorJourney.paymentInitiated(calculateTotal(), standNumber);

      setCurrentStep('payment');
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to initialize payment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentSuccess = async () => {
    try {
      // Check availability one more time before final booking
      const isAvailable = await checkStandAvailability();
      if (!isAvailable) {
        updateStandAvailability(standNumber);
        return;
      }

      const response = await fetch('/api/vendor/book-stand', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          standNumber,
          ...formData,
          addons: cart.map(item => ({
            id: item.addon.id,
            name: item.addon.name,
            quantity: item.quantity,
            price: item.addon.price
          })),
          totalPrice: calculateTotal(), // Make sure this is the correct total
          discountApplied: discountedPrice !== null,
          discountPercentage: discountedPrice !== null ? earlyBirdDiscount.discountPercentage : 0,
          originalPrice: price,
          price: discountedPrice !== null ? discountedPrice : price // Pass the correct base price
        }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to complete booking');
      }

      onBookingSuccess(standNumber);

      // Track successful booking
      trackVendorJourney.bookingConfirmed(standNumber, formData.businessName);

      setCurrentStep('confirmation');

      // Close the modal
      //onClose();

      //toast.success('Stand booked successfully!');
    } catch (error: any) {
      console.error('Frontend: Booking error:', error);
      toast.error(error.message || 'Failed to complete booking');

      // Track booking failure
      trackVendorJourney.paymentFailed(error.message || 'Failed to complete booking', standNumber);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const renderTableInfo = () => {
    if (price === 1350 || price === 1100) {
      return "Includes 2x 6ft Tables and 1x Chair";
    } else {
      return "Includes 1x 6ft Table and 1x Chair";
    }
  };

  const renderPriceDisplay = () => {
    if (discountedPrice !== null) {
      return (
        <div className="flex flex-col items-end">
          <div className="flex items-center">
            <p className="text-gray-400 line-through mr-2">€{elFormatter(price, 2)}</p>
            <p className="text-green-500 font-semibold">€{elFormatter(discountedPrice, 2)}</p>
          </div>
          <p className="text-xs text-green-500">
            {earlyBirdDiscount.discountPercentage}% Early Bird Discount
          </p>
        </div>
      );
    }
    return <p className="text-[var(--darkGold)] font-semibold">€{elFormatter(price, 2)}</p>;
  };

  const calculateTimeRemaining = () => {
    const now = new Date();
    const endTime = new Date(earlyBirdDiscount.endDate);
    const timeDiff = endTime.getTime() - now.getTime();

    if (timeDiff <= 0) return '';

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    return `${days}d ${hours}h ${minutes}m`;
  };

  useEffect(() => {
    // Check if early bird discount is enabled and not expired
    const now = new Date();
    const discountEndDate = new Date(earlyBirdDiscount.endDate);

    if (earlyBirdDiscount.enabled && now < discountEndDate) {
      // Calculate discounted price
      const discount = price * (earlyBirdDiscount.discountPercentage / 100);
      setDiscountedPrice(price - discount);

      // Set up countdown timer
      const timer = setInterval(() => {
        setTimeRemaining(calculateTimeRemaining());
      }, 60000); // Update every minute

      // Initial calculation
      setTimeRemaining(calculateTimeRemaining());

      return () => clearInterval(timer);
    } else {
      setDiscountedPrice(null);
    }
  }, [price]);

  const renderStep = () => {
    switch (currentStep) {
      case 'select':
        return (
          <div className="space-y-4">
            {discountedPrice !== null && (
              <div className="bg-green-900 border border-green-500 text-white p-3 rounded-lg mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-bold">Early Bird Discount!</h4>
                    <p className="text-sm">
                      Save {earlyBirdDiscount.discountPercentage}% on all stands
                    </p>
                  </div>
                  {timeRemaining && (
                    <div className="text-right">
                      <p className="text-xs">Offer ends in:</p>
                      <p className="font-mono font-bold">{timeRemaining}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
            <h3 className="text-xl font-semibold text-white mb-4">
              {isArtist ? 'Artist' : 'Stand'} #{standNumber} - {tableSize}
            </h3>

            <div className="p-4 border border-[var(--darkGold)] rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-lg font-medium text-white">Base Price</h4>
                {renderPriceDisplay()}
              </div>
              <p className="text-sm text-gray-400">{isArtist ? 'Artist' : 'Vendor'} space - {tableSize}</p>
              <p className="text-sm text-gray-400">{renderTableInfo()}</p>
            </div>

            <h4 className="text-lg font-medium text-[var(--darkGold)] mt-6">Available Add-ons</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {addons.map((addon, index) => (
                <div
                  key={`${addon.id}-${index}`}
                  className="flex flex-col justify-between p-3 border border-[var(--darkGold)] rounded-lg"
                >
                  <div className="text-center">
                    <h4 className="text-white font-semibold text-sm">{addon.name}</h4>
                    <p className="text-gray-400 text-xs">{addon.description}</p>
                    <p className="text-[var(--darkGold)] text-sm">
                      €{elFormatter(addon.price, 2)}
                    </p>
                  </div>
                  <div className="flex items-center justify-center space-x-2 mt-2">
                    <button
                      onClick={() => updateCart(
                        addon,
                        (cart.find(item => item.addon.id === addon.id)?.quantity || 0) - 1
                      )}
                      className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                    >
                      -
                    </button>
                    <span className="text-white text-sm">
                      {cart.find(item => item.addon.id === addon.id)?.quantity || 0}
                    </span>
                    <button
                      onClick={() => updateCart(
                        addon,
                        (cart.find(item => item.addon.id === addon.id)?.quantity || 0) + 1
                      )}
                      className="px-2 py-1 bg-[var(--darkGold)] rounded text-xs"
                    >
                      +
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Cart summary and continue button */}
            <div className="mt-6 border-t border-[var(--darkGold)] pt-4">
              <h4 className="text-lg font-medium text-white">Summary</h4>
              <ul className="space-y-2 mt-2">
                <li className="flex justify-between text-sm">
                  <span className="text-gray-300">
                    {isArtist ? 'Artist' : 'Stand'} #{standNumber} ({renderTableInfo()})
                  </span>
                  {discountedPrice !== null ? (
                    <div className="flex flex-col items-end">
                      <div className="flex items-center">
                        <p className="text-gray-400 line-through mr-2">€{elFormatter(price, 2)}</p>
                        <p className="text-green-500">€{elFormatter(discountedPrice, 2)}</p>
                      </div>
                    </div>
                  ) : (
                    <span className="text-[var(--darkGold)]">€{elFormatter(price, 2)}</span>
                  )}
                </li>

                {/* Add-ons */}
                {cart.map((item, index) => (
                  <li key={index} className="flex justify-between text-sm">
                    <span className="text-gray-300">{item.quantity}x {item.addon.name}</span>
                    <span className="text-[var(--darkGold)]">€{elFormatter(item.addon.price * item.quantity, 2)}</span>
                  </li>
                ))}

                {/* Total */}
                <li className="flex justify-between text-sm font-bold pt-2 border-t border-gray-700">
                  <span className="text-white">Total</span>
                  <span className="text-[var(--darkGold)]">€{elFormatter(calculateTotal(), 2)}</span>
                </li>

                {/* Discount info */}
                {discountedPrice !== null && (
                  <li className="text-xs text-green-500 text-right">
                    Early Bird Discount: {earlyBirdDiscount.discountPercentage}% off applied
                  </li>
                )}
              </ul>
              <div className="flex justify-center space-x-4 mt-6">
                <button
                  onClick={() => {
                    trackVendorJourney.formStarted(standNumber);
                    setCurrentStep('details');
                  }}
                  className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
                >
                  Continue to Details
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-white bg-gray-700 rounded-lg transition-colors w-full"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        );

      case 'details':
        return (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-[var(--darkGold)] mb-1">
                Business Name
              </label>
              <input
                type="text"
                id="businessName"
                name="businessName"
                value={formData.businessName}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-800 border border-[var(--darkGold)] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]"
              />
            </div>

            <div>
              <label htmlFor="contactName" className="block text-sm font-medium text-[var(--darkGold)] mb-1">
                Contact Name
              </label>
              <input
                type="text"
                id="contactName"
                name="contactName"
                value={formData.contactName}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-800 border border-[var(--darkGold)] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-[var(--darkGold)] mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-800 border border-[var(--darkGold)] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]"
              />
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-[var(--darkGold)] mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="w-full px-4 py-2 bg-gray-800 border border-[var(--darkGold)] rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[var(--darkGold)]"
              />
            </div>

            <div className="flex justify-center space-x-4 mt-6">
              <button
                type="submit"
                disabled={isSubmitting || isChecking}
                className={`w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {(isSubmitting || isChecking) ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </span>
                ) : (
                  'Proceed to Payment'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-white bg-gray-700 rounded-lg transition-colors w-full"
              >
                Cancel
              </button>
            </div>
          </form>
        );

      case 'payment':
        return stripeOptions && stripePromise ? (
          <div className="space-y-4">
            <div className="text-center mb-6">
              <p className="text-lg text-white">{isArtist ? 'Artist' : 'Stand'} #{standNumber}</p>
              <p className="text-2xl font-bold text-[var(--darkGold)]">€{calculateTotal()}</p>
              <div className="mt-2 text-sm text-gray-400">
                {discountedPrice !== null ? (
                  <>
                    <p>Original price: <span className="line-through">€{elFormatter(price, 2)}</span></p>
                    <p className="text-green-500">Early Bird Discount: {earlyBirdDiscount.discountPercentage}% off</p>
                    <p>Discounted price: €{elFormatter(discountedPrice, 2)}</p>
                  </>
                ) : (
                  <p>Base price: €{elFormatter(price, 2)}</p>
                )}
                {cart.length > 0 && (
                  <p>Add-ons: €{cart.reduce((sum, item) => sum + (item.addon.price * item.quantity), 0).toFixed(2)}</p>
                )}
              </div>
            </div>
            <Elements stripe={stripePromise} options={stripeOptions}>
              <PaymentForm
                onSuccess={handlePaymentSuccess}
                isProcessing={isSubmitting}
                isChecking={isChecking}
                setIsProcessing={setIsSubmitting}
                clientSecret={stripeOptions.clientSecret}
              />
            </Elements>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--darkGold)] mx-auto"></div>
            <div className="text-center mt-2">Loading payment form...</div>
          </div>
        );

      case 'confirmation':
        return (
          <div className="text-center space-y-4">
            <svg className="w-16 h-16 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <h3 className="text-2xl font-bold text-white">Booking Confirmed!</h3>
            <p className="text-gray-300">Thank you for booking {isArtist ? 'artist' : 'stand'} #{standNumber}</p>
            <p className="text-gray-300">You'll receive a confirmation email shortly. If it doesn't arrive within a few minutes, please check your spam or junk folder. Please note: Once booked, stands are non-refundable.</p>
            <button
              onClick={onClose}
              className="mt-6 px-6 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
            >
              Close
            </button>
          </div>
        );
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[100]"
      >
        <div className="fixed inset-0 flex items-center justify-center z-[100] p-4">
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-[#121212] border border-[var(--darkGold)] rounded-lg w-full max-w-2xl p-6 shadow-xl max-h-[80vh] flex flex-col"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-white text-center w-full">
                {currentStep === 'confirmation' ? 'Booking Confirmed' : `${isArtist ? 'Artist' : 'Stand'} Application`}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="overflow-hidden overflow-y-auto custom-scrollbar">
              {renderStep()}
            </div>
          </motion.div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
export default BookingModal;