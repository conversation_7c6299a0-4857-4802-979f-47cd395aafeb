'use client';

import { useMemo } from 'react';
import { ResponsiveAreaBump } from '@nivo/bump';
import { elFormatter } from '@/lib/utils';

interface DataPoint {
  date: string;
  value: number;
  revenue: number;
  name: string;
}

interface DonationTrendsChartProps {
  data: DataPoint[];
  height?: number;
  tickFormat?: (value: number) => string;
}

export function DonationTrendsChart({
  data,
  height = 360,
  tickFormat = (value) => `€${elFormatter(value, 0)}`,
}: DonationTrendsChartProps) {
  // Sort and format data for Nivo AreaBump
  const chartData = useMemo(() => {
    // Group by date and sum values
    const dailyTotals = data.reduce((acc, item) => {
      const date = new Date(item.date).toISOString().split('T')[0]; // YYYY-MM-DD
      acc[date] = (acc[date] || 0) + (item.value || 0);
      return acc;
    }, {} as Record<string, number>);

    // Convert to array and sort by date
    return Object.entries(dailyTotals)
      .map(([date, value]) => ({
        x: date,
        y: value,
        formattedDate: new Date(date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })
      }))
      .sort((a, b) => new Date(a.x).getTime() - new Date(b.x).getTime());
  }, [data]);

  if (chartData.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center text-amber-100/50">
        No donation data available for the selected period.
      </div>
    );
  }

  return (
    <div style={{ height }} className="w-full">
      <ResponsiveAreaBump
        data={[{
          id: 'donations',
          data: chartData.map((item, index) => ({
            x: item.x,
            y: item.y,
            formattedDate: item.formattedDate,
            formattedValue: tickFormat(item.y)
          }))
        }]}
        margin={{ top: 20, right: 30, bottom: 60, left: 60 }}
        spacing={8}
        colors={['#f59e0b']}
        blendMode="normal"
        startLabel={false}
        endLabel={false}
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: -45,
          legend: 'Date',
          legendPosition: 'middle',
          legendOffset: 40,
          format: (value) => {
            // Show only every 3rd tick to avoid crowding
            const index = chartData.findIndex(d => d.x === value);
            return index % 3 === 0 ? chartData[index]?.formattedDate : '';
          }
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          format: tickFormat,
          legend: 'Amount',
          legendOffset: -50,
          legendPosition: 'middle',
        }}
        gridYValues={5}
        theme={{
          textColor: '#9ca3af',
          fontSize: 12,
          axis: {
            domain: {
              line: {
                stroke: '#4b5563',
                strokeWidth: 1,
              },
            },
            ticks: {
              line: {
                stroke: '#4b5563',
                strokeWidth: 1,
              },
            },
          },
          grid: {
            line: {
              stroke: '#374151',
              strokeWidth: 1,
              strokeDasharray: '3 3',
            },
          },
          tooltip: {
            container: {
              background: '#1f2937',
              border: '1px solid #4b5563',
              borderRadius: '0.25rem',
              padding: '0.5rem',
            },
          },
        }}
      />
    </div>
  );
}
