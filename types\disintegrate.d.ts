declare module 'disintegrate' {
  interface DisintegrateOptions {
    elem: HTMLElement;
    animationDuration?: number;
    particleCount?: number;
    particles?: Array<{
      startColor?: string;
      endColor?: string;
      startOpacity?: number;
      endOpacity?: number;
      size?: number;
      radius?: number;
      spread?: number;
    }>;
    complete?: () => void;
  }

  interface DisintegrateInstance {
    disintegrate: () => void;
    destroy: () => void;
  }

  interface Disintegrate {
    init: () => void;
    createSimultaneousParticles: (options: DisintegrateOptions) => DisintegrateInstance;
  }

  const disintegrate: Disintegrate;
  export default disintegrate;
}