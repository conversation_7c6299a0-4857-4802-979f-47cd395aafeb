module.exports = {
    serverModuleFormat: "cjs",
    serverPlatform: "node",
    serverDependenciesToBundle: [/@stripe\/stripe-js/],
    future: {
        v2_dev: true,
        v2_errorBoundary: true,
        v2_headers: true,
        v2_meta: true,
        v2_normalizeFormMethod: true,
        v2_routeConvention: true,
    },
    publicPath: "/build/",
    serverBuildPath: "build/index.js",
    assetsBuildDirectory: "public/build",
    serverBuildDirectory: "build",
    devServerPort: 8002,
    ignoredRouteFiles: ["**/.*"],
    serverModuleFormat: "cjs",
    serverPlatform: "node",
    serverDependenciesToBundle: [/@stripe\/stripe-js/],
    // Add this section to expose environment variables
    env: {
        STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
        SUPABASE_URL: process.env.SUPABASE_URL,
        SUPABASE_KEY: process.env.SUPABASE_KEY,
        NODE_ENV: process.env.NODE_ENV,
        ADMIN_EMAIL: process.env.ADMIN_EMAIL,
        ADMIN_PASSWORD: process.env.ADMIN_PASSWORD,
        ADMIN_NAME: process.env.ADMIN_NAME,
        ADMIN_ROLE: process.env.ADMIN_ROLE,
        SESSION_SECRET: process.env.SESSION_SECRET,
    },
};