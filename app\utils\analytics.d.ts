// analytics.ts
declare module '@/app/utils/analytics' {
  /**
   * Tracks a page view in the analytics system
   * @param path The path of the page being viewed
   * @param metadata Optional metadata to include with the page view
   */
  export function trackPageView(
    path: string,
    metadata?: Record<string, any>
  ): Promise<void>;

  /**
   * Tracks a custom event in the analytics system
   * @param eventName The name of the event to track
   * @param eventData Additional data associated with the event
   */
  export function trackEvent(
    eventName: string,
    eventData?: Record<string, any>
  ): Promise<void>;

  /**
   * Gets the count of active users (users with events in the last X minutes)
   * @param minutes The number of minutes to look back for active users (default: 30)
   * @returns The number of active users
   */
  export function getActiveUsersCount(minutes?: number): Promise<number>;

  /**
   * Gets the conversion rate (percentage of sessions that resulted in a purchase)
   * @param days The number of days to look back for conversion data (default: 30)
   * @returns The conversion rate as a percentage (0-100)
   */
  export function getConversionRate(days?: number): Promise<number>;

  /**
   * Tracks a purchase event in the analytics system
   * @param orderId The ID of the order
   * @param amount The total amount of the purchase
   * @param items Array of items purchased
   */
  export function trackPurchase(
    orderId: string,
    amount: number,
    items: Array<{
      id: string;
      name: string;
      price: number;
      quantity: number;
    }>
  ): Promise<void>;

  /**
   * Tracks an error event in the analytics system
   * @param error The error that occurred
   * @param context Additional context about where the error occurred
   */
  export function trackError(
    error: Error,
    context?: Record<string, any>
  ): Promise<void>;

  /**
   * Tracks a user signup event
   * @param userId The ID of the user who signed up
   * @param method The signup method used (e.g., 'email', 'google', 'facebook')
   */
  export function trackSignup(
    userId: string,
    method: string
  ): Promise<void>;

  /**
   * Tracks a user login event
   * @param userId The ID of the user who logged in
   * @param method The login method used (e.g., 'email', 'google', 'facebook')
   */
  export function trackLogin(
    userId: string,
    method: string
  ): Promise<void>;

  /**
   * Tracks when a user adds an item to their cart
   * @param itemId The ID of the item added to cart
   * @param itemName The name of the item
   * @param price The price of the item
   * @param quantity The quantity added to cart
   */
  export function trackAddToCart(
    itemId: string,
    itemName: string,
    price: number,
    quantity: number
  ): Promise<void>;
}
