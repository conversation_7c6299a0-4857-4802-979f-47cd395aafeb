// Test utility to verify webhook functionality
// This is for development testing only

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function testPaymentStatusTracking() {
  console.log('Testing payment status tracking...');
  
  try {
    // Test 1: Check if Orders table has status field
    const { data: ordersSchema, error: ordersError } = await supabase
      .from('Orders')
      .select('*')
      .limit(1);
    
    if (ordersError) {
      console.error('Orders table error:', ordersError);
    } else {
      console.log('✅ Orders table accessible');
      if (ordersSchema && ordersSchema.length > 0) {
        const hasStatus = 'status' in ordersSchema[0];
        console.log(hasStatus ? '✅ Orders.status field exists' : '❌ Orders.status field missing');
      }
    }
    
    // Test 2: Check if VendorStands table has payment_status field
    const { data: vendorSchema, error: vendorError } = await supabase
      .from('VendorStands')
      .select('*')
      .limit(1);
    
    if (vendorError) {
      console.error('VendorStands table error:', vendorError);
    } else {
      console.log('✅ VendorStands table accessible');
      if (vendorSchema && vendorSchema.length > 0) {
        const hasPaymentStatus = 'payment_status' in vendorSchema[0];
        const hasStatus = 'status' in vendorSchema[0];
        console.log(hasPaymentStatus ? '✅ VendorStands.payment_status field exists' : '❌ VendorStands.payment_status field missing');
        console.log(hasStatus ? '✅ VendorStands.status field exists' : '❌ VendorStands.status field missing');
      }
    }
    
    // Test 3: Check if CharityDonations table has status field
    const { data: donationSchema, error: donationError } = await supabase
      .from('CharityDonations')
      .select('*')
      .limit(1);
    
    if (donationError) {
      console.error('CharityDonations table error:', donationError);
    } else {
      console.log('✅ CharityDonations table accessible');
      if (donationSchema && donationSchema.length > 0) {
        const hasStatus = 'status' in donationSchema[0];
        const hasPaymentIntentId = 'payment_intent_id' in donationSchema[0];
        console.log(hasStatus ? '✅ CharityDonations.status field exists' : '❌ CharityDonations.status field missing');
        console.log(hasPaymentIntentId ? '✅ CharityDonations.payment_intent_id field exists' : '❌ CharityDonations.payment_intent_id field missing');
      }
    }
    
    console.log('Payment status tracking test completed');
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

export async function testWebhookEndpoint() {
  console.log('Testing webhook endpoint...');
  
  try {
    // Test if the webhook endpoint is accessible
    const response = await fetch('/api/stripe-webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': 'test-signature'
      },
      body: JSON.stringify({
        type: 'test',
        data: { object: {} }
      })
    });
    
    console.log('Webhook endpoint response status:', response.status);
    
    if (response.status === 400) {
      console.log('✅ Webhook endpoint is accessible and properly validates signatures');
    } else {
      console.log('❓ Webhook endpoint response:', await response.text());
    }
    
  } catch (error) {
    console.error('Webhook endpoint test failed:', error);
  }
}

// Environment check
export function checkWebhookEnvironment() {
  console.log('Checking webhook environment...');
  
  const requiredEnvVars = [
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'SUPABASE_URL',
    'SUPABASE_KEY'
  ];
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar]);
  
  if (missing.length === 0) {
    console.log('✅ All required environment variables are set');
  } else {
    console.log('❌ Missing environment variables:', missing);
  }
  
  return missing.length === 0;
}
