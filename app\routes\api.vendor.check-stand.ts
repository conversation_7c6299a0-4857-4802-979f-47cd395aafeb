import { createClient } from '@supabase/supabase-js';
import { json, type LoaderFunctionArgs } from "@remix-run/node";

const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_KEY!
);

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const { searchParams } = new URL(request.url);
    const standNumber = searchParams.get('standNumber');

    if (!standNumber) {
      return json(
        { error: 'Stand number is required' },
        { status: 400 }
      );
    }

    const { data: existingBooking, error } = await supabase
      .from('VendorStands')
      .select('stand_number')
      .eq('stand_number', standNumber)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
      throw error;
    }

    return json({
      isAvailable: !existingBooking
    });

  } catch (error) {
    console.error('Error checking stand availability:', error);
    return json(
      { error: 'Failed to check stand availability' },
      { status: 500 }
    );
  }
}