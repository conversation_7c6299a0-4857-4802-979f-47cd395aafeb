import { createClient } from '@supabase/supabase-js';
import { env } from './env.client';

// This will be set to the client instance
let supabaseClient: ReturnType<typeof createClient> | null = null;

// Function to initialize the Supabase client
export const initSupabase = (url: string, key: string) => {
  if (!supabaseClient) {
    supabaseClient = createClient(url, key);
  }
  return supabaseClient;
};

// Function to get the Supabase client
export const getSupabaseClient = () => {
  if (typeof window === 'undefined') {
    console.log('[Supabase] Running on server, returning null');
    return null;
  }

  // Initialize if not already done
  if (!supabaseClient) {
    console.log('[Supabase] Initializing Supabase client');
    supabaseClient = createClient(
      env.supabase.url,
      env.supabase.key
    );
  }

  return supabaseClient;
};

// For server-side usage
export const getServerSupabaseClient = (url: string, key: string) => {
  if (!supabaseClient) {
    supabaseClient = createClient(url, key);
  }
  return supabaseClient;
};