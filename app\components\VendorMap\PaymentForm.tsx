/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';
import {
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';
import { useAnalytics } from '@/app/hooks/useAnalytics';

interface PaymentFormProps {
  onSuccess: () => void;
  isProcessing: boolean;
  setIsProcessing: (isProcessing: boolean) => void;
  isChecking: boolean;
  clientSecret?: string | null | undefined;
}

const PaymentForm: React.FC<PaymentFormProps> = ({
  onSuccess,
  isProcessing,
  setIsProcessing,
  isChecking,
  clientSecret
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');

  // Analytics tracking
  const { trackVendorJourney } = useAnalytics();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage('');

    if (!stripe || !elements || !clientSecret) {
      setErrorMessage('Payment system not initialized');
      return;
    }

    try {
      setIsProcessing(true);

      // Track payment processing
      trackVendorJourney.paymentProcessing(clientSecret);

      const { error: submitError } = await elements.submit();
      if (submitError) {
        throw submitError;
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret,
        confirmParams: {
          return_url: `${window.location.origin}/payment-confirmation`
        },
        redirect: 'if_required',
      });

      if (error) {
        throw error;
      }

      if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Track successful payment
        trackVendorJourney.paymentCompleted(paymentIntent.amount / 100, paymentIntent.id);
        onSuccess();
      } else {
        throw new Error('Payment failed. Please try again.');
      }

    } catch (err: any) {
      console.error('Payment error:', err);
      setErrorMessage(err.message || 'Payment failed');
      setIsProcessing(false);

      // Track payment failure
      trackVendorJourney.paymentFailed(err.message || 'Payment failed', clientSecret);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <PaymentElement
        id="payment-element"
        options={{
          layout: 'accordion',
          paymentMethodOrder: ['card'],
          defaultValues: {
            billingDetails: {
              email: ''
            }
          }
        }}
        onReady={() => {
          console.log('Payment element is ready');
          setIsLoading(false);
        }}
        onChange={(data: any) => {
          console.log('Payment element changed:', data);
        }}
        onLoaderStart={() => console.log('Payment element loader started')}
      />

      {errorMessage && (
        <div className="text-red-500 text-sm">{errorMessage}</div>
      )}

      <button
        type="submit"
        disabled={isProcessing || isLoading || isChecking || !stripe || !elements || !clientSecret}
        className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg disabled:opacity-50"
      >
        {isProcessing ? 'Processing...' : (isLoading || !stripe || !elements) ? 'Loading...' : 'Pay Now'}
      </button>
    </form>
  );
};
export default PaymentForm;
