import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAdminAuth } from '@/app/context/AdminAuthContext';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { CheckCircle, XCircle, Lock, Mail, User as UserIcon, Eye, EyeOff, Key } from 'lucide-react';
import { showLoadingToast, updateToastToSuccess, updateToastToError } from '@/app/utils/toast';

// Form validation schemas
const profileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
});

const passwordSchema = z.object({
  currentPassword: z.string().min(8, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Please confirm your new password'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type ProfileFormValues = z.infer<typeof profileSchema>;
type PasswordFormValues = z.infer<typeof passwordSchema>;

export default function AdminProfile() {
  const { user, updateProfile, updatePassword } = useAdminAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'security'>('profile');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [status, setStatus] = useState<{ type: 'success' | 'error'; message: string } | null>(null);

  // Profile form
  const {
    register: registerProfile,
    handleSubmit: handleProfileSubmit,
    reset: resetProfile,
    formState: { errors: profileErrors, isSubmitting: isProfileSubmitting },
  } = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
    },
  });

  // Password form
  const {
    register: registerPassword,
    handleSubmit: handlePasswordSubmit,
    reset: resetPassword,
    formState: { errors: passwordErrors, isSubmitting: isPasswordSubmitting },
  } = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
  });

  // Reset forms when user data changes
  useEffect(() => {
    if (user) {
      resetProfile({
        name: user.name || '',
        email: user.email || '',
      });
    }
  }, [user, resetProfile]);

  const onProfileSubmit = async (data: ProfileFormValues, event?: React.BaseSyntheticEvent) => {
    // Prevent default form submission
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    setStatus(null);
    const toastId = showLoadingToast('Updating profile...');

    try {
      await updateProfile(data.name);
      setStatus({
        type: 'success',
        message: 'Profile updated successfully!',
      });
      updateToastToSuccess(toastId, 'Profile updated successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update profile';
      setStatus({
        type: 'error',
        message: errorMessage,
      });
      updateToastToError(toastId, errorMessage);
    }
  };

  const onPasswordSubmit = async (data: PasswordFormValues, event?: React.BaseSyntheticEvent) => {
    // Prevent default form submission
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    setStatus(null);
    const toastId = showLoadingToast('Updating password...');

    try {
      await updatePassword(data.currentPassword, data.newPassword);
      setStatus({
        type: 'success',
        message: 'Password updated successfully!',
      });
      updateToastToSuccess(toastId, 'Password updated successfully!');
      resetPassword();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update password';
      setStatus({
        type: 'error',
        message: errorMessage,
      });
      updateToastToError(toastId, errorMessage);
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout>
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-amber-100">My Profile</h1>
            <p className="mt-1 text-sm text-amber-100/70">
              Manage your account settings and preferences
            </p>
          </div>

          {/* Status Message */}
          {status && (
            <div
              className={`rounded-md p-4 border ${
                status.type === 'success'
                  ? 'border-green-500/30 bg-green-900/20 text-green-200'
                  : 'border-red-500/30 bg-red-900/20 text-red-200'
              }`}
            >
              <div className="flex">
                <div className="flex-shrink-0">
                  {status.type === 'success' ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <XCircle className="h-5 w-5" />
                  )}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium">{status.message}</p>
                </div>
              </div>
            </div>
          )}

          {/* Tabs */}
          <div className="border-b border-amber-600/30">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('profile')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-amber-400 text-amber-100'
                    : 'border-transparent text-amber-100/70 hover:text-amber-100 hover:border-amber-400/50'
                }`}
              >
                <div className="flex items-center">
                  <UserIcon className="w-4 h-4 mr-2" />
                  Profile Information
                </div>
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'security'
                    ? 'border-amber-400 text-amber-100'
                    : 'border-transparent text-amber-100/70 hover:text-amber-100 hover:border-amber-400/50'
                }`}
              >
                <div className="flex items-center">
                  <Lock className="w-4 h-4 mr-2" />
                  Security
                </div>
              </button>
            </nav>
          </div>

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleProfileSubmit(onProfileSubmit)(e);
              }}
              className="space-y-6"
            >
              <div className="bg-zinc-800 shadow overflow-hidden sm:rounded-lg border border-amber-600/30">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-amber-100">
                    Profile Information
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-amber-100/70">
                    Update your account's profile information and email address.
                  </p>
                </div>
                <div className="border-t border-amber-600/30 px-4 py-5 sm:p-0">
                  <div className="space-y-6 p-6">
                    <div className="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
                      <label htmlFor="name" className="block text-sm font-medium text-amber-100/90 sm:mt-px sm:pt-2">
                        Name
                      </label>
                      <div className="mt-1 sm:mt-0 sm:col-span-2">
                        <div className="max-w-lg flex rounded-md shadow-sm">
                          <div className="relative flex items-stretch flex-grow focus-within:z-10">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <UserIcon className="h-5 w-5 text-amber-400/80" aria-hidden="true" />
                            </div>
                            <input
                              type="text"
                              id="name"
                              {...registerProfile('name')}
                              className="focus:ring-amber-500 focus:border-amber-500 block w-full h-10 rounded-md pl-10 sm:text-sm border-amber-600/50 bg-zinc-700/50 text-amber-100 placeholder-amber-100/30"
                              placeholder="Your name"
                            />
                          </div>
                        </div>
                        {profileErrors.name && (
                          <p className="mt-2 text-sm text-red-400">
                            {profileErrors.name.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
                      <label htmlFor="email" className="block text-sm font-medium text-amber-100/90 sm:mt-px sm:pt-2">
                        Email
                      </label>
                      <div className="mt-1 sm:mt-0 sm:col-span-2">
                        <div className="max-w-lg flex rounded-md shadow-sm">
                          <div className="relative flex items-stretch flex-grow focus-within:z-10">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <Mail className="h-5 w-5 text-amber-400/80" aria-hidden="true" />
                            </div>
                            <input
                              type="email"
                              id="email"
                              disabled
                              {...registerProfile('email')}
                              className="block w-full h-10 rounded-md pl-10 sm:text-sm border-amber-600/50 bg-zinc-700/30 text-amber-100/70 cursor-not-allowed"
                            />
                          </div>
                        </div>
                        <p className="mt-2 text-sm text-amber-100/50">
                          Contact support to change your email address.
                        </p>
                      </div>
                    </div>

                    <div className="pt-2">
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isProfileSubmitting}
                          className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-amber-900 bg-amber-400 hover:bg-amber-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {isProfileSubmitting ? 'Saving...' : 'Save'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handlePasswordSubmit(onPasswordSubmit)(e);
              }}
              className="space-y-6"
            >
              <div className="bg-zinc-800 shadow overflow-hidden sm:rounded-lg border border-amber-600/30">
                <div className="px-4 py-5 sm:px-6">
                  <h3 className="text-lg leading-6 font-medium text-amber-100">
                    Update Password
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-amber-100/70">
                    Ensure your account is using a long, random password to stay secure.
                  </p>
                </div>
                <div className="border-t border-amber-600/30 px-4 py-5 sm:p-0">
                  <div className="space-y-6 p-6">
                    <div className="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
                      <label htmlFor="current-password" className="block text-sm font-medium text-amber-100/90 sm:mt-px sm:pt-2">
                        Current Password
                      </label>
                      <div className="mt-1 sm:mt-0 sm:col-span-2">
                        <div className="max-w-lg flex rounded-md shadow-sm">
                          <div className="relative flex items-stretch flex-grow focus-within:z-10">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <Lock className="h-5 w-5 text-amber-400/80" aria-hidden="true" />
                            </div>
                            <input
                              id="current-password"
                              type={showCurrentPassword ? 'text' : 'password'}
                              autoComplete="current-password"
                              {...registerPassword('currentPassword')}
                              className="focus:ring-amber-500 focus:border-amber-500 block w-full h-10 rounded-md pl-10 sm:text-sm border-amber-600/50 bg-zinc-700/50 text-amber-100 placeholder-amber-100/30"
                              placeholder="Enter current password"
                            />
                            <button
                              type="button"
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                              onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                            >
                              {showCurrentPassword ? (
                                <EyeOff className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              ) : (
                                <Eye className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              )}
                            </button>
                          </div>
                        </div>
                        {passwordErrors.currentPassword && (
                          <p className="mt-2 text-sm text-red-400">
                            {passwordErrors.currentPassword.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
                      <label htmlFor="new-password" className="block text-sm font-medium text-amber-100/90 sm:mt-px sm:pt-2">
                        New Password
                      </label>
                      <div className="mt-1 sm:mt-0 sm:col-span-2">
                        <div className="max-w-lg flex rounded-md shadow-sm">
                          <div className="relative flex items-stretch flex-grow focus-within:z-10">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <Key className="h-5 w-5 text-amber-400/80" aria-hidden="true" />
                            </div>
                            <input
                              id="new-password"
                              type={showNewPassword ? 'text' : 'password'}
                              autoComplete="new-password"
                              {...registerPassword('newPassword')}
                              className="focus:ring-amber-500 focus:border-amber-500 block w-full h-10 rounded-md pl-10 sm:text-sm border-amber-600/50 bg-zinc-700/50 text-amber-100 placeholder-amber-100/30"
                              placeholder="Enter new password"
                            />
                            <button
                              type="button"
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                              onClick={() => setShowNewPassword(!showNewPassword)}
                            >
                              {showNewPassword ? (
                                <EyeOff className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              ) : (
                                <Eye className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              )}
                            </button>
                          </div>
                        </div>
                        {passwordErrors.newPassword && (
                          <p className="mt-2 text-sm text-red-400">
                            {passwordErrors.newPassword.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start">
                      <label htmlFor="confirm-password" className="block text-sm font-medium text-amber-100/90 sm:mt-px sm:pt-2">
                        Confirm New Password
                      </label>
                      <div className="mt-1 sm:mt-0 sm:col-span-2">
                        <div className="max-w-lg flex rounded-md shadow-sm">
                          <div className="relative flex items-stretch flex-grow focus-within:z-10">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <Key className="h-5 w-5 text-amber-400/80" aria-hidden="true" />
                            </div>
                            <input
                              id="confirm-password"
                              type={showConfirmPassword ? 'text' : 'password'}
                              autoComplete="new-password"
                              {...registerPassword('confirmPassword')}
                              className="focus:ring-amber-500 focus:border-amber-500 block w-full h-10 rounded-md pl-10 sm:text-sm border-amber-600/50 bg-zinc-700/50 text-amber-100 placeholder-amber-100/30"
                              placeholder="Confirm new password"
                            />
                            <button
                              type="button"
                              className="absolute inset-y-0 right-0 pr-3 flex items-center"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? (
                                <EyeOff className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              ) : (
                                <Eye className="h-5 w-5 text-amber-400/70 hover:text-amber-300" aria-hidden="true" />
                              )}
                            </button>
                          </div>
                        </div>
                        {passwordErrors.confirmPassword && (
                          <p className="mt-2 text-sm text-red-400">
                            {passwordErrors.confirmPassword.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="pt-2">
                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isPasswordSubmitting}
                          className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-amber-900 bg-amber-400 hover:bg-amber-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {isPasswordSubmitting ? 'Updating...' : 'Update Password'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          )}

          {/* Session Management */}
          <div className="bg-zinc-800 shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                Active Sessions
              </h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
                This is a list of devices that have logged into your account. Revoke any sessions that you
                don't recognize.
              </p>

              <div className="mt-6">
                <div className="bg-zinc-700 p-4 rounded-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="p-2 rounded-full bg-zinc-600">
                        <svg
                          className="w-6 h-6 text-gray-500 dark:text-gray-300"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Windows 10 • Chrome
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date().toLocaleString()} • Current session
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      className="text-sm font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300"
                      disabled
                    >
                      Logout
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};