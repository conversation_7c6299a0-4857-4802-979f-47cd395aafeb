.pulsingIndicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  z-index: 10;
}

@media (max-width: 768px) {
  .pulsingIndicator {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 479px) {
  .pulsingIndicator {
    width: 5px;
    height: 5px;
  }
}

.available {
  background: rgba(12, 203, 95, 0.8);
  box-shadow: 0 0 0 rgba(12, 203, 95, 0.6);
  animation: pulse 2s infinite;
}

.unavailable {
  background: rgba(255, 0, 0, 0.8);
  box-shadow: 0 0 0 rgba(255, 0, 0, 0.6);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(12, 203, 95, 0.6);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(12, 203, 95, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(12, 203, 95, 0);
  }
}
