import { useMemo, useState } from 'react';
import { ResponsiveLine } from '@nivo/line';

type UserActivityData = {
  labels: string[];
  activeUsers: number[];
  newUsers: number[];
  sessions?: number[];
  returningUsers?: number[];
};

interface UserActivityChartProps {
  data: UserActivityData;
  height?: number;
  activeColor?: string;
  newColor?: string;
  returningColor?: string;
  sessionsColor?: string;
}

export function UserActivityChart({
  data,
  height = 300,
  activeColor = '#f59e0b', // amber-500
  newColor = '#3b82f6',    // blue-500
  returningColor = '#8b5cf6', // purple-500
  sessionsColor = '#10b981' // emerald-500
}: UserActivityChartProps) {
  const [stacked, setStacked] = useState(false);
  const [visibleSeries, setVisibleSeries] = useState({
    activeUsers: true,
    newUsers: true,
    returningUsers: true,
    sessions: false
  });

  // Always calculate these values - don't conditionally compute them
  const processedData = useMemo(() => {
    if (!data || !data.labels) {
      return {
        labels: [],
        activeUsers: [],
        newUsers: [],
        returningUsers: [],
        sessions: [],
        hasReturningData: false,
        hasSessionsData: false
      };
    }

    const { labels = [], activeUsers = [], newUsers = [], returningUsers = [], sessions = [] } = data;
    const hasReturningData = returningUsers.length > 0 && returningUsers.some(val => val > 0);
    const hasSessionsData = sessions.length > 0 && sessions.some(val => val > 0);

    return {
      labels,
      activeUsers,
      newUsers,
      returningUsers,
      sessions,
      hasReturningData,
      hasSessionsData
    };
  }, [data]);

  const { labels, activeUsers, newUsers, returningUsers, sessions, hasReturningData, hasSessionsData } = processedData;

  // Format data for Nivo Line chart - always called, but returns empty array if no data
  const chartData = useMemo(() => {
    // Always return an array, even if empty
    if (!labels || labels.length === 0) {
      return [];
    }

    const series = [];

    if (visibleSeries.activeUsers) {
      series.push({
        id: 'activeUsers',
        label: 'Active Users',
        color: activeColor,
        data: labels.map((label, i) => ({
          x: label,
          y: activeUsers[i] || 0,
        })),
      });
    }

    if (visibleSeries.newUsers) {
      series.push({
        id: 'newUsers',
        label: 'New Users',
        color: newColor,
        data: labels.map((label, i) => ({
          x: label,
          y: newUsers[i] || 0,
        })),
      });
    }

    // Always check for data existence, but don't make the useMemo call conditional
    if (hasReturningData && visibleSeries.returningUsers) {
      series.push({
        id: 'returningUsers',
        label: 'Returning Users',
        color: returningColor,
        data: labels.map((label, i) => ({
          x: label,
          y: returningUsers?.[i] || 0,
        })),
      });
    }

    if (hasSessionsData && visibleSeries.sessions) {
      series.push({
        id: 'sessions',
        label: 'Sessions',
        color: sessionsColor || '#10b981', // emerald-500
        data: labels.map((label, i) => ({
          x: label,
          y: sessions[i] || 0,
        })),
      });
    }

    return series;
  }, [
    labels,
    activeUsers,
    newUsers,
    returningUsers,
    sessions,
    visibleSeries,
    activeColor,
    newColor,
    returningColor,
    sessionsColor,
    hasReturningData,
    hasSessionsData
  ]);

  // Check if we have data to display
  const hasData = labels && labels.length > 0;

  if (!hasData) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-400">No activity data available</p>
      </div>
    );
  }

  // Custom tooltip
  const CustomTooltip = ({ point }: any) => {
    const { data, serieId } = point;
    let label = '';

    switch (serieId) {
      case 'activeUsers':
        label = 'Active Users';
        break;
      case 'newUsers':
        label = 'New Users';
        break;
      case 'returningUsers':
        label = 'Returning Users';
        break;
      case 'sessions':
        label = 'Sessions';
        break;
      default:
        label = serieId;
    }

    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-md p-2 text-sm">
        <div className="font-medium text-white">{data.xFormatted}</div>
        <div className="flex items-center mt-1">
          <div
            className="w-3 h-3 rounded-sm mr-2"
            style={{
              backgroundColor: point.serieColor,
              opacity: 0.8
            }}
          />
          <span className="text-gray-300">
            {label}: {data.yFormatted}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="relative" style={{ height: `${height}px` }}>
      <div style={{ height: '100%' }}>
        <ResponsiveLine
          data={chartData}
          margin={{ top: 20, right: 20, bottom: 50, left: 50 }}
          xScale={{
            type: 'point',
          }}
          yScale={{
            type: 'linear',
            min: 0,
            max: stacked ? 'auto' : undefined,
            stacked: stacked,
            nice: true,
          }}
          curve="monotoneX"
          axisTop={null}
          axisRight={null}
          axisBottom={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: labels.length > 10 ? 45 : 0,
            legend: 'Date',
            legendOffset: 36,
            legendPosition: 'middle',
          }}
          axisLeft={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: 0,
            legend: 'Users',
            legendOffset: -40,
            legendPosition: 'middle',
            format: (value) => {
              if (value >= 1000) return `${(value / 1000).toFixed(1)}k`;
              return value.toString();
            }
          }}
          colors={({ id }) => {
            if (id === 'activeUsers') return activeColor;
            if (id === 'newUsers') return newColor;
            if (id === 'returningUsers') return returningColor;
            if (id === 'sessions') return sessionsColor;
            return '#cccccc';
          }}
          lineWidth={2}
          pointSize={6}
          pointColor={{ theme: 'background' }}
          pointBorderWidth={2}
          pointBorderColor={{ from: 'serieColor' }}
          pointLabelYOffset={-12}
          enableArea={true}
          areaOpacity={0.15}
          areaBaselineValue={0}
          enableGridX={false}
          enableGridY={true}
          enablePoints={labels.length <= 30}
          useMesh={true}
          theme={{
            textColor: '#9ca3af',
            fontSize: 12,
            axis: {
              domain: {
                line: {
                  stroke: '#374151',
                  strokeWidth: 1,
                },
              },
              ticks: {
                line: {
                  stroke: '#374151',
                  strokeWidth: 1,
                },
              },
            },
            grid: {
              line: {
                stroke: '#374151',
                strokeWidth: 0.5,
                strokeDasharray: '2 2',
              },
            },
            tooltip: {
              container: {
                background: 'transparent',
                padding: 0,
                boxShadow: 'none',
              },
            },
          }}
          tooltip={CustomTooltip}
          legends={[
            {
              anchor: 'bottom-right',
              direction: 'column',
              justify: false,
              translateX: 0,
              translateY: 0,
              itemsSpacing: 0,
              itemDirection: 'left-to-right',
              itemWidth: 120,
              itemHeight: 20,
              itemOpacity: 0.85,
              symbolSize: 12,
              symbolShape: 'circle',
              symbolBorderColor: 'rgba(0, 0, 0, .5)',
              onClick: (d) => {
                setVisibleSeries(prev => ({
                  ...prev,
                  [d.id]: !prev[d.id as keyof typeof prev]
                }));
              },
              effects: [
                {
                  on: 'hover',
                  style: {
                    itemOpacity: 1
                  }
                }
              ]
            }
          ]}
          motionConfig="gentle"
        />
        <div className="flex items-center justify-between mt-2">
          <label className="inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              className="sr-only peer"
              checked={stacked}
              onChange={() => setStacked(!stacked)}
            />
            <div className="relative w-11 h-6 bg-zinc-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-amber-500"></div>
            <span className="ms-2 text-sm font-medium text-gray-300">Stacked</span>
          </label>
        </div>

        {/* Legend */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-center space-x-4 mt-2">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-amber-500 mr-1"></div>
            <span className="text-xs text-gray-400">Active</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
            <span className="text-xs text-gray-400">New</span>
          </div>
          {hasReturningData && (
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-purple-500 mr-1"></div>
              <span className="text-xs text-gray-400">Returning</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};