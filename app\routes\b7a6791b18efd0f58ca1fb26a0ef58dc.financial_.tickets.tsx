import { useState, useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchDashboardMetrics } from '@/app/store/slices/adminSlice';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { TicketDistributionChart } from '@/app/components/admin/charts/TicketDistributionChart';
import { RevenueChart } from '@/app/components/admin/charts/RevenueChart';
import { elFormatter } from '@/lib/utils';

// Ticket types and their display names
const TICKET_TYPES = {
    'adult-1day-saturday': 'Adult (Saturday)',
    'adult-1day-sunday': 'Adult (Sunday)',
    'child-1day-saturday': 'Child (Saturday)',
    'child-1day-sunday': 'Child (Sunday)',
    'family-1day-saturday': 'Family (Saturday)',
    'family-1day-sunday': 'Family (Sunday)',
    'adult-2day': 'Adult (Weekend)',
    'child-2day': 'Child (Weekend)',
    'santa-option-saturday': 'Santa Slot (Saturday)',
    'santa-option-sunday': 'Santa Slot (Sunday)'
} as const;

// Initial availability from the API
const INITIAL_AVAILABILITY = {
    'adult-1day-saturday': 1900,
    'adult-1day-sunday': 1900,
    'child-1day-saturday': 1300,
    'child-1day-sunday': 1300,
    'family-1day-saturday': 350,
    'family-1day-sunday': 350,
    'adult-2day': 3000,
    'child-2day': 2500,
    'santa-option-saturday': 162,
    'santa-option-sunday': 162
} as const;

interface TicketSale {
    ticket_id: keyof typeof TICKET_TYPES;
    quantity: number;
    total_amount: number;
    created_at: string;
}

interface TicketStats {
    totalTickets: number;
    ticketsSold: number;
    totalRevenue: number;
    conversionRate: number;
    popularTickets: Array<{
        name: string;
        count: number;
        revenue?: number;
    }>;
}

interface DashboardMetrics {
    totalRevenue: number;
    ticketsSold: number;
    activeUsers: number;
    conversionRate: number;
    uniqueCustomers: number;
    revenueByDay: Array<{ date: string; revenue: number }>;
    ticketsByType: Array<{
        type: keyof typeof TICKET_TYPES;
        count: number;
        revenue: number;
        available: number;
    }>;
    revenueByProductType: Array<{
        name: keyof typeof TICKET_TYPES;
        revenue: number;
        sold: number;
        available: number;
    }>;
    revenueByCategory: Array<{
        category: string;
        revenue: number;
        percentage: number;
        tickets: Array<{
            id: keyof typeof TICKET_TYPES;
            name: string;
            sold: number;
            available: number;
            revenue: number;
        }>;
    }>;
    averageOrderValue: number;
    dateRange: {
        start: string;
        end: string;
    };
}

export default function TicketsDashboard() {
    const dispatch = useAppDispatch();
    const { data: metrics, loading, error } = useAppSelector((state) => ({
        data: state.admin.metrics.data as DashboardMetrics | null,
        loading: state.admin.metrics.loading,
        error: state.admin.metrics.error
    }));

    const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
    const [selectedCategory, setSelectedCategory] = useState<string>('all');
    const navigate = useNavigate();

    // Fetch data when component mounts or time range changes
    useEffect(() => {
        dispatch(fetchDashboardMetrics({ timeRange }));
    }, [dispatch, timeRange]);

    // Get unique categories from revenueByCategory
    const categories = metrics?.revenueByCategory?.map(cat => ({
        id: cat.category.toLowerCase().replace(/\s+/g, '-'),
        name: cat.category
    })) || [];

    // Add 'All' category
    const allCategories = [
        { id: 'all', name: 'All Ticket Types' },
        ...categories
    ];

    // Process ticket data with proper availability from INITIAL_AVAILABILITY
    const ticketTypes = Object.entries(TICKET_TYPES).map(([ticketId, ticketName]) => {
        // Find ticket in metrics if it exists
        const ticketData = metrics?.ticketsByType?.find((t) => t.type === ticketId) || {
            count: 0,
            revenue: 0
        };

        // Get initial availability from INITIAL_AVAILABILITY
        const initialAvailable = INITIAL_AVAILABILITY[ticketId as keyof typeof INITIAL_AVAILABILITY] || 0;
        const sold = ticketData.count || 0;
        const available = Math.max(0, initialAvailable - sold);
        const price = sold > 0 ? ticketData.revenue / sold : 0;

        return {
            id: ticketId,
            name: ticketName,
            price: price,
            sold: sold,
            available: available,
            totalAvailable: initialAvailable,
            revenue: ticketData.revenue || 0
        };
    });

    // Filter tickets by selected category if needed
    const displayedTickets = selectedCategory === 'all'
        ? ticketTypes
        : ticketTypes.filter(ticket => {
            const type = ticket.id.split('-')[0]; // Get base type (adult, child, etc.)
            return type === selectedCategory.split('-')[0];
        });

    // Calculate summary stats
    const totalSold = displayedTickets.reduce((sum, ticket) => sum + ticket.sold, 0);
    const totalAvailable = displayedTickets.reduce((sum, ticket) => sum + ticket.available, 0);
    const totalCapacity = displayedTickets.reduce((sum, ticket) => sum + ticket.totalAvailable, 0);
    const totalRevenue = metrics?.totalRevenue || 0;
    const conversionRate = totalCapacity > 0 ? Math.round((totalSold / totalCapacity) * 100) : 0;

    // Format data for charts
    const salesData = metrics?.revenueByDay?.map(day => ({
        date: day.date,
        revenue: day.revenue
    })) || [];

    // Prepare chart data
    const chartData = displayedTickets.map(ticket => ({
        type: ticket.id,
        count: ticket.sold,
        label: ticket.name,
        revenue: ticket.revenue
    }));

    return (
        <ProtectedRoute>
            <AdminLayout>
                <div className="py-6">
                    <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-2">
                        <h1 className="text-2xl font-bold text-white">Ticket Sales & Revenue</h1>
                        <p className="text-amber-100/70">Track and manage ticket sales and inventory</p>
                    </div>
                    <div className="flex space-x-2 justify-center">
                        <select
                            value={timeRange}
                            onChange={(e) => setTimeRange(e.target.value as 'week' | 'month' | 'year')}
                            className="bg-zinc-700 border border-amber-600/50 text-amber-100 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
                        >
                            <option value="week">Last 7 Days</option>
                            <option value="month">Last 30 Days</option>
                            <option value="year">Last 12 Months</option>
                        </select>
                    </div>
                </div>

                {error ? (
                    <div className="bg-red-900/30 border border-red-500/50 text-red-100 p-4 rounded-lg">
                        <p className="font-semibold">Error loading ticket data</p>
                        <p className="text-sm mt-1">{error}</p>
                        <button
                            onClick={() => dispatch(fetchDashboardMetrics({ arg: timeRange }))}
                            className="mt-2 px-3 py-1 bg-red-600/50 hover:bg-red-600/70 text-white rounded text-sm"
                        >
                            Retry
                        </button>
                    </div>
                ) : loading || !metrics ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
                    </div>
                ) : (
                    <>
                        {/* Stats Overview */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <p className="text-sm text-amber-100/70">Tickets Sold</p>
                                <p className="text-2xl font-bold text-white">{elFormatter(totalSold, 0)}</p>
                                <p className="text-xs text-amber-400 mt-1">out of {elFormatter((totalSold + totalAvailable), 0)}</p>
                            </div>
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <p className="text-sm text-amber-100/70">Available</p>
                                <p className="text-2xl font-bold text-white">{elFormatter(totalAvailable, 0)}</p>
                                <p className="text-xs text-amber-400 mt-1">tickets remaining</p>
                            </div>
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <p className="text-sm text-amber-100/70">Total Revenue</p>
                                <p className="text-2xl font-bold text-white">€{elFormatter(totalRevenue, 0)}</p>
                                <p className="text-xs text-amber-400 mt-1">from ticket sales</p>
                            </div>
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                                <p className="text-sm text-amber-100/70">Conversion Rate</p>
                                <p className="text-2xl font-bold text-white">{conversionRate}%</p>
                                <p className="text-xs text-amber-400 mt-1">tickets sold vs available</p>
                            </div>
                        </div>

                        {/* Sales Chart */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30 flex flex-col h-[400px]">
                                <h3 className="text-lg font-semibold text-white mb-4">Revenue Over Time</h3>
                                <div className="flex-1 min-h-0 relative">
                                    <div className="absolute inset-0">
                                        <RevenueChart
                                            data={salesData}
                                            title=""
                                        tickFormat={(value) => `€${elFormatter(value, 0)}`}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30 flex flex-col h-[400px]">
                                <h3 className="text-lg font-semibold text-white mb-4">Ticket Sales Distribution</h3>
                                <div className="flex-1 min-h-0">
                                    <div className="h-full">
                                        <TicketDistributionChart
                                            data={chartData}
                                            title=""
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Ticket Inventory */}
                        <div className="bg-zinc-800 rounded-lg p-4 border border-amber-600/30">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-semibold text-white">Ticket Inventory</h3>
                                <button
                                    onClick={() => navigate("/b7a6791b18efd0f58ca1fb26a0ef58dc/tickets/manage")}
                                    className="text-amber-400 hover:text-amber-300 text-sm font-medium"
                                >
                                    Manage Tickets
                                </button>
                            </div>

                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-amber-600/30">
                                    <thead>
                                        <tr>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                Ticket Type
                                            </th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                Price
                                            </th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                Available
                                            </th>
                                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                Sold
                                            </th>
                                            <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-amber-100/70 uppercase tracking-wider">
                                                Revenue
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-amber-600/30">
                                        {ticketTypes.map((ticket: any) => (
                                            <tr key={ticket.id} className="hover:bg-zinc-700/50">
                                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-white">
                                                    {ticket.name}
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-amber-100">
                                                    €{elFormatter(ticket.price, 2)}
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-amber-100">
                                                    <span className="text-amber-100">
                                                        {ticket.available > 0 ? elFormatter(ticket.available, 0) : 'N/A'}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm text-amber-100">
                                                    <span className={ticket.sold > 0 ? 'text-green-400' : 'text-amber-100'}>
                                                        {elFormatter(ticket.sold, 0)}
                                                    </span>
                                                </td>
                                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-right text-white">
                                                    €{elFormatter(ticket.revenue, 2)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </>
                )}
            </AdminLayout>
        </ProtectedRoute >
    );
};