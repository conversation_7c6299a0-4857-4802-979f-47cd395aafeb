import { RealtimeChannel } from '@supabase/supabase-js';
import { store } from '@/app/store';
import {
  updateConnectionStatus,
  handleRealtimeInsert,
  handleRealtimeUpdate,
  handleRealtimeDelete,
  addError,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  setLastConnectedAt
} from '@/app/store/slices/realtimeSlice';
import {
  handleRealtimeTransactionInsert,
  handleRealtimeTransactionUpdate,
  handleRealtimeTransactionDelete,
  updateDashboardMetricsRealtime
} from '@/app/store/slices/adminSlice';
import { getSupabaseClient } from './supabaseClient';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface RealtimeNotification {
  id: string;
  type: 'insert' | 'update' | 'delete';
  table: string;
  data: any;
  timestamp: string; // ISO string instead of Date
  message: string;
}

export interface SubscriptionConfig {
  table: string;
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  enabled: boolean;
}

class RealtimeManager {
  private supabase: ReturnType<typeof getSupabaseClient> | null = null;
  private channels: Map<string, RealtimeChannel> = new Map();
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isActive = false;
  private eventListeners: Map<string, Function[]> = new Map();

  // Default subscription configurations
  private subscriptionConfigs: SubscriptionConfig[] = [
    { table: 'AnalyticsEvents', event: '*', enabled: true },
    { table: 'Orders', event: '*', enabled: true },
    { table: 'VendorStands', event: '*', enabled: true },
    { table: 'CharityDonations', event: '*', enabled: true },
    { table: 'NewsletterSubscribers', event: 'INSERT', enabled: true },
    { table: 'SantaBookings', event: '*', enabled: true },
    // Note: EventApplications table doesn't exist in current schema
  ];

  constructor() {
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
  }

  /**
   * Add event listener for real-time events
   */
  addEventListener(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  private emitEvent(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[RealtimeManager] Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Initialize the realtime manager with Supabase client
   */
  async initialize(): Promise<void> {
    const supabaseClient = getSupabaseClient();

    if (!supabaseClient) {
      console.error('[RealtimeManager] Supabase client is required');
      return;
    }

    if (this.supabase) {
      return;
    }

    this.supabase = supabaseClient;

    // Try to sign in anonymously for better RLS compatibility
    try {
      const { data, error } = await this.supabase.auth.signInAnonymously();
      if (error) {
        console.warn('[RealtimeManager] Anonymous auth not available:', error.message);
      }
    } catch (error) {
      console.warn('[RealtimeManager] Anonymous authentication failed, continuing without auth');
    }

    this.setupEventListeners();
  }

  /**
   * Check if the manager is initialized
   */
  isInitialized(): boolean {
    return this.supabase !== null;
  }

  /**
   * Start real-time subscriptions
   */
  async start(): Promise<void> {
    if (!this.supabase) {
      console.warn('[RealtimeManager] Supabase client not initialized - real-time monitoring will not start');
      this.updateConnectionStatus('disconnected');
      return;
    }

    if (this.isActive) {
      return;
    }

    this.isActive = true;
    this.updateConnectionStatus('connecting');

    try {
      await this.createSubscriptions();
      this.updateConnectionStatus('connected');
      store.dispatch(setLastConnectedAt(new Date().toISOString()));
      store.dispatch(resetReconnectAttempts());
      this.reconnectAttempts = 0;
    } catch (error) {
      console.error('[RealtimeManager] Failed to start:', error);
      this.updateConnectionStatus('error');
      store.dispatch(addError({
        message: `Failed to start real-time monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`,
        context: 'RealtimeManager.start'
      }));
      store.dispatch(incrementReconnectAttempts());
      this.scheduleReconnect();
    }
  }

  /**
   * Stop all real-time subscriptions
   */
  async stop(): Promise<void> {
    this.isActive = false;
    await this.cleanupSubscriptions();
    this.updateConnectionStatus('disconnected');
  }

  /**
   * Get current connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Check if manager is active
   */
  isManagerActive(): boolean {
    return this.isActive;
  }

  /**
   * Manually trigger reconnection
   */
  async reconnect(): Promise<void> {
    await this.stop();
    await this.start();
  }

  /**
   * Create subscriptions for all configured tables
   */
  private async createSubscriptions(): Promise<void> {
    if (!this.supabase) return;

    const promises = this.subscriptionConfigs
      .filter(config => config.enabled)
      .map(config => this.createTableSubscription(config));

    await Promise.all(promises);
  }

  /**
   * Create subscription for a specific table
   */
  private async createTableSubscription(config: SubscriptionConfig): Promise<void> {
    if (!this.supabase) return;

    const channelName = `realtime:${config.table}`;

    try {
      const channel = this.supabase
        .channel(channelName)
        .on(
          'postgres_changes' as any,
          {
            event: config.event,
            schema: 'public',
            table: config.table,
            filter: config.filter
          },
          (payload: any) => {
            this.handleDatabaseChange(config.table, payload);
          }
        )
        .subscribe((status: string, err?: any) => {
          if (status === 'SUBSCRIBED') {} else if (status === 'CHANNEL_ERROR') {
            console.error(`[RealtimeManager] Subscription error for ${config.table}. Status: ${status}`, err ?? 'No error payload');
            store.dispatch(addError({
              message: `Subscription error for ${config.table}. Status: ${status}`,
              context: 'RealtimeManager.subscription'
            }));
            this.handleSubscriptionError(config.table);
          } else if (status === 'CLOSED') {
            console.warn(`[RealtimeManager] Subscription closed for ${config.table}`);
            this.handleSubscriptionError(config.table);
          }
        });

      this.channels.set(channelName, channel);
    } catch (error) {
      console.error(`[RealtimeManager] Failed to create subscription for ${config.table}:`, error);
      throw error;
    }
  }

  /**
   * Handle database change events
   */
  private handleDatabaseChange(table: string, payload: any): void {
    const { eventType, new: newRecord, old: oldRecord } = payload;

    // Dispatch notification to Redux immediately
    try {
      const notification = {
        id: `${table}_${eventType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: eventType.toLowerCase() as 'insert' | 'update' | 'delete',
        table,
        data: newRecord || oldRecord,
        timestamp: new Date().toISOString(),
        message: this.generateNotificationMessage(eventType.toLowerCase(), table, newRecord || oldRecord, oldRecord)
      };

      // Only emit event for React components to listen to
      // Do NOT dispatch directly to Redux to avoid infinite loops
      this.emitEvent('notification', notification);

    } catch (error) {
      console.error('[RealtimeManager] Failed to dispatch notification:', error);
    }

    try {
      switch (eventType) {
        case 'INSERT':
          this.handleInsert(table, newRecord);
          break;
        case 'UPDATE':
          this.handleUpdate(table, newRecord, oldRecord);
          break;
        case 'DELETE':
          this.handleDelete(table, oldRecord);
          break;
        default:
          console.warn(`[RealtimeManager] Unknown event type: ${eventType}`);
      }
    } catch (error) {
      console.error(`[RealtimeManager] Error handling ${eventType} for ${table}:`, error);
    }
  }

  /**
   * Handle INSERT events
   */
  private handleInsert(table: string, record: any): void {
    // Note: Notifications are now handled through the event system
    // to avoid Redux store instance conflicts

    // Dispatch to admin slice for transaction-specific handling
    if (['Orders', 'VendorStands', 'CharityDonations'].includes(table)) {
      store.dispatch(handleRealtimeTransactionInsert({ table, record }));
      store.dispatch(updateDashboardMetricsRealtime({ table, record, type: 'insert' }));
    }
  }

  /**
   * Handle UPDATE events
   */
  private handleUpdate(table: string, newRecord: any, oldRecord: any): void {
    // Note: Notifications are now handled through the event system
    // to avoid Redux store instance conflicts
    store.dispatch(handleRealtimeUpdate({ table, newRecord, oldRecord }));

    // Dispatch to admin slice for transaction-specific handling
    if (['Orders', 'VendorStands', 'CharityDonations'].includes(table)) {
      store.dispatch(handleRealtimeTransactionUpdate({ table, newRecord, oldRecord }));
      store.dispatch(updateDashboardMetricsRealtime({ table, record: newRecord, type: 'update', oldRecord }));
    }
  }

  /**
   * Handle DELETE events
   */
  private handleDelete(table: string, record: any): void {
    // Note: Notifications are now handled through the event system
    // to avoid Redux store instance conflicts
    store.dispatch(handleRealtimeDelete({ table, record }));

    // Dispatch to admin slice for transaction-specific handling
    if (['Orders', 'VendorStands', 'CharityDonations'].includes(table)) {
      store.dispatch(handleRealtimeTransactionDelete({ table, record }));
      store.dispatch(updateDashboardMetricsRealtime({ table, record, type: 'delete' }));
    }
  }

  /**
   * Generate user-friendly notification messages
   */
  private generateNotificationMessage(
    type: 'insert' | 'update' | 'delete',
    table: string,
    record: any,
    oldRecord?: any
  ): string {
    const tableDisplayNames: Record<string, string> = {
      'Orders': 'ticket order',
      'VendorStands': 'vendor booking',
      'CharityDonations': 'donation',
      'NewsletterSubscribers': 'newsletter signup',
      'SantaBookings': 'Santa booking'
    };

    const displayName = tableDisplayNames[table] || table.toLowerCase();

    switch (type) {
      case 'insert':
        if (table === 'Orders') {
          return `New ticket order from ${record.name || 'customer'} - €${record.total}`;
        } else if (table === 'VendorStands') {
          return `New vendor booking: ${record.business_name} - Stand ${record.stand_number}`;
        } else if (table === 'CharityDonations') {
          return `New donation: €${record.amount}`;
        } else if (table === 'NewsletterSubscribers') {
          return `New newsletter signup: ${record.email}`;
        } else if (table === 'SantaBookings') {
          return `New Santa booking: ${record.customer_name} - ${record.day} ${record.time_range}`;
        }
        return `New ${displayName} created`;

      case 'update':
        if (table === 'Orders' && oldRecord?.status !== record.status) {
          return `Order ${record.id} status changed: ${oldRecord.status} → ${record.status}`;
        } else if (table === 'VendorStands' && oldRecord?.payment_status !== record.payment_status) {
          return `Vendor booking ${record.stand_number} payment: ${oldRecord.payment_status} → ${record.payment_status}`;
        } else if (table === 'SantaBookings' && oldRecord?.day !== record.day) {
          return `Santa booking updated: ${record.customer_name} moved to ${record.day}`;
        }
        return `${displayName} updated`;

      case 'delete':
        return `${displayName} deleted`;

      default:
        return `${displayName} changed`;
    }
  }

  /**
   * Clean up all subscriptions
   */
  private async cleanupSubscriptions(): Promise<void> {
    const unsubscribePromises = Array.from(this.channels.values()).map(channel =>
      this.supabase?.removeChannel(channel)
    );

    await Promise.all(unsubscribePromises);
    this.channels.clear();
  }

  /**
   * Handle subscription errors
   */
  private handleSubscriptionError(table: string): void {
    console.error(`[RealtimeManager] Subscription error for ${table}`);

    if (this.isActive) {
      this.scheduleReconnect();
    }
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[RealtimeManager] Max reconnection attempts reached');
      this.updateConnectionStatus('error');
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    setTimeout(async () => {
      if (this.isActive) {
        try {
          await this.stop();
          await this.start();
        } catch (error) {
          console.error('[RealtimeManager] Reconnection failed:', error);
          store.dispatch(addError({
            message: `Reconnection attempt ${this.reconnectAttempts} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            context: 'RealtimeManager.reconnect'
          }));
        }
      }
    }, delay);
  }

  /**
   * Update connection status and dispatch to Redux
   */
  private updateConnectionStatus(status: ConnectionStatus): void {
    this.connectionStatus = status;
    store.dispatch(updateConnectionStatus(status));
  }

  /**
   * Setup event listeners for page visibility and unload
   */
  private setupEventListeners(): void {
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange);
      window.addEventListener('beforeunload', this.handleBeforeUnload);
    }
  }

  /**
   * Handle page visibility changes
   */
  private handleVisibilityChange(): void {
    if (document.hidden) {
      // Keep connections active even when page is hidden for admin monitoring
    } else {
      if (this.connectionStatus === 'error' || this.connectionStatus === 'disconnected') {
        this.reconnect();
      }
    }
  }

  /**
   * Handle page unload
   */
  private handleBeforeUnload(): void {
    this.stop();
  }

  /**
   * Cleanup event listeners
   */
  destroy(): void {
    if (typeof window !== 'undefined') {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange);
      window.removeEventListener('beforeunload', this.handleBeforeUnload);
    }
    this.stop();
  }
}

// Create singleton instance
export const realtimeManager = new RealtimeManager();