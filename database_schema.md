| table_schema | table_name            | column_name         | data_type                |
| ------------ | --------------------- | ------------------- | ------------------------ |
| public       | AdminAuditLogs        | id                  | uuid                     |
| public       | AdminAuditLogs        | user_id             | uuid                     |
| public       | AdminAuditLogs        | action              | text                     |
| public       | AdminAuditLogs        | details             | jsonb                    |
| public       | AdminAuditLogs        | created_at          | timestamp with time zone |
| public       | AdminAuditLogs        | severity            | text                     |
| public       | AdminUsers            | id                  | uuid                     |
| public       | AdminUsers            | user_id             | uuid                     |
| public       | AdminUsers            | role                | text                     |
| public       | AdminUsers            | created_at          | timestamp with time zone |
| public       | AdminUsers            | updated_at          | timestamp with time zone |
| public       | AdminUsers            | email               | text                     |
| public       | AdminUsers            | password            | text                     |
| public       | AdminUsers            | name                | text                     |
| public       | AnalyticsEvents       | id                  | uuid                     |
| public       | AnalyticsEvents       | event_type          | text                     |
| public       | AnalyticsEvents       | page_path           | text                     |
| public       | AnalyticsEvents       | referrer            | text                     |
| public       | AnalyticsEvents       | user_agent          | text                     |
| public       | AnalyticsEvents       | ip_address          | inet                     |
| public       | AnalyticsEvents       | session_id          | text                     |
| public       | AnalyticsEvents       | user_id             | uuid                     |
| public       | AnalyticsEvents       | created_at          | timestamp with time zone |
| public       | AnalyticsEvents       | device_type         | text                     |
| public       | AnalyticsEvents       | browser             | text                     |
| public       | AnalyticsEvents       | event_data          | jsonb                    |
| public       | AnalyticsEvents       | os                  | text                     |
| public       | AnalyticsEvents       | viewport_size       | text                     |
| public       | AnalyticsEvents       | screen_resolution   | text                     |
| public       | AnalyticsEvents       | is_touch_device     | boolean                  |
| public       | AnalyticsEvents       | language            | text                     |
| public       | AnalyticsEvents       | timezone            | text                     |
| public       | CharityDonations      | id                  | uuid                     |
| public       | CharityDonations      | order_id            | uuid                     |
| public       | CharityDonations      | amount              | numeric                  |
| public       | CharityDonations      | created_at          | timestamp with time zone |
| public       | CharityDonations      | metadata            | jsonb                    |
| public       | CharityDonations      | multiplier          | numeric                  |
| public       | CharityDonations      | original_amount     | numeric                  |
| public       | NewsletterSubscribers | id                  | uuid                     |
| public       | NewsletterSubscribers | email               | text                     |
| public       | NewsletterSubscribers | subscribed_at       | timestamp with time zone |
| public       | NewsletterSubscribers | created_at          | timestamp with time zone |
| public       | NewsletterSubscribers | is_subscribed       | boolean                  |
| public       | OrderItems            | id                  | integer                  |
| public       | OrderItems            | ticket_id           | text                     |
| public       | OrderItems            | quantity            | integer                  |
| public       | OrderItems            | price               | numeric                  |
| public       | OrderItems            | order_id            | uuid                     |
| public       | OrderItems            | original_ticket_id  | text                     |
| public       | OrderItems            | selected_day        | text                     |
| public       | OrderItems            | created_at          | timestamp with time zone |
| public       | OrderItems            | status              | text                     |
| public       | Orders                | email               | text                     |
| public       | Orders                | name                | text                     |
| public       | Orders                | total               | numeric                  |
| public       | Orders                | payment_intent_id   | text                     |
| public       | Orders                | created_at          | timestamp with time zone |
| public       | Orders                | id                  | uuid                     |
| public       | Orders                | redeemed            | boolean                  |
| public       | Orders                | redeemed_timestamp  | timestamp with time zone |
| public       | Orders                | status              | text                     |
| public       | SantaBookings         | id                  | integer                  |
| public       | SantaBookings         | slot_id             | integer                  |
| public       | SantaBookings         | order_id            | character varying        |
| public       | SantaBookings         | customer_email      | character varying        |
| public       | SantaBookings         | customer_name       | character varying        |
| public       | SantaBookings         | num_children        | integer                  |
| public       | SantaBookings         | created_at          | timestamp with time zone |
| public       | SantaBookings         | day                 | text                     |
| public       | SantaBookings         | time_range          | text                     |
| public       | SantaBookings         | ticket_id           | text                     |
| public       | SantaTimeSlots        | id                  | integer                  |
| public       | SantaTimeSlots        | day                 | character varying        |
| public       | SantaTimeSlots        | start_time          | time without time zone   |
| public       | SantaTimeSlots        | end_time            | time without time zone   |
| public       | SantaTimeSlots        | capacity            | integer                  |
| public       | SantaTimeSlots        | booked              | integer                  |
| public       | SantaTimeSlots        | created_at          | timestamp with time zone |
| public       | TicketSales           | ticket_id           | text                     |
| public       | TicketSales           | quantity            | integer                  |
| public       | TicketSales           | created_at          | timestamp with time zone |
| public       | TicketSales           | id                  | uuid                     |
| public       | TicketSales           | selected_day        | text                     |
| public       | Tickets               | id                  | text                     |
| public       | Tickets               | name                | text                     |
| public       | Tickets               | description         | text                     |
| public       | Tickets               | price               | numeric                  |
| public       | Tickets               | max_quantity        | integer                  |
| public       | Tickets               | original_id         | text                     |
| public       | Tickets               | type                | text                     |
| public       | VendorStands          | id                  | uuid                     |
| public       | VendorStands          | stand_number        | integer                  |
| public       | VendorStands          | business_name       | character varying        |
| public       | VendorStands          | contact_name        | character varying        |
| public       | VendorStands          | email               | character varying        |
| public       | VendorStands          | phone               | character varying        |
| public       | VendorStands          | price               | numeric                  |
| public       | VendorStands          | created_at          | timestamp with time zone |
| public       | VendorStands          | updated_at          | timestamp with time zone |
| public       | VendorStands          | addons              | jsonb                    |
| public       | VendorStands          | discount_applied    | boolean                  |
| public       | VendorStands          | discount_percentage | numeric                  |
| public       | VendorStands          | original_price      | numeric                  |
| public       | VendorStands          | payment_status      | text                     |
| public       | totaldonations        | total_amount        | numeric                  |