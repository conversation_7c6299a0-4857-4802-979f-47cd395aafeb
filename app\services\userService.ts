import { User, CreateUserData, UpdateUserData, UserFilters, UsersResponse, AdminRole } from '@/app/models/user';
import bcrypt from 'bcryptjs';
import type { SupabaseClient } from '@supabase/supabase-js';

// No mock data - only use real database

// Simulate API delay for consistent UX
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const userService = {
  async getUsers(supabase: SupabaseClient, filters: UserFilters = {}): Promise<UsersResponse> {
    await delay(300); // Simulate network delay

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // Build the query for AdminUsers table
      let query = supabase
        .from('AdminUsers')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filters.search) {
        const search = filters.search.toLowerCase();
        query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
      }

      if (filters.role) {
        query = query.eq('role', filters.role);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'name';
      const sortOrder = filters.sortOrder || 'asc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit - 1;

      query = query.range(startIndex, endIndex);

      const { data: adminUsers, error, count } = await query;

      if (error) {
        console.error('UserService: Database error:', error);
        throw new Error(error.message);
      }

      // Transform AdminUsers data to User format
      const users: User[] = (adminUsers || []).map((adminUser: any) => ({
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true, // AdminUsers table doesn't have isActive field, assume all are active
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: undefined, // AdminUsers table doesn't track last login
      }));

      return {
        data: users,
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit),
      };

    } catch (error) {
      console.error('Error fetching admin users:', error);
      throw error;
    }

  },

  async getCurrentUserFromStorage(supabase: SupabaseClient): Promise<User | null> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // Since we're using custom authentication (AdminUsers table with bcrypt),
      // we need to get the current user from localStorage instead of Supabase auth
      const storedUser = localStorage.getItem('adminUser');
      if (!storedUser) {
        console.warn('getCurrentUser: no stored admin user found');
        return null;
      }

      const adminUserData = JSON.parse(storedUser);
      if (!adminUserData || !adminUserData.id) {
        console.warn('getCurrentUser: invalid stored admin user data');
        return null;
      }

      // Verify the user still exists in the database
      const { data: adminUser, error: adminError } = await supabase
        .from('AdminUsers')
        .select('*')
        .eq('id', adminUserData.id)
        .single();

      if (adminError || !adminUser) {
        console.warn('getCurrentUser: admin user not found in AdminUsers table', adminError);
        // Clear invalid stored user
        localStorage.removeItem('adminUser');
        return null;
      }

      // Return the properly formatted User object
      return {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true, // Assume active if they're stored and exist in DB
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: new Date(), // Current time as last login
      };
    } catch (err) {
      console.error('Error fetching current user:', err);
      return null;
    }
  },

  async getCurrentUser(supabase: SupabaseClient): Promise<User | null> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      // fetch the session’s user
      const { data, error } = await supabase.auth.getUser();
      console.log({ data, error });

      if (error || !data?.user) {
        console.warn('getCurrentUser: no session user or error', error);
        return null;
      }

      const authUser = data.user;

      // Then, look up the admin user in the AdminUsers table using the auth user ID
      const { data: adminUser, error: adminError } = await supabase
        .from('AdminUsers')
        .select('*')
        .eq('user_id', authUser.id)
        .single();

      if (adminError || !adminUser) {
        console.warn('getCurrentUser: admin user not found in AdminUsers table', adminError);
        return null;
      }

      // Return the properly formatted User object
      return {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true, // Assume active if they can authenticate
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: new Date(), // Current time as last login
      };
    } catch (err) {
      console.error('Error fetching current user:', err);
      return null;
    }
  },

  async getUserById(supabase: SupabaseClient, id: string): Promise<User | null> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      const { data: adminUser, error } = await supabase
        .from('AdminUsers')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !adminUser) {
        return null;
      }

      return {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true,
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: undefined,
      };
    } catch (error) {
      console.error('Error fetching user by ID:', error);
      return null;
    }
  },

  async createUser(supabase: SupabaseClient, data: CreateUserData): Promise<User> {
    await delay(500);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      const adminUserId = crypto.randomUUID();
      const userId = crypto.randomUUID();

      const hashedPassword = await bcrypt.hash(data.password, 10);

      const { data: adminUser, error } = await supabase
        .from('AdminUsers')
        .insert({
          id: adminUserId,
          user_id: userId,
          email: data.email,
          name: data.name,
          password: hashedPassword, // Use the hashed password
          role: data.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error || !adminUser) {
        throw new Error(error?.message || 'Failed to create user');
      }

      return {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true,
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: undefined,
      };
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  async updateUser(supabase: SupabaseClient, id: string, data: UpdateUserData): Promise<User | null> {
    await delay(500);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      const { data: adminUser, error } = await supabase
        .from('AdminUsers')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error || !adminUser) {
        return null;
      }

      return {
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        isActive: true,
        createdAt: new Date(adminUser.created_at),
        updatedAt: new Date(adminUser.updated_at),
        lastLogin: undefined,
      };
    } catch (error) {
      console.error('Error updating user:', error);
      return null;
    }
  },

  async deleteUser(supabase: SupabaseClient, id: string): Promise<boolean> {
    await delay(300);

    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      const { error } = await supabase
        .from('AdminUsers')
        .delete()
        .eq('id', id);

      return !error;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  },

  async toggleUserStatus(supabase: SupabaseClient, id: string): Promise<User | null> {
    if (!supabase) {
      throw new Error('Supabase client is required');
    }

    try {
      const user = await this.getUserById(supabase, id);
      if (!user) return null;

      return this.updateUser(supabase, id, { isActive: !user.isActive });
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  },
};