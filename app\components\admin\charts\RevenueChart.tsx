import { useMemo } from 'react';
import { ResponsiveLine } from '@nivo/line';
import { elFormatter } from '@/lib/utils';

interface DataPoint {
  date: string;
  revenue: number;
}

interface RevenueChartProps {
  data: DataPoint[];
  title?: string;
  height?: number;
  tickFormat?: (value: number) => string;
  color?: string;
}

export function RevenueChart({
  data,
  title = 'Revenue Over Time',
  height = 300,
  tickFormat,
  color = '#f59e0b' // amber-500
}: RevenueChartProps) {
  // Sort data by date
  const sortedData = useMemo(() =>
    [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    , [data]);

  // Format data for Nivo Line chart
  const chartData = useMemo(() => [{
    id: 'revenue',
    color,
    data: sortedData.map(item => ({
      x: new Date(item.date).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      }),
      y: item.revenue,
      date: item.date,
      formattedValue: new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
      }).format(item.revenue)
    }))
  }], [sortedData, color]);

  // Format currency for axis
  const formatCurrency = (value: number) => {
    if (tickFormat) return tickFormat(value);

    return '€'+elFormatter(value);
  };

  // Custom tooltip
  const CustomTooltip = ({ point }: any) => {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-md p-2 text-sm">
        <div className="font-medium text-white">
          {new Date(point.data.date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })}
        </div>
        <div className="flex items-center mt-1">
          <div
            className="w-3 h-3 rounded-sm mr-2"
            style={{ backgroundColor: point.serieColor }}
          />
          <span className="text-gray-300">
            Revenue: {point.data.formattedValue}
          </span>
        </div>
      </div>
    );
  };

  if (sortedData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-400">No revenue data available</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col" style={{ height: `${height}px` }}>
      {title && (
        <div className="mb-2">
          <h3 className="text-lg font-semibold text-white">{title}</h3>
        </div>
      )}
      <div className="text-sm text-gray-400 mb-2">
        {new Date(sortedData[0].date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })} - {new Date(sortedData[sortedData.length - 1].date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        })}
      </div>

      <div className="flex-1">
        <ResponsiveLine
          data={chartData}
          margin={{ top: 10, right: 20, bottom: 40, left: 50 }}
          xScale={{
            type: 'point',
          }}
          yScale={{
            type: 'linear',
            min: 0,
            max: 'auto',
            nice: true,
          }}
          curve="monotoneX"
          axisTop={null}
          axisRight={null}
          axisBottom={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: sortedData.length > 7 ? 45 : 0,
            legend: 'Date',
            legendOffset: 36,
            legendPosition: 'middle',
          }}
          axisLeft={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: 0,
            format: formatCurrency,
            legend: 'Revenue',
            legendOffset: -40,
            legendPosition: 'middle',
          }}
          colors={[color]}
          lineWidth={2}
          pointSize={6}
          pointColor={{ theme: 'background' }}
          pointBorderWidth={2}
          pointBorderColor={{ from: 'serieColor' }}
          pointLabelYOffset={-12}
          enableArea={true}
          areaOpacity={0.1}
          areaBaselineValue={0}
          enableGridX={false}
          enableGridY={true}
          enablePoints={sortedData.length <= 30}
          useMesh={true}
          theme={{
            textColor: '#9ca3af',
            fontSize: 12,
            axis: {
              domain: {
                line: {
                  stroke: '#374151',
                  strokeWidth: 1,
                },
              },
              ticks: {
                line: {
                  stroke: '#374151',
                  strokeWidth: 1,
                },
              },
            },
            grid: {
              line: {
                stroke: '#374151',
                strokeWidth: 0.5,
                strokeDasharray: '2 2',
              },
            },
            tooltip: {
              container: {
                background: 'transparent',
                padding: 0,
                boxShadow: 'none',
              },
            },
          }}
          tooltip={CustomTooltip}
          motionConfig="gentle"
          enableSlices={false}
        />
      </div>
    </div>
  );
}