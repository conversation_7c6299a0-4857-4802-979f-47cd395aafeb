import { Suspense } from 'react';
import BookTicketsClient from '@/app/components/BookTickets/book-tickets-client';
import { getTickets } from '@/lib/tickets';

export const metadata = {
  title: 'Book Tickets - The Convention Before Christmas',
  description: 'Secure your spot at The Convention Before Christmas! Book your tickets for this magical event.',
};

export default function BookTicketsPage() {
  return (
    <section className="py-20 min-h-[80vh]">
      <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4">Book Tickets</h2>
          <p className="text-gray-300">Secure your spot at The Convention Before Christmas!</p>
        </div>

        <div className="max-w-2xl mx-auto space-y-6">
          <Suspense fallback={
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[var(--darkGold)] mx-auto"></div>
              <p className="text-white mt-4">Loading tickets...</p>
            </div>
          }>
            <BookTicketsClient initialTickets={[]} />
          </Suspense>
        </div>
      </div>
    </section>
  );
}