import { useEffect, useRef, useState } from 'react';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { RealtimeConnectionStatus } from '@/app/components/admin/RealtimeConnectionStatus';
import { RevenueChart } from '@/app/components/admin/charts/RevenueChart';
import { TicketDistributionChart } from '@/app/components/admin/charts/TicketDistributionChart';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchDashboardMetrics } from '@/app/store/slices/adminSlice';
import { selectConnectionStatus, selectIsConnected } from '@/app/store/slices/realtimeSlice';
import { RefreshCw, Download, Calendar, ChevronDown } from 'lucide-react';
import { subDays, startOfYear } from 'date-fns';
import { exportToCSV, exportToPDF, exportToExcel, formatDataForExport, generateFilename } from '@/app/utils/exportUtils';
import { useSupabase } from '@/app/context/AdminAuthContext';
import { showExportStartToast, updateToastToSuccess, updateToastToError } from '@/app/utils/toast';

export default function FinancialDashboard() {
  const dispatch = useAppDispatch();
  const { data: metrics, loading, error } = useAppSelector(state => state.admin.metrics);
  const connectionStatus = useAppSelector(selectConnectionStatus);
  const isConnected = useAppSelector(selectIsConnected);
  const supabase = useSupabase(); // Add hook at component level
  const [dateRange, setDateRange] = useState('30days'); // '7days', '30days', '90days', 'year', 'all'
  const [showDateRangeMenu, setShowDateRangeMenu] = useState(false);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const dateRangeRef = useRef<HTMLDivElement>(null);
  const exportRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dateRangeRef.current && !dateRangeRef.current.contains(event.target as Node)) {
        setShowDateRangeMenu(false);
      }
      if (exportRef.current && !exportRef.current.contains(event.target as Node)) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Helper function to get date range parameters based on the selected range
  const getDateRangeParams = () => {
    let startDate: Date | undefined;
    const endDate = new Date();

    switch (dateRange) {
      case '7days':
        startDate = subDays(endDate, 7);
        break;
      case '30days':
        startDate = subDays(endDate, 30);
        break;
      case '90days':
        startDate = subDays(endDate, 90);
        break;
      case 'year':
        startDate = startOfYear(endDate);
        break;
      case 'all':
      default:
        // No date range filtering for 'all'
        break;
    }

    return startDate
      ? {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      }
      : {};
  };

  // Fetch metrics when date range changes or component mounts
  useEffect(() => {
    const fetchData = async () => {
      const params = getDateRangeParams();

      try {
        // @ts-ignore - TypeScript doesn't recognize the params type yet
        await dispatch(fetchDashboardMetrics(params)).unwrap();
      } catch (error) {
        console.error('Failed to fetch dashboard metrics:', error);
      }
    };

    fetchData();
  }, [dateRange, dispatch]);

  // Handle refresh button click
  const handleRefresh = async () => {
    const params = getDateRangeParams();

    try {
      // @ts-ignore - TypeScript doesn't recognize the params type yet
      await dispatch(fetchDashboardMetrics(params)).unwrap();
    } catch (error) {
      console.error('Failed to refresh dashboard metrics:', error);
    }
  };

  // Toggle date range menu
  const toggleDateRangeMenu = () => {
    setShowDateRangeMenu(!showDateRangeMenu);
    setShowExportMenu(false);
  };

  // Toggle export menu
  const toggleExportMenu = () => {
    setShowExportMenu(!showExportMenu);
    setShowDateRangeMenu(false);
  };

  // Handle date range change
  const handleDateRangeChange = (range: string) => {
    setDateRange(range);
    setShowDateRangeMenu(false);
  };

  // Handle export data
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    const toastId = showExportStartToast(format);

    try {
      setShowExportMenu(false);

      const financialData: any[] = [];

      // Calculate date range
      let fromDate: Date;
      const toDate = new Date();

      switch (dateRange) {
        case '7days':
          fromDate = subDays(toDate, 7);
          break;
        case '30days':
          fromDate = subDays(toDate, 30);
          break;
        case '90days':
          fromDate = subDays(toDate, 90);
          break;
        case 'year':
          fromDate = startOfYear(toDate);
          break;
        default:
          fromDate = new Date('2020-01-01'); // All time
      }

      // Fetch orders within date range
      const { data: orders, error: ordersError } = await supabase
        .from('Orders')
        .select('*')
        .gte('created_at', fromDate.toISOString())
        .lte('created_at', toDate.toISOString())
        .order('created_at', { ascending: false });

      if (!ordersError && orders) {
        orders.forEach(order => {
          financialData.push({
            created_at: order.created_at,
            type: 'Ticket Sales',
            amount: order.total_amount,
            source: 'Online',
            status: order.status,
            customer_email: order.customer_email
          });
        });
      }

      // Fetch vendor stands within date range
      const { data: vendors, error: vendorsError } = await supabase
        .from('VendorStands')
        .select('*')
        .gte('created_at', fromDate.toISOString())
        .lte('created_at', toDate.toISOString())
        .order('created_at', { ascending: false });

      if (!vendorsError && vendors) {
        vendors.forEach(vendor => {
          financialData.push({
            created_at: vendor.created_at,
            type: 'Vendor Stalls',
            amount: vendor.price,
            source: 'Vendor Registration',
            status: 'Completed',
            customer_email: vendor.email
          });
        });
      }

      // Fetch charity donations within date range
      const { data: donations, error: donationsError } = await supabase
        .from('CharityDonations')
        .select('*')
        .gte('created_at', fromDate.toISOString())
        .lte('created_at', toDate.toISOString())
        .order('created_at', { ascending: false });

      if (!donationsError && donations) {
        donations.forEach(donation => {
          financialData.push({
            created_at: donation.created_at,
            type: 'Charity Donations',
            amount: donation.amount,
            source: 'Donation',
            status: 'Completed',
            customer_email: donation.email || 'Anonymous'
          });
        });
      }

      // Sort by date
      financialData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      if (financialData.length === 0) {
        updateToastToError(toastId, 'No financial data found for the selected date range');
        return;
      }

      // Format data for export
      const formattedData = formatDataForExport(financialData, 'financial');
      const filename = generateFilename('financial-report', dateRange);

      // Export based on format
      switch (format) {
        case 'csv':
          await exportToCSV(formattedData, filename);
          break;
        case 'pdf':
          await exportToPDF(formattedData, filename, `Financial Report (${dateRange})`);
          break;
        case 'excel':
          await exportToExcel(formattedData, filename);
          break;
      }

      updateToastToSuccess(toastId, `Successfully exported ${financialData.length} financial records as ${format.toUpperCase()}`);

    } catch (error) {
      console.error('Export failed:', error);
      updateToastToError(toastId, `Export failed: ${(error as any)?.message || 'Please try again'}`);
    }
  };

  return (
    <ProtectedRoute requiredRole={['super_admin', 'finance_admin', 'support_admin']}>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header with controls */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex flex-col md:flex-row items-center gap-4">
              <h1 className="text-2xl font-bold text-white">Financial Metrics</h1>

              {/* Real-time connection status */}
              <RealtimeConnectionStatus size="sm" variant="badge" />
            </div>

            <div className="flex flex-col md:flex-row items-center justify-end gap-4">
              {/* Date range selector */}
              <div className="relative inline-block" ref={dateRangeRef}>
                <button
                  onClick={toggleDateRangeMenu}
                  className="flex items-center space-x-1 px-3 py-2 bg-zinc-800 border border-white/50 rounded-md shadow-sm text-sm text-white hover:bg-zinc-700/50 focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <Calendar className="h-4 w-4 text-white" />
                  <span>
                    {dateRange === '7days' && 'Last 7 days'}
                    {dateRange === '30days' && 'Last 30 days'}
                    {dateRange === '90days' && 'Last 90 days'}
                    {dateRange === 'year' && 'This year'}
                    {dateRange === 'all' && 'All time'}
                  </span>
                  <ChevronDown className={`h-4 w-4 text-white transition-transform ${showDateRangeMenu ? 'transform rotate-180' : ''}`} />
                </button>

                {showDateRangeMenu && (
                  <div className="absolute right-0 mt-1 w-48 bg-zinc-800 rounded-md shadow-lg border border-amber-600/50 z-10">
                    <div className="py-1">
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleDateRangeChange('7days')}
                      >
                        Last 7 days
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleDateRangeChange('30days')}
                      >
                        Last 30 days
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleDateRangeChange('90days')}
                      >
                        Last 90 days
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleDateRangeChange('year')}
                      >
                        This year
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleDateRangeChange('all')}
                      >
                        All time
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Export button */}
              <div className="relative inline-block" ref={exportRef}>
                <button
                  onClick={toggleExportMenu}
                  className="flex items-center space-x-1 px-3 py-2 bg-zinc-800 border border-white/50 rounded-md shadow-sm text-sm text-white hover:bg-zinc-700/50 focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <Download className="h-4 w-4 text-white" />
                  <span>Export</span>
                  <ChevronDown className={`h-4 w-4 text-white transition-transform ${showExportMenu ? 'transform rotate-180' : ''}`} />
                </button>

                {showExportMenu && (
                  <div className="absolute right-0 mt-1 w-32 bg-zinc-800 rounded-md shadow-lg border border-amber-600/50 z-10">
                    <div className="py-1">
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleExport('csv')}
                      >
                        CSV
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleExport('pdf')}
                      >
                        PDF
                      </button>
                      <button
                        className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-zinc-700/50"
                        onClick={() => handleExport('excel')}
                      >
                        Excel
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Refresh button */}
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="flex items-center space-x-1 px-3 py-2 bg-gold-600 border border-gold-600/50 rounded-md shadow-sm text-sm text-white hover:bg-gold-500 focus:outline-none focus:ring-2 focus:ring-gold-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className={`h-4 w-4 text-white ${loading ? 'animate-spin' : ''}`} />
                <span className="text-white">{loading ? 'Refreshing...' : 'Refresh'}</span>
              </button>
            </div>
          </div>

          {/* Key metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Total Revenue */}
            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-white">Total Revenue</p>
                  <h3 className="text-2xl font-bold text-white mt-1">
                    {metrics ? (
                      `€${metrics.totalRevenue.toLocaleString()}`
                    ) : (
                      <span className="text-white">--</span>
                    )}
                  </h3>
                </div>
                <div className="p-2 rounded-full bg-green-100 bg-green-900/30">
                  <svg className="w-6 h-6 text-green-600 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-green-500">+12.5%</span>
                <span className="text-xs text-gray-500 text-gray-400 ml-2">vs. last period</span>
              </div>
            </div>

            {/* Tickets Sold */}
            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-white">Tickets Sold</p>
                  <h3 className="text-2xl font-bold text-white mt-1">
                    {metrics ? (
                      metrics.ticketsSold.toLocaleString()
                    ) : (
                      <span className="text-white">--</span>
                    )}
                  </h3>
                </div>
                <div className="p-2 rounded-full bg-blue-100 bg-blue-900/30">
                  <svg className="w-6 h-6 text-blue-600 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                  </svg>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-blue-500">+8.2%</span>
                <span className="text-xs text-gray-500 text-gray-400 ml-2">vs. last period</span>
              </div>
            </div>

            {/* Average Order Value */}
            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-white">Avg. Order Value</p>
                  <h3 className="text-2xl font-bold text-white mt-1">
                    {metrics && metrics.ticketsSold > 0 ? (
                      `€${(metrics.totalRevenue / metrics.ticketsSold).toFixed(2)}`
                    ) : (
                      <span className="text-white">--</span>
                    )}
                  </h3>
                </div>
                <div className="p-2 rounded-full bg-purple-100 bg-purple-900/30">
                  <svg className="w-6 h-6 text-purple-600 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-purple-500">+3.7%</span>
                <span className="text-xs text-gray-500 text-gray-400 ml-2">vs. last period</span>
              </div>
            </div>

            {/* Conversion Rate */}
            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-white">Conversion Rate</p>
                  <h3 className="text-2xl font-bold text-white mt-1">
                    {metrics ? (
                      `${metrics.conversionRate}%`
                    ) : (
                      <span className="text-gray-400">--</span>
                    )}
                  </h3>
                </div>
                <div className="p-2 rounded-full bg-orange-100 bg-orange-900/30">
                  <svg className="w-6 h-6 text-orange-600 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className="text-sm font-medium text-orange-500">+0.8%</span>
                <span className="text-xs text-gray-500 text-gray-400 ml-2">vs. last period</span>
              </div>
            </div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              {/* Revenue Chart */}
              {metrics?.revenueByDay && (
                <RevenueChart data={metrics.revenueByDay} />
              )}
            </div>

            <div className="bg-zinc-800 rounded-lg shadow-md p-6">
              {/* Ticket Distribution Chart */}
              {metrics?.ticketsByType && (
                <TicketDistributionChart data={metrics.ticketsByType} />
              )}
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};