import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Suspense } from 'react';

interface Social {
  icon: string,
  href: string,
}

const socialLinks: Social[] = [
  {
    icon: `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
      {/*!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.*/}
      <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
    </svg>
  `,
    href: "https://instagram.com/theconbeforechristmas"
  },
  {
    icon: `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
      {/*!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.*/}
      <path d="M448 209.9a210.1 210.1 0 0 1 -122.8-39.3V349.4A162.6 162.6 0 1 1 185 188.3V278.2a74.6 74.6 0 1 0 52.2 71.2V0l88 0a121.2 121.2 0 0 0 1.9 22.2h0A122.2 122.2 0 0 0 381 102.4a121.4 121.4 0 0 0 67 20.1z" />
    </svg>
  `,
    href: "https://www.tiktok.com/@the.con.before.christmas"
  },
  {
    icon: `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
      {/*!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.*/}
      <path d="M64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64h98.2V334.2H109.4V256h52.8V222.3c0-87.1 39.4-127.5 125-127.5c16.2 0 44.2 3.2 55.7 6.4V172c-6-.6-16.5-1-29.6-1c-42 0-58.2 15.9-58.2 57.2V256h83.6l-14.4 78.2H255V480H384c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z" />
    </svg>
  `,
    href: "https://www.facebook.com/profile.php?id=61572625690312"
  },
  {
    icon: `
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
      {/*!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.*/}
      <path d="M549.7 124.1c-6.3-23.7-24.8-42.3-48.3-48.6C458.8 64 288 64 288 64S117.2 64 74.6 75.5c-23.5 6.3-42 24.9-48.3 48.6-11.4 42.9-11.4 132.3-11.4 132.3s0 89.4 11.4 132.3c6.3 23.7 24.8 41.5 48.3 47.8C117.2 448 288 448 288 448s170.8 0 213.4-11.5c23.5-6.3 42-24.2 48.3-47.8 11.4-42.9 11.4-132.3 11.4-132.3s0-89.4-11.4-132.3zm-317.5 213.5V175.2l142.7 81.2-142.7 81.2z" />
    </svg>
  `,
    href: "https://youtube.com/@theconbeforechristmas"
  },
];

const Location = () => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [LocationMapComponent, setLocationMapComponent] = useState<React.ComponentType | null>(null);

  useEffect(() => {
    mapboxgl.accessToken = 'pk.eyJ1IjoiY2hpbGxlZGtvbmdzIiwiYSI6ImNtMHptbWtybDA3eGEyanNnejhmeG03NnUifQ.koQ7k5Km5ttOZP9gKN9uzA';

    if (mapContainer.current && !map.current) {
      try {
        // Check if WebGL is supported
        if (!mapboxgl.supported()) {
          console.warn('WebGL is not supported in this browser');
          return;
        }

        map.current = new mapboxgl.Map({
          container: mapContainer.current,
          style: 'mapbox://styles/mapbox/dark-v11',
          center: [-6.2251355, 53.4368267],
          zoom: 14
        });

        // Create a popup that's always open
        const popup = new mapboxgl.Popup({
          closeOnClick: false,
          closeButton: false,
          offset: 25
        })
          .setLngLat([-6.2251355, 53.4368267])
          .setHTML('<h3 class="text-black font-bold">The National Show Centre</h3><p class="text-black">Stockhole Lane, Cloghran, Dublin</p>')
          .addTo(map.current);

        // Add marker
        const marker = new mapboxgl.Marker()
          .setLngLat([-6.2251355, 53.4368267])
          .setPopup(popup)
          .addTo(map.current);

        // Trigger popup to open
        marker.togglePopup();
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    }
  }, []);

  useEffect(() => {
    import('./location-map').then((module) => {
      setLocationMapComponent(() => module.default);
    }).catch(error => {
      console.error("Failed to load LocationMap component:", error);
    });
  }, []);

  return (
    <footer className="relative" id="location-section">
      <div className="container mx-auto lg:max-w-screen-xl md:max-w-screen-md px-4 relative z-10 py-10">
        <div className="grid grid-cols-1 justify-center items-center text-center">
          <h3 className="text-white text-4xl font-medium mb-9 uppercase">Contact Us</h3>
          <div className='flex justify-center items-center gap-4 mb-8'>
            {socialLinks.map((items, i) => (
              <a href={items.href} target="_blank" rel="nofollow noreferrer noopener cursor-pointer" key={i}> <span className="relative w-10 h-10 flex items-center justify-center footer-social-icons border border-solid border-white rounded-md" dangerouslySetInnerHTML={{ __html: items.icon }} /></a>
            ))}
          </div>
          <h4 className="text-white font-bold text-sm mb-6 flex justify-center items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width={20} height={20} style={{ fill: '#fff' }}>
              <path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z" />
            </svg>

            <span>(353) 89 448 5006</span>
          </h4>
          <h4 className="text-white text-sm font-bold mb-6 flex justify-center items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width={20} height={20} style={{ fill: '#fff' }}>
              <path d="M48 64C21.5 64 0 85.5 0 112c0 15.1 7.1 29.3 19.2 38.4L236.8 313.6c11.4 8.5 27 8.5 38.4 0L492.8 150.4c12.1-9.1 19.2-23.3 19.2-38.4c0-26.5-21.5-48-48-48L48 64zM0 176L0 384c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-208L294.4 339.2c-22.8 17.1-54 17.1-76.8 0L0 176z" />
            </svg>

            <a href="mailto:<EMAIL>"><EMAIL></a>
          </h4>
          <h4 className="text-white text-sm font-bold mb-6 flex justify-center items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width={20} height={20} style={{ fill: '#fff' }}>
              <path d="M215.7 499.2C267 435 384 279.4 384 192C384 86 298 0 192 0S0 86 0 192c0 87.4 117 243 168.3 307.2c12.3 15.3 35.1 15.3 47.4 0zM192 128a64 64 0 1 1 0 128 64 64 0 1 1 0-128z" />
            </svg>
            <a className="underline cursor-pointer" href="https://www.google.com/maps/place//data=!4m2!3m1!1s0x48670c1a744f305b:0x4a15d0df7286346e?sa=X&ved=1t:8290&ictx=111" target="_blank" rel="nofollow noopener noreferrer">
              The National Show Centre, Stockhole Lane, Cloghran, Dublin, K67 VF43
            </a>
          </h4>
          <a
            href="https://www.google.com/maps/place//data=!4m2!3m1!1s0x48670c1a744f305b:0x4a15d0df7286346e?sa=X&ved=1t:8290&ictx=111"
            target="_blank"
            rel="nofollow noopener noreferrer"
          >
            <Suspense fallback={<div className="w-full h-[300px] bg-gray-900 flex items-center justify-center text-white">Loading map...</div>}>
              {LocationMapComponent ? <LocationMapComponent /> : null}
            </Suspense>
          </a>
        </div>
      </div>
    </footer>
  )
}
export default Location;
