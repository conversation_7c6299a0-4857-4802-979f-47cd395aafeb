import React, { useState, useEffect } from "react";
//import Snowfall from "react-snowfall";
import Hero from "@/app/components/Home/Hero";
import Sponsors from "@/app/components/Home/Sponsors";
import Traders from "@/app/components/Home/Traders";
import Welcome from "@/app/components/Home/Welcome";
import Celebs from "@/app/components/Home/Celebs";
import Location from "@/app/components/Home/Location";
import WhatsOn from "@/app/components/Home/WhatsOn";
import Newsletter from "@/app/components/Newsletter";
//import CurtainModal from "@/components/ui/curtain-modal";
import { useLocation } from '@remix-run/react';

const HomeClientContent = () => {
  const [showCurtain, setShowCurtain] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const location = useLocation();

  // Handle curtain animation complete
  /* const handleCurtainComplete = () => {
    // Save the current time to localStorage
    localStorage.setItem('curtainLastShown', Date.now().toString());

    setShowCurtain(false);
    setContentVisible(true);
  }; */

  /* useEffect(() => {
    // Only show curtain on home page
    if (location.pathname === '/') { // Use location.pathname for Remix
      // Check if we should show the curtain (only on client-side)
      const shouldShowCurtain = () => {
        // Get the last time the curtain was shown
        const lastShown = localStorage.getItem('curtainLastShown');

        if (!lastShown) {
          // First visit, show the curtain
          return true;
        }

        const lastShownTime = parseInt(lastShown, 10);
        const currentTime = Date.now();

        // Check if 15 minutes (900000 ms) have passed
        return currentTime - lastShownTime > 900000;
      };

      const showCurtainValue = shouldShowCurtain();
      setShowCurtain(showCurtainValue);

      // If we're not showing the curtain, make content visible immediately
      if (!showCurtainValue) {
        setContentVisible(true);
      }
    } else {
    // Not on home page, don't show curtain and make content visible
    setShowCurtain(false);
    setContentVisible(true);
    }
  }, [location.pathname]); */

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* showCurtain && <CurtainModal onComplete={handleCurtainComplete} /> */}

      <div className="fixed inset-0 z-50 pointer-events-none">
        {/* <Snowfall
              snowflakeCount={200}
              color="#fff"
            /> */}
      </div>

      {/* <div style={{ opacity: contentVisible ? 1 : 0, transition: 'opacity 1s ease-in-out' }}> */}
      <div>
        <Hero />
        <Welcome />
        <Sponsors />
        <div className="bg-[#1c1c1c]">
          <WhatsOn />
        </div>
        <Traders />
        <div className="bg-[#1c1c1c]">
          <Celebs />
        </div>
        <Newsletter />
        <div className="bg-[#1c1c1c]">
          <Location />
        </div>
      </div>
    </div>
  );
};
export default HomeClientContent;