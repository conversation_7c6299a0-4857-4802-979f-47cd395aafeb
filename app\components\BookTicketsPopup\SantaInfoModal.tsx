"use client";

import React, { useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';

interface SantaInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SantaInfoModal: React.FC<SantaInfoModalProps> = ({ isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, onClose);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70"
        >
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 max-w-md w-full mx-4"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 id="santa-modal-title" className="text-xl font-semibold text-white">Meet Santa - Information</h3>
            </div>

            <div className="text-white space-y-4 max-h-[300px] overflow-y-auto custom-scrollbar flex-grow">
              <p>
                <span className="text-[var(--darkGold)] font-semibold">Booking Details:</span>
              </p>

              <ul className="list-disc pl-5 space-y-2">
                <li>€16 per slot (includes one child, maximum of 4 children in total per slot)</li>
                <li>€6 per additional child (maximum of 3 additional children per slot)</li>
                <li>Each slot lasts up to 10 minutes</li>
              </ul>

              <p>
                <span className="text-[var(--darkGold)] font-semibold">Availability:</span>
              </p>

              <ul className="list-disc pl-5 space-y-2">
                <li>Saturday: 80 slots available</li>
                <li>Sunday: 80 slots available</li>
                <li>Hours: 9:00 AM to 5:00 PM</li>
              </ul>

              <p>
                <span className="text-[var(--darkGold)] font-semibold">Important Notes:</span>
              </p>

              <ul className="list-disc pl-5 space-y-2">
                <li>Please bring your own camera to capture photos</li>
                <li>We do not provide printing or photo services</li>
                <li>An elf will be available to assist with taking photos</li>
                <li className="text-red-500">Limited availability - book early to secure your slot</li>
              </ul>
            </div>

            <div className="w-full mt-6 text-center">
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg hover:bg-[#95784a] transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default SantaInfoModal;