"use client";

import React, { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
//import { TextShimmer } from "@/components/ui/text-shimmer";
//import { Skeleton } from '@/components/ui/skeleton';

const Banner = () => {
  const [imageLoaded, setImageLoaded] = useState(false);

  const leftTexts = ["Fun Fair", "Live Shows", "Craft & Collectibles"];
  const rightTexts = ["Toy Fair", "Comic Con", "Christmas Market"];

  /**
   * <PERSON><PERSON><PERSON> renders a trail of star elements anchored at the trailing edge
   * of a text line. For left text, the stars are positioned on the left; for right text, on the right.
   */
  /* const Sparkles = ({
    direction,
    delay,
  }: {
    direction: "left" | "right";
    delay: number;
  }) => {
    const sparkleCount = 15;
    return (
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: sparkleCount }).map((_, i) => {
          const randY = Math.random() * 30 - 15; // small random vertical offset
          // Stars animate outward from the edge:
          const finalX = direction === "left" ? -40 : 40;
          // Use a dynamic style to anchor the star at the appropriate side
          const anchorStyle = direction === "left" ? { left: 0 } : { right: 0 };
          return (
            <motion.div
              key={i}
              className="absolute star-five"
              style={{
                top: `${randY}px`,
                ...anchorStyle,
              }}
              initial={{ x: 0, opacity: 0, scale: 0.5 }}
              animate={{
                x: finalX,
                opacity: [0, 1, 0],
                scale: [0.5, 1.5, 0.5],
              }}
              transition={{
                delay: delay + i * 0.08,
                duration: 1.2,
                ease: "easeOut",
              }}
            />
          );
        })}
      </div>
    );
  }; */

  return (
    <section className="relative mb-16 mt-2" id="home-section">
      <div className="relative w-full h-full flex items-center justify-center">
        {/* Left side text */}
        {/* <div className="absolute left-8 md:left-16 flex flex-col items-end justify-center h-full">
          {leftTexts.map((text, index) => {
            const delay = 0.2 * index;
            return (
              <div key={`left-${index}`} className="relative my-6">

                <motion.div
                  className="text-xl md:text-3xl lg:text-3xl font-bold"
                  initial={{ x: -100, opacity: 0, filter: "blur(4px)" }}
                  animate={{
                    x: 0,
                    opacity: 1,
                    filter: "blur(0px)",
                  }}
                  transition={{ delay, duration: 0.5 }}
                >
                  <TextShimmer duration={6.5} spread={2}>{text}</TextShimmer>
                </motion.div>

              </div>
            );
          })}
        </div> */}

        {/* Logo in center */}
        <div className="flex relative justify-center items-center w-auto h-full max-w-[606px] banner-mask mt-4">
          <img
            loading="lazy"
            src="/images/logo-banner.webp"
            alt="Con Before Christmas"
            width={606}
            height={606}
            className={`relative object-contain z-0 w-auto mt-4 transition-opacity duration-300`}
            onLoad={() => setImageLoaded(true)}
          />
        </div>

        {/* Right side text */}
        {/* <div className="absolute right-8 md:right-16 flex flex-col items-start justify-center h-full">
          {rightTexts.map((text, index) => {
            const delay = 0.2 * index;
            return (
              <div key={`right-${index}`} className="relative my-6">

                <motion.div
                  className="text-xl md:text-3xl lg:text-3xl font-bold"
                  initial={{ x: 100, opacity: 0, filter: "blur(4px)" }}
                  animate={{
                    x: 0,
                    opacity: 1,
                    filter: "blur(0px)",
                  }}
                  transition={{ delay, duration: 0.5 }}
                >
                  <TextShimmer duration={6.5} spread={2}>{text}</TextShimmer>
                </motion.div>

              </div>
            );
          })}
        </div> */}
      </div>
    </section>
  );
};
export default Banner;