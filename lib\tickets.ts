import { TicketType } from '@/types/ticket';

interface TicketResponse {
  tickets: TicketType[];
  availability: Record<string, number>;
}

export async function getTickets(): Promise<TicketResponse> {
  try {
    const response = await fetch(`/api/tickets/get-tickets`, {
      cache: 'no-store',
      headers: {
        'Accept': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch tickets');
    }

    const data = await response.json();

    if (!data.tickets || !Array.isArray(data.tickets) || !data.availability) {
      console.error('Invalid ticket data format:', data);
      return { tickets: [], availability: {} };
    }

    return {
      tickets: data.tickets,
      availability: data.availability
    };
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return { tickets: [], availability: {} };
  }
}