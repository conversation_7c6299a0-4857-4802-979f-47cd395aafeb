import { useState, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { AdminLayout } from '@/app/components/admin/layout/AdminLayout';
import { ProtectedRoute } from '@/app/components/admin/ProtectedRoute';
import { fetchVendorStallMetrics } from '@/app/store/slices/adminSlice';
import { elFormatter } from '@/lib/utils';
import { format } from 'date-fns';
import { standPositions } from '@/data/standPositions';

export default function VendorStallsPage() {
  const dispatch = useAppDispatch();
  const { data: metrics, loading, error } = useAppSelector(state => state.admin.vendorStalls);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    dispatch(fetchVendorStallMetrics({ timeRange }));
  }, [dispatch, timeRange]);

  // Calculate stand statistics
  const standStats = useMemo(() => {
    const totalStands = standPositions.length;
    const reservedStands = standPositions.filter(stand => stand.reserved).length;
    const availableStands = totalStands - reservedStands;
    const artistStands = standPositions.filter(stand => stand.isArtist).length;
    const reservedArtistStands = standPositions.filter(stand => stand.isArtist && stand.reserved).length;

    const totalPotentialRevenue = standPositions.reduce((sum, stand) => sum + stand.price, 0);
    const reservedRevenue = standPositions
      .filter(stand => stand.reserved)
      .reduce((sum, stand) => sum + stand.price, 0);

    // Group by table size
    const tableSizes = standPositions.reduce((acc, stand) => {
      if (!acc[stand.tableSize]) {
        acc[stand.tableSize] = { total: 0, reserved: 0 };
      }
      acc[stand.tableSize].total++;
      if (stand.reserved) {
        acc[stand.tableSize].reserved++;
      }
      return acc;
    }, {} as Record<string, { total: number; reserved: number }>);

    return {
      totalStands,
      reservedStands,
      availableStands,
      artistStands,
      reservedArtistStands,
      totalPotentialRevenue,
      reservedRevenue,
      tableSizes
    };
  }, []);

  // Stats Cards
  const stats = [
    {
      name: 'Total Stands',
      value: metrics?.totalStands || 0,
      description: `Booked: ${standStats.reservedStands} / ${standStats.totalStands} (${Math.round((standStats.reservedStands / standStats.totalStands) * 100)}%)`
    },
    {
      name: 'Total Revenue',
      value: `€${(metrics?.totalRevenue || 0).toLocaleString()}`,
      description: `Potential: €${standStats.reservedRevenue.toLocaleString()} of €${standStats.totalPotentialRevenue.toLocaleString()}`
    },
    {
      name: 'Artist Stands',
      value: `${standStats.reservedArtistStands} / ${standStats.artistStands}`,
      description: `Artist stands booked (${Math.round((standStats.reservedArtistStands / standStats.artistStands) * 100)}%)`
    },
    {
      name: 'Addon Revenue',
      value: `€${(metrics?.addonRevenue || 0).toLocaleString()}`,
      description: 'Additional revenue from stand addons'
    },
  ];

  if (loading && !metrics) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <ProtectedRoute requiredRole={['super_admin', 'finance_admin']}>
      <AdminLayout>
        <div className="flex flex-col space-y-4">
          <h1 className="text-2xl font-bold">Vendor Stalls</h1>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div className="text-sm text-gray-400">
              Showing data for: <span className="font-medium">
                {timeRange === 'week' ? 'Last 7 days' : timeRange === 'month' ? 'Last 30 days' : 'Last 12 months'}
              </span>
            </div>
            <div className="flex items-center space-x-4 mb-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as 'week' | 'month' | 'year')}
                className="bg-zinc-700 border border-amber-600/50 text-amber-100 rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="week">Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="year">Last 12 months</option>
              </select>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat: any) => (
            <div key={stat.name} className="bg-zinc-800 p-6 rounded-lg hover:bg-zinc-700 transition-colors">
              <h3 className="text-gray-400 text-sm font-medium">{stat.name}</h3>
              <p className="text-2xl font-bold mb-1">{stat.value}</p>
              <p className="text-xs text-gray-500">{stat.description}</p>
            </div>
          ))}
        </div>

        {/* Stand Availability */}
        <div className="bg-zinc-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Stand Availability</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-750 p-4 rounded-lg space-y-2">
              <div className="text-sm text-gray-400">Total Stands</div>
              <div className="text-2xl font-bold">{standStats.totalStands}</div>
              <div className="text-xs text-gray-500">100% capacity</div>
            </div>
            <div className="bg-gray-750 p-4 rounded-lg space-y-2">
              <div className="text-sm text-gray-400">Reserved</div>
              <div className="text-2xl font-bold text-green-400">{standStats.reservedStands}</div>
              <div className="text-xs text-gray-500">
                {Math.round((standStats.reservedStands / standStats.totalStands) * 100)}% booked
              </div>
            </div>
            <div className="bg-gray-750 p-4 rounded-lg space-y-2">
              <div className="text-sm text-gray-400">Available</div>
              <div className="text-2xl font-bold">{standStats.availableStands}</div>
              <div className="text-xs text-gray-500">
                {Math.round((standStats.availableStands / standStats.totalStands) * 100)}% remaining
              </div>
            </div>
            <div className="bg-gray-750 p-4 rounded-lg space-y-2">
              <div className="text-sm text-gray-400">Artist Stands</div>
              <div className="text-2xl font-bold">{standStats.artistStands}</div>
              <div className="text-xs text-gray-500">
                {standStats.reservedArtistStands} reserved
              </div>
            </div>
          </div>

          <h3 className="text-lg font-medium mb-3">Stand Types</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(standStats.tableSizes).map(([size, { total, reserved }]) => (
              <div key={size} className="bg-gray-750 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{size}</span>
                  <span className="text-sm text-gray-400">
                    {reserved} / {total}
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2.5">
                  <div
                    className="bg-blue-500 h-2.5 rounded-full"
                    style={{ width: `${(reserved / total) * 100}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>€{standPositions.find(s => s.tableSize === size)?.price.toLocaleString()}</span>
                  <span>{Math.round((reserved / total) * 100)}% booked</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Businesses */}
        <div className="bg-zinc-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Top Businesses</h2>
          {!metrics?.topBusinesses?.length ? (
            <div className="bg-gray-750 rounded-lg p-8 text-center">
              <div className="text-gray-400 mb-2">No vendor data found</div>
              <p className="text-sm text-gray-500">No businesses have booked stands for the selected time frame.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-gray-400 text-sm">
                    <th className="pb-2">Business</th>
                    <th className="pb-2">Contact</th>
                    <th className="pb-2 text-right">Stands Booked</th>
                    <th className="pb-2 text-right">Total Spent</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {metrics.topBusinesses.map((business, index) => (
                    <tr key={business.email} className={index < 3 ? 'bg-gray-750' : ''}>
                      <td className="py-3">
                        <div className="font-medium">{business.businessName}</div>
                        <div className="text-sm text-gray-400">{business.email}</div>
                      </td>
                      <td className="py-3">
                        {business.contactName}
                      </td>
                      <td className="py-3 text-right">
                        {business.standsBooked}
                      </td>
                      <td className="py-3 text-right font-medium">
                        €{elFormatter(business.totalSpent, 2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Recent Sales */}
        <div className="bg-zinc-800 rounded-lg p-6 mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Recent Stand Bookings</h2>
            <div className="text-sm text-gray-400">
              Showing {Math.min(10, metrics?.recentSales?.length || 0)} most recent
            </div>
          </div>
          {!metrics?.recentSales?.length ? (
            <div className="bg-gray-750 rounded-lg p-8 text-center">
              <div className="text-gray-400 mb-2">No recent bookings found</div>
              <p className="text-sm text-gray-500">No stand bookings were made during the selected time frame.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="text-left text-gray-400 text-sm">
                    <th className="pb-2">Business</th>
                    <th className="pb-2">Stand #</th>
                    <th className="pb-2">Amount</th>
                    <th className="pb-2">Date</th>
                    <th className="pb-2">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {metrics.recentSales.map((sale) => (
                    <tr key={sale.id}>
                      <td className="py-3">
                        <div className="font-medium">{sale.business_name}</div>
                        <div className="text-sm text-gray-400">{sale.contact_name}</div>
                      </td>
                      <td className="py-3">
                        <span className="font-mono">#{sale.stand_number}</span>
                      </td>
                      <td className="py-3 font-medium">
                        €{sale.price.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                        {sale.discount_applied && (
                          <div className="text-xs text-green-400">
                            {sale.discount_percentage}% off
                          </div>
                        )}
                      </td>
                      <td className="py-3 text-sm text-gray-400">
                        {format(new Date(sale.created_at), 'MMM d, yyyy')}
                      </td>
                      <td className="py-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${(sale.payment_status || 'pending') === 'paid' ? 'bg-green-100 text-green-800' :
                            (sale.payment_status || 'pending') === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                          }`}>
                          {sale.payment_status || 'pending'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute >
  );
};