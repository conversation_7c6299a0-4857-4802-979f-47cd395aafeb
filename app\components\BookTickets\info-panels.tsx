import { AnimatePresence, motion } from 'framer-motion';

interface InfoPanelsProps {
  isFamilyInfoOpen: boolean;
  isSantaInfoOpen: boolean;
  isCharityInfoOpen: boolean;
  closeFamilyInfo: () => void;
  closeSantaInfo: () => void;
  closeCharityInfo: () => void;
}

export function InfoPanels({
  isFamilyInfoOpen,
  isSantaInfoOpen,
  isCharityInfoOpen,
  closeFamilyInfo,
  closeSantaInfo,
  closeCharityInfo,
}: InfoPanelsProps) {

  return (
    <AnimatePresence>
      {isFamilyInfoOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="overflow-hidden"
        >
          <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">Family Pass Information</h3>
              <button onClick={closeFamilyInfo} className="text-gray-400 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="text-white space-y-4">
              <p>The Family Pass is designed for families and includes admission for:</p>
              <ul className="list-disc pl-5 space-y-2">
                <li>2 Adults (18 years and older)</li>
                <li>Up to 3 Children (12 years and under)</li>
              </ul>
              <p>This pass offers significant savings compared to purchasing individual tickets.</p>
              <p>
                <span className="text-[var(--darkGold)] font-semibold">Important:</span> All family members must enter the event together. ID may be required to verify eligibility.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {isSantaInfoOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="overflow-hidden"
        >
          <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">Meet Santa - Information</h3>
              <button onClick={closeSantaInfo} className="text-gray-400 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="text-white space-y-4">
              <p><span className="text-[var(--darkGold)] font-semibold">Booking Details:</span></p>
              <ul className="list-disc pl-5 space-y-2">
                <li>€16 per slot (includes one child, maximum of 4 children in total per slot)</li>
                <li>€6 per additional child (maximum of 3 additional children per slot)</li>
                <li>Each slot lasts up to 10 minutes</li>
              </ul>
              <p><span className="text-[var(--darkGold)] font-semibold">Availability:</span></p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Saturday: 80 slots available</li>
                <li>Sunday: 80 slots available</li>
                <li>Hours: 9:00 AM to 5:00 PM</li>
              </ul>
              <p><span className="text-[var(--darkGold)] font-semibold">Important Notes:</span></p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Please bring your own camera to capture photos</li>
                <li>We do not provide printing or photo services</li>
                <li>An elf will be available to assist with taking photos</li>
                <li className="text-red-500">Limited availability - book early to secure your slot</li>
              </ul>
            </div>
          </div>
        </motion.div>
      )}

      {isCharityInfoOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="overflow-hidden"
        >
          <div className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">Support LauraLynn Children&apos;s Hospice</h3>
              <button onClick={closeCharityInfo} className="text-gray-400 hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="text-white space-y-4">
              <p>
                LauraLynn is Ireland&apos;s only children&apos;s hospice providing specialized care to children with life-limiting conditions and support to their families.
              </p>
              <p>
                We&apos;re aiming to raise <span className="text-[var(--darkGold)] font-semibold">€70,000</span> to help LauraLynn continue their vital work. Your donation with each ticket purchase makes a real difference.
              </p>
              <p><span className="text-[var(--darkGold)] font-semibold">How Your Donation Helps:</span></p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Provides specialized care for children with life-limiting conditions</li>
                <li>Supports families through difficult times</li>
                <li>Helps fund essential medical equipment and services</li>
                <li>Contributes to making precious memories for families</li>
              </ul>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};