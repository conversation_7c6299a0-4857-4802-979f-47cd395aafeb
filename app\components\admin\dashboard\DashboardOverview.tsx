import { useEffect, useCallback, useState } from 'react';
import {
  Users,
  CreditCard,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  AlertTriangle,
  RefreshCw,
  Info,
  AlertCircle,
  CheckCircle2,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/app/store/hooks';
import { fetchDashboardMetrics } from '@/app/store/slices/adminSlice';
import { fetchRecentAuditLogs } from '@/app/store/slices/auditLogsSlice';
import { selectConnectionStatus, selectIsConnected } from '@/app/store/slices/realtimeSlice';
import { useDashboardRefresh } from '@/app/hooks/useRealtimeRefresh';
import { RealtimeConnectionStatus } from '@/app/components/admin/RealtimeConnectionStatus';
import { useAnalytics } from '@/app/hooks/useAnalytics';
import { useNavigate } from 'react-router-dom';

export function DashboardOverview() {
  const dispatch = useAppDispatch();
  const { data: metrics, loading, error } = useAppSelector(state => state.admin.metrics);
  const { logs: auditLogs, loading: logsLoading } = useAppSelector(state => state.auditLogs);
  const connectionStatus = useAppSelector(selectConnectionStatus);
  const isConnected = useAppSelector(selectIsConnected);
  const { track } = useAnalytics();
  const navigate = useNavigate();

  // Track when metrics are updated for visual feedback
  const [lastMetricsUpdate, setLastMetricsUpdate] = useState<Date | null>(null);
  const [recentlyUpdated, setRecentlyUpdated] = useState(false);

  // Track dashboard data loading
  const loadDashboardData = useCallback(async () => {
    if (typeof window === 'undefined' || loading) return;

    try {
      track('dashboard_data_loading', {
        timestamp: new Date().toISOString(),
        status: 'started'
      });

      const resultAction = await dispatch(fetchDashboardMetrics({}));

      if (fetchDashboardMetrics.fulfilled.match(resultAction)) {
        track('dashboard_data_loaded', {
          timestamp: new Date().toISOString(),
          status: 'success'
        });
      } else if (fetchDashboardMetrics.rejected.match(resultAction)) {
        track('dashboard_data_error', {
          timestamp: new Date().toISOString(),
          status: 'error',
          error: resultAction.error?.message || 'Unknown error'
        });
      }
    } catch (err) {
      track('dashboard_data_error', {
        timestamp: new Date().toISOString(),
        status: 'exception',
        error: err instanceof Error ? err.message : 'Unknown error'
      });
    }
  }, []);

  // Load audit logs
  const loadAuditLogs = useCallback(() => {
    dispatch(fetchRecentAuditLogs({
      limit: 5,
      severity: ['error', 'warning'] // Only show errors and warnings in dashboard
    }));
  }, [dispatch]);

  // Track metrics changes for real-time visual feedback
  useEffect(() => {
    if (metrics && isConnected) {
      const now = new Date();
      if (lastMetricsUpdate && now.getTime() - lastMetricsUpdate.getTime() < 10000) {
        // Metrics updated within last 10 seconds, show visual feedback
        setRecentlyUpdated(true);
        const timer = setTimeout(() => setRecentlyUpdated(false), 3000);
        return () => clearTimeout(timer);
      }
      setLastMetricsUpdate(now);
    }
  }, [metrics, isConnected, lastMetricsUpdate]);

  // Set up real-time refresh for dashboard data
  useDashboardRefresh(() => {
    setLastMetricsUpdate(new Date());
    setRecentlyUpdated(true);

    // Refresh dashboard metrics
    loadDashboardData();

    // Clear the "recently updated" indicator after 3 seconds
    setTimeout(() => setRecentlyUpdated(false), 3000);
  });

  // Initial data load
  useEffect(() => {
    loadDashboardData();
    loadAuditLogs();

    // Set up refresh interval (e.g., every 5 minutes)
    const intervalId = setInterval(() => {
      loadDashboardData();
      loadAuditLogs();
    }, 5 * 60 * 1000);

    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [loadDashboardData, loadAuditLogs]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="w-12 h-12 text-red-600 dark:text-red-400 animate-spin mx-auto" />
          <p className="mt-4 text-gray-700 dark:text-gray-300">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Error Loading Dashboard</h3>
        <p className="text-red-700 dark:text-red-400">{error}</p>
        <button
          onClick={() => {
            track('dashboard_retry_click', {
              timestamp: new Date().toISOString(),
              error: error || 'Unknown error'
            });
            loadDashboardData();
          }}
          className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-300 mb-2">No Data Available</h3>
        <p className="text-yellow-700 dark:text-yellow-400">Dashboard metrics are not available.</p>
        <button
          onClick={() => {
            track('dashboard_refresh_click', {
              timestamp: new Date().toISOString()
            });
            loadDashboardData();
          }}
          className="mt-4 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-md transition-colors"
        >
          Refresh Data
        </button>
      </div>
    );
  }

  // Format currency with Euro symbol
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IE', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Calculate percentage change
  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? '100%' : '0%';
    const change = ((current - previous) / previous) * 100;
    return `${change >= 0 ? '+' : ''}${change.toFixed(1)}%`;
  };

  const statCards = [
    {
      title: 'Total Revenue',
      value: formatCurrency(metrics.totalRevenue),
      icon: <DollarSign className="w-8 h-8 text-green-500" />,
      change: metrics.revenueByDay?.length >= 2
        ? calculateChange(
          metrics.revenueByDay[metrics.revenueByDay.length - 1]?.revenue || 0,
          metrics.revenueByDay[metrics.revenueByDay.length - 2]?.revenue || 0
        )
        : 'N/A',
      trend: 'up',
      description: 'vs. previous period'
    },
    {
      title: 'Tickets Sold',
      value: metrics.ticketsSold.toLocaleString(),
      icon: <ShoppingCart className="w-8 h-8 text-blue-500" />,
      change: metrics.ticketsByType?.length >= 2
        ? calculateChange(
          metrics.ticketsByType.reduce((sum: number, t: any) => sum + t.count, 0),
          metrics.ticketsByType.reduce((sum: number, t: any) => sum + (t.count || 0), 0) * 0.9 // Simulate previous period
        )
        : 'N/A',
      trend: 'up',
      description: 'vs. previous period'
    },
    {
      title: 'Active Users',
      value: metrics.activeUsers.toLocaleString(),
      icon: <Users className="w-8 h-8 text-purple-500" />,
      change: metrics.activeUsers > 0
        ? calculateChange(metrics.activeUsers, Math.floor(metrics.activeUsers * 0.9)) // Simulate previous period
        : 'N/A',
      trend: 'up',
      description: 'active in last 30 min'
    },
    {
      title: 'Conversion Rate',
      value: `${metrics.conversionRate.toFixed(1)}%`,
      icon: <TrendingUp className="w-8 h-8 text-orange-500" />,
      change: metrics.conversionRate > 0
        ? calculateChange(metrics.conversionRate, metrics.conversionRate * 0.95) // Simulate previous period
        : 'N/A',
      trend: 'up',
      description: 'sessions with purchases'
    }
  ];

  const handleViewAll = (section: string) => {
    track('dashboard_view_all', {
      section: section,
      timestamp: new Date().toISOString()
    });
    navigate(`/b7a6791b18efd0f58ca1fb26a0ef58dc/${section}`);
  };

  // Format date to relative time (e.g., "2 hours ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return date.toLocaleDateString();
  };

  // Get status color and icon based on transaction status
  const getStatusStyles = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return {
          bg: 'bg-green-900',
          icon: <CreditCard className="w-5 h-5 text-green-400" />
        };
      case 'pending':
        return {
          bg: 'bg-yellow-900',
          icon: <CreditCard className="w-5 h-5 text-yellow-400" />
        };
      case 'failed':
        return {
          bg: 'bg-red-900',
          icon: <CreditCard className="w-5 h-5 text-red-400" />
        };
      default:
        return {
          bg: 'bg-blue-900',
          icon: <CreditCard className="w-5 h-5 text-blue-400" />
        };
    }
  };

  // Get alert styles based on severity
  const getAlertStyles = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'error':
        return {
          bg: 'bg-red-900/20',
          border: 'border-red-800',
          icon: <AlertCircle className="w-5 h-5 text-red-500" />,
          iconBg: 'bg-red-900'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-900/20',
          border: 'border-yellow-800',
          icon: <AlertTriangle className="w-5 h-5 text-yellow-500" />,
          iconBg: 'bg-yellow-900'
        };
      case 'success':
        return {
          bg: 'bg-green-900/20',
          border: 'border-green-800',
          icon: <CheckCircle2 className="w-5 h-5 text-green-500" />,
          iconBg: 'bg-green-900'
        };
      default: // info
        return {
          bg: 'bg-blue-900/20',
          border: 'border-blue-800',
          icon: <Info className="w-5 h-5 text-blue-500" />,
          iconBg: 'bg-blue-900'
        };
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">Dashboard</h1>
        <div className="flex items-center space-x-4">
          {/* Real-time connection status */}
          <RealtimeConnectionStatus size="sm" variant="badge" />

          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">Last updated:</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {new Date().toLocaleString()}
            </span>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map((card, index) => (
          <div
            key={index}
            className={`bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${
              recentlyUpdated && isConnected ? 'ring-2 ring-green-400 ring-opacity-50 animate-pulse' : ''
            }`}
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{card.title}</p>
                <h3 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-1">{card.value}</h3>
              </div>
              <div className="p-2 rounded-full bg-zinc-100 dark:bg-zinc-700">
                {card.icon}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className={`text-sm font-medium ${card.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                {card.change}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                {card.description}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Transactions */}
      <div className="w-full">
        <div className="bg-zinc-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-100">Recent Transactions</h2>
            <button
              onClick={() => handleViewAll('transactions')}
              className="text-sm text-blue-400 hover:underline"
            >
              View All
            </button>
          </div>
          <div className="space-y-4">
            {metrics.recentTransactions?.length > 0 ? (
              metrics.recentTransactions.slice(0, 5).map((transaction: any, index: any) => {
                const statusStyles = getStatusStyles(transaction.status);
                const mainItem = transaction.items?.[0];
                const additionalItems = transaction.items?.length > 1 ? transaction.items.length - 1 : 0;

                return (
                  <div
                    key={transaction.id || index}
                    className="flex items-center justify-between p-3 bg-zinc-700 rounded-md hover:bg-zinc-600 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-full ${statusStyles.bg}`}>
                        {statusStyles.icon}
                      </div>
                      <div>
                        <p className="font-medium text-gray-100">
                          {mainItem?.name || 'Ticket Purchase'}
                          {additionalItems > 0 && ` +${additionalItems} more`}
                        </p>
                        <p className="text-sm text-gray-400">
                          {transaction.customerName || 'Guest'}
                          {transaction.email ? ` • ${transaction.email}` : ''}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-100">
                        {formatCurrency(transaction.amount)}
                      </p>
                      <p className="text-sm text-gray-400">
                        {formatRelativeTime(transaction.timestamp)}
                      </p>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-6">
                <p className="text-gray-400">No recent transactions found</p>
                <button
                  onClick={() => loadDashboardData()}
                  className="mt-2 text-sm text-blue-400 hover:underline"
                >
                  Refresh
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Alerts */}
      <div className="bg-zinc-800 rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-100">System Alerts</h2>
          <button
            onClick={() => handleViewAll('alerts')}
            className="text-sm text-blue-400 hover:underline"
          >
            View All Alerts
          </button>
        </div>
        <div className="space-y-4">
          {logsLoading ? (
            <div className="flex items-center justify-center p-4">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin mr-2" />
              <span className="text-gray-400">Loading alerts...</span>
            </div>
          ) : auditLogs.length > 0 ? (
            auditLogs.slice(0, 5).map((log) => {
              const styles = getAlertStyles(log.severity || 'info');
              const details = typeof log.details === 'object'
                ? log.details.message || JSON.stringify(log.details, null, 2)
                : log.details;

              return (
                <div
                  key={log.id}
                  className={`flex items-start p-3 rounded-md border ${styles.border} ${styles.bg}`}
                >
                  <div className={`p-2 rounded-full ${styles.iconBg} mt-1 mr-3`}>
                    {styles.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-100 truncate">
                        {log.action}
                      </h3>
                      <span className="text-xs text-gray-400 ml-2 whitespace-nowrap">
                        {formatRelativeTime(log.created_at)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-400 mt-1 break-words">
                      {details}
                    </p>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-400">No recent alerts</p>
              <p className="text-sm text-gray-500 mt-1">The system is running smoothly</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};