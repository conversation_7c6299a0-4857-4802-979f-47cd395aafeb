"use client";

import React, { useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOutsideClick } from '@/hooks/useOutsideClick';

interface FamilyPassInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FamilyPassInfoModal: React.FC<FamilyPassInfoModalProps> = ({ isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useOutsideClick(modalRef as React.RefObject<HTMLDivElement>, onClose);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70"
          aria-modal="true"
          role="dialog"
          aria-labelledby="family-modal-title"
        >
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-[#1e1e1e] border border-[var(--darkGold)] rounded-lg p-6 max-w-md w-full mx-4"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 id="family-modal-title" className="text-xl font-semibold text-white">Family Pass Information</h3>
            </div>

            <div className="text-white space-y-4">
              <p>
                The Family Pass is designed for families and includes admission for:
              </p>

              <ul className="list-disc pl-5 space-y-2">
                <li>2 Adults (18 years and older)</li>
                <li>Up to 3 Children (12 years and under)</li>
              </ul>

              <p>
                This pass offers significant savings compared to purchasing individual tickets.
              </p>

              <p>
                <span className="text-[var(--darkGold)] font-semibold">Important:</span> All family members must enter the event together. ID may be required to verify eligibility.
              </p>
            </div>

            <div className="w-full mt-6 text-center">
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-[var(--darkGold)] text-white rounded-lg"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default FamilyPassInfoModal;